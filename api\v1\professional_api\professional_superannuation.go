package professional_api

import (
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type ProfessionalSuperannuationController struct{}

func NewProfessionalSuperannuationController() ProfessionalSuperannuationController {
	return ProfessionalSuperannuationController{}
}

// @Tags Superannuation
// @Summary 编辑退休金信息
// @Description
// @Router /v1/professional/superannuation/actions/edit [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalSuperannuationEditReq true "parameter"
// @Success 200 "Success"
func (con ProfessionalSuperannuationController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	userId := nc.GetJWTUserId()
	var err error
	var req services.ProfessionalSuperannuationEditReq
	var draftReq services.ProfessionalSuperannuationEditDraftReq

	// 先嘗試以草稿模式綁定，檢查是否為草稿模式
	if err = c.ShouldBindBodyWith(&draftReq, binding.JSON); err != nil {
		nc.BadRequestResponse(err)
		return
	}
	if draftReq.DataType == model.ProfessionalSuperannuationDataTypeDraft {
		_ = copier.Copy(&req, draftReq)
	} else if err = c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		nc.BadRequestResponse(err)
		return
	}
	db := xgorm.DB.WithContext(c)

	checker := xapp.NewCK(c)
	checker.
		Run(func() (bool, i18n.Message, error) {
			return services.ProfessionalSuperannuationService.CheckIdExist(db, &model.ProfessionalSuperannuation{}, req.ProfessionalSuperannuationId)
		}).
		Run(func() (bool, i18n.Message, error) {
			return services.ProfessionalSuperannuationService.CheckDeclarationConfirmed(req)
		})

	if nc.BadRequestResponseIfCheckerFailed(checker, req) {
		return
	}

	err = services.ProfessionalSuperannuationService.Edit(db, req, userId)
	if err != nil {
		if xgorm.IsNotFoundErr(err) {
			nc.BadRequestResponse(err)
			return
		}
		nc.ErrorResponse(req, err)
		return
	}
	nc.OKResponse(nil)
}
