package routers

import (
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func agreementSystemRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/system").Use(handlers...)
	{
		con := system_api.NewSystemAgreementController()
		r.GET("/agreements", con.List)
		r.GET("/agreements/actions/search", con.Search)
		r.GET("/agreements/actions/inquire", con.Inquire)
		r.POST("/agreements/actions/create", con.Create)
		r.POST("/agreements/actions/edit", con.Edit)
		r.POST("/agreements/actions/delete", con.Delete)
	}
}
