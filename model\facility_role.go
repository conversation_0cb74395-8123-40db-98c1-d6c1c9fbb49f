package model

import "github.com/Norray/xrocket/xmodel"

// 機構角色
type FacilityRole struct {
	Id         uint64 `json:"id" gorm:"primary_key"`
	FacilityId uint64 `json:"facilityId" gorm:"index:facility_idx;not null"` // 機構ID
	RoleId     uint64 `json:"roleId" gorm:"index:role_idx;not null"`         // 角色ID
	xmodel.Model
}

func (FacilityRole) TableName() string {
	return "facility_role"
}

func (FacilityRole) SwaggerDescription() string {
	return "機構角色"
}
