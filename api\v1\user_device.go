package v1

import (
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xi18n"
	"github.com/gin-gonic/gin"
)

type UserDeviceController struct{}

func NewUserDeviceController() UserDeviceController {
	return UserDeviceController{}
}

// @Tags User Device
// @Summary 申請設備key，將會發送驗證碼到電郵
// @Description
// @Router /v1/user-devices/actions/apply [POST]
// @Produce  json
// @Param json body services.UserDevicesApplyReq true "parameter"
// @Success 200 {object} services.UserDevicesApplyResp "Success"
func (con UserDeviceController) UserDevicesApply(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.UserDevicesApplyReq
	if e := c.ShouldBindJSON(&req); e == nil {
		db := xgorm.DB.WithContext(c)
		resp, msg, err := services.UserDeviceService.UserDeviceApply(db, req, nc.GetLanguage())
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if msg != nil {
			nc.SingleAlertResponse(xi18n.Localize(c.Request, msg))
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(e)
	}
}

// @Tags User Device
// @Summary 核實驗證碼，發放設備key
// @Description
// @Router /v1/user-devices/actions/verify [POST]
// @Produce  json
// @Param json body services.UserDevicesVerifyReq true "parameter"
// @Success 200 {object} services.UserDevicesVerifyResp "Success"
func (con UserDeviceController) UserDevicesVerify(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.UserDevicesVerifyReq
	if e := c.ShouldBindJSON(&req); e == nil {
		db := xgorm.DB.WithContext(c)
		resp, msg, err := services.UserDeviceService.UserDeviceVerify(c, db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if msg != nil {
			nc.SingleAlertResponse(xi18n.Localize(c.Request, msg))
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(e)
	}
}

// @Tags User Device
// @Summary 用戶登出設備
// @Router /v1/user-devices/actions/revoke [post]
// @Produce  json
// @Security ApiKeyAuth
// @Success 200 "Success"
func (con UserDeviceController) UserDeviceRevoke(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}

	dk := nc.GetJWTDeviceKey()
	userId := nc.GetJWTUserId()

	db := xgorm.DB.WithContext(c)
	if err := services.UserDeviceService.UserDeviceRevoke(db, userId, dk); err != nil {
		nc.ErrorResponse(nil, err)
		return
	}

	nc.OKResponse(nil)
}
