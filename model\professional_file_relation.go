package model

import (
	"github.com/Norray/xrocket/xmodel"
)

// 專業人士文件關聯
type ProfessionalFileRelation struct {
	Id                 uint64 `json:"id" gorm:"primary_key"`
	UserId             uint64 `json:"userId" gorm:"index:user_idx;not null"`                          // 用戶Id
	ProfessionalId     uint64 `json:"professionalId" gorm:"index:professional_idx;not null"`          // 專業人士Id
	ProfessionalFileId uint64 `json:"professionalFileId" gorm:"index:professional_file_idx;not null"` // 專業人士文件Id
	xmodel.Model
}

func (ProfessionalFileRelation) TableName() string {
	return "professional_file_relation"
}

func (ProfessionalFileRelation) SwaggerDescription() string {
	return "專業人士文件關聯"
}
