package services

import (
	"context"
	"github.com/Norray/xrocket/xconfig"
	"github.com/shopspring/decimal"
	"testing"
)

func TestGoogleTimezone(t *testing.T) {
	xconfig.Setup("../config/app.ini")
	timezone, err := GoogleMapsService.GetTimezone(context.Background(), TimezoneRequest{
		Latitude:  decimal.NewFromFloat(-37.98051130),
		Longitude: decimal.NewFromFloat(145.27552580),
	})
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	//tz, err := time.LoadLocation(timezone.Timezone)
	//t.Logf("Timezone: %s", timezone.Timezone)
	timezoneOffset, err := ServiceLocationService.GetTimeZoneOffset(timezone.Timezone)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(timezone)
	t.Log(timezoneOffset)
}
