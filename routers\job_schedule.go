package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/gin-gonic/gin"
)

func jobScheduleFacilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		controller := facility_api.NewJobScheduleController()
		r.GET("/job-schedules", controller.List)                                                // 查詢工作排程列表
		r.GET("/job-schedules/actions/inquire", controller.Inquire)                             // 查詢工作排程詳情
		r.POST("/job-schedules/actions/create", controller.Create)                              // 創建工作排程
		r.POST("/job-schedules/actions/edit", controller.Edit)                                  // 編輯工作排程
		r.POST("/job-schedules/actions/update-status", controller.UpdateStatus)                 // 更新工作排程狀態
		r.POST("/job-schedules/actions/delete", controller.Delete)                              // 刪除工作排程
		r.POST("/job-schedules/actions/calc-potential-count", controller.CalcPotentialJobCount) // 計算潛在工作數量
	}
}
