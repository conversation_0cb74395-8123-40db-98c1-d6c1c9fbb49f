package model

import (
	"github.com/Norray/xrocket/xmodel"
)

// DocumentFileRelation 單據相關文件關係
type DocumentFileRelation struct {
	Id             uint64 `json:"id" gorm:"primary_key"`
	DocumentId     uint64 `json:"documentId" gorm:"index:documentId_idx;not null"`
	DocumentFileId uint64 `json:"documentFileId" gorm:"index:documentFileId_idx;not null"`
	Seq            int32  `json:"seq" gorm:"not null"` // 序號
	xmodel.Model
}

func (DocumentFileRelation) TableName() string {
	return "document_file_relation"
}

func (DocumentFileRelation) SwaggerDescription() string {
	return "單據相關文件關係"
}
