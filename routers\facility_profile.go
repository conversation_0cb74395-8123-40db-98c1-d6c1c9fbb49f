package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func facilityProfileFacilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		r.POST("/facility-profiles/actions/init", facility_api.NewFacilityProfileController().Init)
		r.GET("/facility-profiles/actions/inquire", facility_api.NewFacilityProfileController().Inquire)
		r.POST("/facility-profiles/actions/edit", facility_api.NewFacilityProfileController().Edit)
		r.POST("/facility-profiles/actions/submit", facility_api.NewFacilityProfileController().Submit)
	}
}

func facilityProfileSystemRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/system").Use(handlers...)
	{
		r.GET("/facility-profiles", system_api.NewFacilityProfileController().List)
		r.GET("/facility-profiles/actions/inquire", system_api.NewFacilityProfileController().Inquire)
		r.POST("/facility-profiles/actions/approve", system_api.NewFacilityProfileController().Approve)
		r.POST("/facility-profiles/actions/deactivate", system_api.NewFacilityProfileController().Deactivate)
	}
}
