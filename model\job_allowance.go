package model

import (
	"github.com/Norray/xrocket/xmodel"
	"github.com/shopspring/decimal"
)

const (
	JobAllowanceAllowanceTypeHourly = "HOURLY" // 小時津貼
	JobAllowanceAllowanceTypeShift  = "SHIFT"  // 班次津貼
	JobAllowanceAllowanceTypeJob    = "JOB"    // 職位津貼
)

// 職位津貼
type JobAllowance struct {
	Id                     uint64          `json:"id" gorm:"primary_key"`                                  // 主鍵
	FacilityId             uint64          `json:"facilityId" gorm:"index:facility_idx;not null"`          // 機構Id
	JobId                  uint64          `json:"jobId" gorm:"index:job_idx;not null"`                    // 工作Id
	AllowanceId            uint64          `json:"allowanceId" gorm:"index:allowance_idx;not null"`        // 津貼Id
	AllowanceName          string          `json:"allowanceName" gorm:"type:varchar(100);not null"`        // 津貼名稱
	AllowanceType          string          `json:"allowanceType" gorm:"type:varchar(20);not null"`         // 津貼類型 HOURLY, SHIFT, JOB
	AttractsSuperannuation string          `json:"attractsSuperannuation" gorm:"type:varchar(1);not null"` // 納入退休金 Y N
	BaseAmount             decimal.Decimal `json:"baseAmount" gorm:"type:decimal(10,2);not null"`          // 基礎津貼金額
	xmodel.Model
}

func (JobAllowance) TableName() string {
	return "job_allowance"
}

func (JobAllowance) SwaggerDescription() string {
	return "職位津貼"
}

// 津貼明細（用於動態計算結果）
type AllowanceDetail struct {
	AllowanceId            uint64          `json:"allowanceId"`            // 津貼Id
	AllowanceName          string          `json:"allowanceName"`          // 津貼名稱
	AllowanceType          string          `json:"allowanceType"`          // 津貼類型
	BaseAmount             decimal.Decimal `json:"baseAmount"`             // 基礎金額
	Amount                 decimal.Decimal `json:"amount"`                 // 計算後金額
	AttractsSuperannuation string          `json:"attractsSuperannuation"` // 是否納入退休金
	SuperAmount            decimal.Decimal `json:"superAmount"`            // 退休金金額
}
