package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/Norray/medic-crew/api/v1/professional_api"
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func faqFileSystemRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/system").Use(handlers...)
	{
		r.GET("/faq-files/actions/preview", system_api.NewFaqFileController().Preview)
		r.POST("/faq-files/actions/upload", system_api.NewFaqFileController().Upload)
	}
}

func faqFileFacilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		r.GET("/faq-files/actions/preview", facility_api.NewFaqFileController().Preview)
	}
}

func faqFileProfessionalRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/professional").Use(handlers...)
	{
		r.GET("/faq-files/actions/preview", professional_api.NewFaqFileController().Preview)
	}
}
