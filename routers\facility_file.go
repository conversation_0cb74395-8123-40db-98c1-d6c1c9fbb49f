package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func facilityFileFacilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		r.POST("/facility-files/actions/upload", facility_api.NewFacilityFileController().Upload)
		r.GET("/facility-files/actions/preview", facility_api.NewFacilityFileController().Preview)
	}
}

func facilityFileSystemRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/system").Use(handlers...)
	{
		r.GET("/facility-files/actions/preview", system_api.NewFacilityFileController().Preview)
	}
}
