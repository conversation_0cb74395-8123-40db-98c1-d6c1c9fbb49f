package routers

import (
	"github.com/Norray/medic-crew/api/v1/professional_api"
	"github.com/gin-gonic/gin"
)

func professionalSuperannuationRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/professional").Use(handlers...)
	{
		controller := professional_api.NewProfessionalSuperannuationController()
		r.POST("/superannuation/actions/edit", controller.Edit)
	}
}
