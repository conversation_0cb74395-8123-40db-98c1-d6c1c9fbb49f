package system_api

import (
	"fmt"
	"net/http"

	"github.com/Norray/medic-crew/model"
	"github.com/nicksnyder/go-i18n/v2/i18n"

	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	"github.com/gin-gonic/gin"
)

type DocumentFileController struct {
	v1.CommonController
}

func NewDocumentFileController() DocumentFileController {
	return DocumentFileController{}
}

// @Tags Document File
// @Summary 獲取單據文件圖片
// @Description 直接獲取單據文件的圖片內容，可以在瀏覽器中直接顯示
// @Router /v1/system/document-files/actions/preview [GET]
// @Security ApiKeyAuth
// @Param json query services.DocumentFileGetPreviewReq true "parameter"
// @Success 200 "Success"
func (con DocumentFileController) Preview(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.DocumentFileGetPreviewReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var documentFile model.DocumentFile
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.DocumentFileService.CheckIdExistByFacilityId(db, &documentFile, req.DocumentFileId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		req.UserId = documentFile.UserId

		// 獲取圖片數據
		resp, err := services.DocumentFileService.Preview(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		c.Writer.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename*=utf-8''%s", xtool.ReplacePlus(resp.Filename)))
		c.Data(http.StatusOK, services.DocumentFileService.GetFileMimeType(resp.Filename), resp.FileBytes)

	} else {
		nc.BadRequestResponse(err)
	}
}
