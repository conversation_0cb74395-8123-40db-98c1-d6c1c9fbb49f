package model

import (
	"time"

	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xmodel/xtype"
	"github.com/shopspring/decimal"
)

const (
	FacilityProfileDataTypeDraft    = "DRAFT"    // 草稿 (status=PENDING才可以修改)
	FacilityProfileDataTypeApproved = "APPROVED" // 已通過 (不能修改)
	FacilityProfileDataTypeHistory  = "HISTORY"  // 歷史 (不能修改)

	FacilityProfileStatusPending   = "PENDING"   // 未提交
	FacilityProfileStatusReviewing = "REVIEWING" // 審核中
	FacilityProfileStatusApproved  = "APPROVED"  // 已通過
	FacilityProfileStatusDeleted   = "DELETED"   // 已刪除 (註銷時標記)

	FacilityProfilePaymentTermsPayInArrears = "PAY_IN_ARREARS" // 事後支付
	FacilityProfilePaymentTermsPayUpfront   = "PAY_UPFRONT"    // 預付款

	FacilityProfileOrientationConfirmationY = "Y" // 確認提供適當入職培訓
	FacilityProfileOrientationConfirmationN = "N" // 不確認提供適當入職培訓

)

// 機構
type FacilityProfile struct {
	Id                                 uint64          `json:"id" gorm:"primary_key"`
	FacilityId                         uint64          `json:"facilityId" gorm:"index:facility_idx;index:facility_data_type_idx,priority:1;not null"`                                                       // 機構ID
	No                                 string          `json:"no" gorm:"type:varchar(32);not null"`                                                                                                         // 機構編號
	DataType                           string          `json:"dataType" gorm:"type:varchar(32);index:data_type_idx;index:data_type_status_idx,priority:1;index:facility_data_type_idx,priority:2"`          // 數據類型 DRAFT=草稿,APPROVED=已通過,HISTORY=歷史
	FacilityType                       string          `json:"facilityType" gorm:"type:varchar(64);index:facility_type_idx;not null"`                                                                       // 機構類型
	FacilityTypeName                   string          `json:"facilityTypeName" gorm:"type:varchar(128);not null"`                                                                                          // 其他機構類型名稱 (當 FacilityType 為 OTHER 時)
	Name                               string          `json:"name" gorm:"type:varchar(128);index:name_idx;not null"`                                                                                       // 機構名稱
	Email                              string          `json:"email" gorm:"type:varchar(128);not null"`                                                                                                     // 機構聯絡電郵
	Address                            string          `json:"address" gorm:"type:varchar(255);not null"`                                                                                                   // 地址
	AddressExtra                       string          `json:"addressExtra" gorm:"type:varchar(255);not null"`                                                                                              // 地址附加信息
	LocationState                      string          `json:"locationState" gorm:"type:varchar(128);index:location_state_idx;not null"`                                                                    // 州
	LocationCity                       string          `json:"locationCity" gorm:"type:varchar(128);index:location_city_idx;not null"`                                                                      // 城市
	LocationRoute                      string          `json:"locationRoute" gorm:"type:varchar(128);index:location_route_idx;not null"`                                                                    // 街道
	LocationLat                        decimal.Decimal `json:"locationLat" gorm:"type:decimal(12,8);not null"`                                                                                              // 緯度
	LocationLng                        decimal.Decimal `json:"locationLng" gorm:"type:decimal(12,8);not null"`                                                                                              // 經度
	ApplicationTime                    *time.Time      `swaggertype:"string" json:"applicationTime" gorm:"type:datetime(0);index:application_time_idx;index:status_application_time_idx,priority:2"`        // 申請日期
	Status                             string          `json:"status" gorm:"type:varchar(32);index:status_idx;index:data_type_status_idx,priority:2;index:status_application_time_idx,priority:1;not null"` // 狀態
	ApprovedTime                       *time.Time      `swaggertype:"string" json:"approvedTime" gorm:"type:datetime(0)"`                                                                                   // 審核時間
	ApprovedUserId                     uint64          `json:"approvedUserId" gorm:"not null"`                                                                                                              // 審核人員
	RejectReason                       string          `json:"rejectReason" gorm:"type:varchar(1024);not null"`                                                                                             // 拒絕原因
	Abn                                string          `json:"abn" gorm:"type:varchar(11);not null"`                                                                                                        // 商業登記號碼
	ContactFirstName                   string          `json:"contactFirstName" gorm:"type:varchar(255);not null"`                                                                                          // 聯絡人姓名
	ContactLastName                    string          `json:"contactLastName" gorm:"type:varchar(255);not null"`                                                                                           // 聯絡人姓氏
	ContactEmail                       string          `json:"contactEmail" gorm:"type:varchar(128);not null"`                                                                                              // 聯絡人電郵
	ContactPhone                       string          `json:"contactPhone" gorm:"type:varchar(128);not null"`                                                                                              // 聯絡人電話
	ContactRole                        string          `json:"contactRole" gorm:"type:varchar(128);not null"`                                                                                               // 聯絡人角色
	ExpertiseRequired                  string          `json:"expertiseRequired" gorm:"type:varchar(1024);not null"`                                                                                        // 專業需求
	PaymentTerms                       string          `json:"paymentTerms" gorm:"type:varchar(32);not null"`                                                                                               // 付款條件
	PublicLiabilityInsuranceExpiryDate xtype.NullDate  `swaggertype:"string" json:"publicLiabilityInsuranceExpiryDate" gorm:"type:date"`                                                                    // 公共責任保險過期日期
	OrientationConfirmation            string          `json:"orientationConfirmation" gorm:"type:varchar(1);not null"`                                                                                     // 確認提供適當入職培訓 (Y/N)
	xmodel.Model
}

func (FacilityProfile) TableName() string {
	return "facility_profile"
}

func (FacilityProfile) SwaggerDescription() string {
	return "機構資料"
}
