package model

import "github.com/Norray/xrocket/xmodel"

// Google用戶
type GoogleUser struct {
	Id         uint64 `json:"id" gorm:"primary_key"`
	UserId     uint64 `json:"userId" gorm:"unique_index:user_idx;not null"`   // 用戶ID
	GoogleId   string `json:"googleId" gorm:"unique_index:user_idx;not null"` // Google ID
	Email      string `json:"email" gorm:"unique_index:user_idx;not null"`    // 電郵
	CreateDate string `json:"createDate" gorm:"not null"`                     // 創建日期
	xmodel.Model
}

func (GoogleUser) TableName() string {
	return "google_user"
}

func (GoogleUser) SwaggerDescription() string {
	return "Google用戶"
}
