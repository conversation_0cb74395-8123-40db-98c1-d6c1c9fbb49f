# 津貼金額計算（Job 級別設計）

注意，每次計算應該將金額保留2位小數，避免計算精度問題

## 設計概述
- **存儲級別**: Job 級別（不再為每個 JobShift 單獨存儲）
- **計算方式**: 需要時動態計算（發單據時、顯示時）
- **數據一致性**: Job 發佈後配置不可修改，確保計算基礎穩定

## 關鍵值
- 津貼配置 JobAllowance（Job 級別）
- 津貼類型 JobAllowance.AllowanceType HOURLY, SHIFT, JOB
- 基礎金額 JobAllowance.BaseAmount（發佈時快照的金額）
- 工作時長 JobShift.PayHours
- 是否包含公務員津貼 JobAllowance.AttractsSuperannuation 如果等於Y，需要計算後的津貼金額加上12%的superannuation

## 動態計算邏輯

### 按每小時津貼 （AllowanceType = HOURLY）
```
每個JobShift的津貼金額 = 基礎金額 × 該班次工作時長
amount = jobAllowance.BaseAmount × jobShift.PayHours

如果包含公務員津貼：
amount = amount × 1.12
```

### 按每班次津貼 （AllowanceType = SHIFT）
```
每個JobShift的津貼金額 = 固定基礎金額
amount = jobAllowance.BaseAmount

如果包含公務員津貼：
amount = amount × 1.12
```

### 按每職位津貼 （AllowanceType = JOB）
```
每個JobShift的津貼金額 = 基礎金額 ÷ 總班次數
amount = jobAllowance.BaseAmount ÷ totalJobShifts

誤差處理：
- 平均分配到各班次
- 如因計算精度問題導致誤差，將誤差計入最後一個JobShift（按ID排序）

如果包含公務員津貼：
amount = amount × 1.12
```

## 計算示例

假設一個 Job 有 2 個 JobShift：
- JobShift A: 8小時
- JobShift B: 4小時

津貼配置：
- 小時津貼: $5/小時
- 班次津貼: $20/班次
- 職位津貼: $100/職位（包含退休金）

計算結果：
```
JobShift A:
- 小時津貼: $5 × 8 = $40
- 班次津貼: $20
- 職位津貼: ($100 ÷ 2) × 1.12 = $56
- 總計: $116

JobShift B:
- 小時津貼: $5 × 4 = $20
- 班次津貼: $20
- 職位津貼: ($100 ÷ 2) × 1.12 = $56
- 總計: $96
```
