package services

import (
	"fmt"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xgorm"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gorm.io/gorm"
)

var FacilityService = new(facilityService)

type facilityService struct{}

func (s *facilityService) CheckIdExist(db *gorm.DB, m *model.Facility, id uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility.id.does_not_exist",
		Other: "No such record, please try after reloading.",
	}
	var err error
	if err = db.First(&m, id).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

type FacilityEditReq struct {
	FacilityId              uint64 `json:"facilityId"  binding:"required"`
	BreakTimeDurationMinute int32  `json:"breakTimeDurationMinute" binding:"required,min=0"`
	BreakTimeEveryHour      int32  `json:"breakTimeEveryHour"  binding:"required,min=0"`
}

func (s *facilityService) Edit(db *gorm.DB, req FacilityEditReq) error {
	var err error
	var m model.Facility
	if err = db.First(&m, req.FacilityId).Error; err != nil {
		return err
	}
	_ = copier.Copy(&m, req)
	if err = db.Save(&m).Error; err != nil {
		return err
	}
	return nil
}

type FacilityInquireReq struct {
	FacilityId uint64 `form:"facilityId" binding:"required"`
}

type FacilityInquireResp struct {
	FacilityId              uint64 `json:"facilityId"`
	BreakTimeDurationMinute int32  `json:"breakTimeDurationMinute"`
	BreakTimeEveryHour      int32  `json:"breakTimeEveryHour"`
	Deactivated             string `json:"deactivated"`
}

func (s *facilityService) Inquire(db *gorm.DB, req FacilityInquireReq) (FacilityInquireResp, error) {
	var err error
	var resp FacilityInquireResp
	var m model.Facility
	if err = db.First(&m, req.FacilityId).Error; err != nil {
		return resp, err
	}
	_ = copier.Copy(&resp, m)
	resp.FacilityId = m.Id
	return resp, nil
}

type FacilitySearchForProfessionalReq struct {
	Name       string `form:"name"`       // 機構名稱
	ReqUserId  uint64 `json:"-"`          // 請求者Id
	SelectedId uint64 `form:"selectedId"` // 選中ID
	Limit      int    `form:"limit"`      // 每頁條目數
}

type FacilitySearchForProfessionalResp struct {
	FacilityId   uint64 `json:"facilityId"`   // 機構Id
	FacilityName string `json:"facilityName"` // 機構名稱
}

func (s *facilityService) FacilitySearchForProfessional(db *gorm.DB, req FacilitySearchForProfessionalReq) ([]FacilitySearchForProfessionalResp, error) {
	var resp []FacilitySearchForProfessionalResp
	builder := db.Table("job_application ja").
		Joins("JOIN facility f ON ja.facility_id = f.id").
		Joins("JOIN facility_profile fp ON f.id = fp.facility_id AND fp.data_type = ?", model.FacilityProfileDataTypeApproved).
		Select([]string{
			"f.id AS facility_id",
			"fp.name AS facility_name",
		}).
		Where("ja.user_id = ?", req.ReqUserId).
		Where("ja.deleted <> ?", model.JobApplicationDeletedY)

	if req.Name != "" {
		builder = builder.Where("fp.name LIKE ?", xgorm.EscapeLikeWithWildcards(req.Name))
	}

	if req.SelectedId != 0 {
		builder = builder.Order(fmt.Sprintf("IF(f.id = %d,0,1)", req.SelectedId))
	}

	if req.Limit > 0 {
		builder = builder.Limit(req.Limit)
	}

	if err := builder.
		Group("f.id").
		Order("ja.id DESC").
		Find(&resp).Error; err != nil {
		return nil, err
	}
	return resp, nil
}

type FacilitySearchReq struct {
	Name               string `form:"name" binding:"omitempty"`               // 名稱
	ProfessionalUserId uint64 `form:"professionalUserId" binding:"omitempty"` // 專業人員Id
	Approved           string `form:"approved" binding:"omitempty,oneof=Y N"` // 是否已經審批通過
	SelectedId         uint64 `form:"selectedId" binding:"omitempty"`         // 選中Id
	Limit              int    `form:"limit" binding:"omitempty"`              // 限制
}

type FacilitySearchResp struct {
	FacilityId              uint64 `json:"facilityId"`              // 機構Id
	FacilityProfileId       uint64 `json:"facilityProfileId"`       // 機構Id
	FacilityNo              string `json:"facilityNo"`              // 機構編號
	FacilityName            string `json:"facilityName"`            // 機構名稱
	LastFacilityAgreementId uint64 `json:"lastFacilityAgreementId"` // 最後一份機構協議Id
}

func (s *facilityService) Search(db *gorm.DB, req FacilitySearchReq) ([]FacilitySearchResp, error) {
	var err error
	var resp []FacilitySearchResp

	subQuery := db.Table("facility_agreement fa").
		Select("fa.facility_id, MAX(fa.id) as id").
		Group("fa.facility_id").
		Order("fa.end_time DESC").
		Order("fa.id DESC")

	builder := db.Table("facility AS f").
		Joins("JOIN facility_profile AS fp ON fp.facility_id = f.id AND fp.data_type = ?", model.FacilityProfileDataTypeDraft).               // 因為審批後機構名稱不能修改，所有統一使用草稿機構名稱
		Joins("LEFT JOIN facility_profile AS fp_a ON fp_a.facility_id = f.id AND fp_a.data_type = ?", model.FacilityProfileDataTypeApproved). // 只顯示審批後端機構Id
		Joins(`LEFT JOIN (?) AS fa ON fa.facility_id = f.id`, subQuery).
		Select([]string{
			"f.id AS facility_id",
			"fp_a.id AS facility_profile_id",
			"fp_a.no AS facility_no",
			"fp.name AS facility_name",
			"fa.id AS last_facility_agreement_id",
		}).
		Where("fp.facility_type <> ?", "")

	if req.ProfessionalUserId > 0 {
		jobApplicationSubQuery := db.Table("job_application ja").
			Select("ja.facility_id").
			Where("ja.user_id = ?", req.ProfessionalUserId).
			Where("ja.deleted <> ?", model.JobApplicationDeletedY).
			Where("ja.status <> ?", model.JobApplicationStatusWithdraw).
			Group("ja.facility_id")
		builder = builder.Joins("JOIN (?) AS ja ON ja.facility_id = f.id", jobApplicationSubQuery)
	}

	if req.Approved != "" {
		builder = builder.Where("fp_a.id IS NOT NULL")
	}

	if req.Name != "" {
		builder = builder.Where("fp.name LIKE ?", xgorm.EscapeLikeWithWildcards(req.Name))
	}
	if req.SelectedId != 0 {
		builder = builder.Order(fmt.Sprintf("IF(f.id = %d,0,1)", req.SelectedId))
	}
	if req.Limit > 0 {
		builder = builder.Limit(req.Limit)
	}
	if err = builder.
		Order("fp.name").
		Group("f.id").
		Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}
