package main

import (
	"github.com/Norray/xrocket/xgorm"
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/generator"
)

func TestGenerate(t *testing.T) {
	g := generator.DefaultGenerator()
	err := g.Generate([]string{
		generator.GenerateEdit,
		generator.GenerateInquire,
	}, model.Facility{})
	if err != nil {
		t.Error(err)
	}
}

func TestRecursiveReplaceSQLPatterns(t *testing.T) {
	result, err := xgorm.RecursiveReplaceSQLPatterns(".")
	if err != nil {
		t.Error(err)
	}
	for _, v := range result {
		t.Log(v)
	}
}
