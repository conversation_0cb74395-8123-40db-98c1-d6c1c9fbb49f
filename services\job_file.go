package services

import (
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xtool"
	"github.com/nicksnyder/go-i18n/v2/i18n"

	"gorm.io/gorm"
)

type jobFileService struct{}

var JobFileService = new(jobFileService)

func (s *jobFileService) CheckFileExist(db *gorm.DB, files *[]model.JobFile, facilityId uint64, fileCodes []string, ids []uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_file.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	if len(ids) == 0 {
		return true, i18n.Message{}, nil
	}
	ids = xtool.Uint64ArrayDeduplication(ids)
	builder := db.Table("job_file AS jf").
		Joins("JOIN facility_file AS ff ON jf.facility_file_id = ff.id").
		Where("ff.facility_id = ?", facilityId).
		Where("ff.file_code IN (?)", fileCodes).
		Where("jf.id IN (?)", ids)
	if err := builder.Find(files).Error; err != nil {
		return false, msg, err
	}
	if len(ids) == len(*files) {
		return true, i18n.Message{}, nil
	}
	return false, msg, nil
}

type JobFileListReq struct {
	FacilityId         uint64 `json:"facility_id" binding:"required"`                                                                                          // 機構ID
	PositionProfession string `json:"position_profession" binding:"omitempty,oneof=MEDICAL_PRACTITIONER ENROLLED_NURSE REGISTERED_NURSE PERSONAL_CARE_WORKER"` // 職位專業 MEDICAL_PRACTITIONER ENROLLED_NURSE REGISTERED_NURSE PERSONAL_CARE_WORKER
}

type JobFileListResp struct {
	FacilityFileId string `json:"facilityFileId"` // 機構文件ID
	FileCode       string `json:"fileCode"`       // 文件編號
	OriginFileName string `json:"originFileName"` // 文件名稱
}

// 獲取機構指定工作職位的文件列表
func (s *jobFileService) JobFileList(db *gorm.DB, req *JobFileListReq) ([]JobFileListResp, error) {
	resp := make([]JobFileListResp, 0)
	builder := db.Model(&model.JobFile{}).
		Table("job_file AS jf").
		Joins("JOIN facility_file AS ff ON jf.facility_file_id = ff.id").
		Select([]string{
			"jf.id AS facility_file_id",
			"jf.file_code",
			"jf.origin_file_name",
		}).
		Where("facility_id = ?", req.FacilityId)
	if req.PositionProfession != "" {
		builder.Where("position_profession.id IN ?", req.PositionProfession)
	}
	if err := builder.
		Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

// 根據 JobId 獲取工作文件列表
func (s *jobFileService) GetJobFilesByJobId(db *gorm.DB, jobId uint64) ([]JobFileListResp, error) {
	var resp []JobFileListResp

	err := db.Model(&model.JobFile{}).
		Table("job_file AS jf").
		Joins("JOIN facility_file AS ff ON jf.facility_file_id = ff.id").
		Select([]string{
			"ff.id AS facility_file_id",
			"ff.file_code",
			"ff.origin_file_name",
		}).
		Where("jf.job_id = ?", jobId).
		Find(&resp).Error

	return resp, err
}

// 檢查機構文件是否存在並且是指定職業的文件
func (s *jobFileService) CheckFacilityFilesForProfession(db *gorm.DB, facilityId uint64, positionProfession string, facilityFileIds []uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job_file.facility_files.invalid_for_profession",
		Other: "Some files do not exist or are not valid for the specified profession.",
	}

	if len(facilityFileIds) == 0 {
		return true, i18n.Message{}, nil // 空數組視為通過
	}

	// 獲取職業對應的文件代碼
	expectedFileCode := model.GetOrientationDocumentationCodeByProfession(positionProfession)
	if expectedFileCode == "" {
		return false, i18n.Message{
			ID:    "checker.job_file.profession.not_supported",
			Other: "The specified profession is not supported.",
		}, nil
	}

	// 去重
	facilityFileIds = xtool.Uint64ArrayDeduplication(facilityFileIds)

	// 檢查所有文件是否存在並且是正確的職業文件
	var count int64
	err := db.Model(&model.FacilityFile{}).
		Where("facility_id = ?", facilityId).
		Where("id IN (?)", facilityFileIds).
		Where("file_code = ?", expectedFileCode).
		Count(&count).Error

	if err != nil {
		return false, i18n.Message{}, err
	}

	// 檢查找到的文件數量是否等於傳入的文件ID數量
	if int64(len(facilityFileIds)) != count {
		return false, msg, nil
	}

	return true, i18n.Message{}, nil
}
