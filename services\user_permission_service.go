package services

import (
	"fmt"
	"strings"

	"github.com/Norray/medic-crew/middleware"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xcasbin"
	"github.com/Norray/xrocket/xgorm"
	"github.com/casbin/casbin/v2"
	"github.com/gin-gonic/gin"
)

type userPermissionService struct{}

var UserPermissionService = new(userPermissionService)

type UserPermissionInquireReq struct {
	UserId uint64 `form:"userId" binding:"required"`
	V0     string `form:"v0"`
	V1     string `form:"v1"`
	V2     string `form:"v2"`
	Level  string `form:"level"`
	Remark string `form:"remark"`
}

type UserApiPermissionInquireResp struct {
	PolicyId uint64 `json:"policyId"`
	Ptype    string `json:"ptype"`
	V0       string `json:"v0"`
	V1       string `json:"v1"`
	V2       string `json:"v2"`
	Level    string `json:"level"`
	Remark   string `json:"remark"`
}

type userApiPermissionCache struct {
	V0 string `json:"v0"`
	V1 string `json:"v1"`
}

func (s *userPermissionService) Inquire(c *gin.Context, req UserPermissionInquireReq, pageSet *xresp.PageSet) ([]UserApiPermissionInquireResp, error) {
	db := xgorm.DB.WithContext(c)

	// 獲取此用戶有什麼casbin的sub
	subs, err := xcasbin.GetUserRoleCacheWithHandler(c, fmt.Sprintf("%d", req.UserId), middleware.GetUserRoleCache)
	if err != nil {
		return nil, err
	}

	var allSubMap = make(map[string][]string)
	// 去各個casbin分別查詢所有sub
	for _, table := range casbinTableNameMap {
		var g []userApiPermissionCache
		err = db.Select("v0,v1").
			Table(table).
			Where("ptype = ?", "g").
			Find(&g).Error
		if err != nil {
			return nil, err
		}
		enforcer, err := casbin.NewEnforcer("config/rbac_model.conf")
		if err != nil {
			return nil, err
		}
		// 初始化casbin
		for _, cas := range g {
			_, err := enforcer.AddRoleForUser(cas.V0, cas.V1)
			if err != nil {
				return nil, err
			}
		}
		// 查看role所屬的所有sub
		var result [][]string
		result = append(result, subs)
		for _, s := range subs {
			cs, err := enforcer.GetImplicitRolesForUser(s)
			if err != nil {
				return nil, err
			}
			result = append(result, cs)
		}
		allSubMap[table] = s.uniqueArray(result...)
	}

	builders := make([]interface{}, 0)
	unions := make([]string, 0)
	for _, table := range casbinTableNameMap {
		builder := db.Select("id AS policy_id,ptype,v0,v1,v2,level,remark").Where("ptype = ?", "p").Where("v0 IN (?)", allSubMap[table]).Table(table)
		if req.V0 != "" {
			builder = builder.Where("v0 LIKE ?", "%"+req.V0+"%")
		}
		if req.V1 != "" {
			builder = builder.Where("v1 LIKE ?", "%"+req.V1+"%")
		}
		if req.V2 != "" {
			builder = builder.Where("v2 LIKE ?", "%"+req.V2+"%")
		}
		if req.Remark != "" {
			builder = builder.Where("remark LIKE ?", xgorm.EscapeLikeWithWildcards(req.Remark))
		}
		if req.Level != "" {
			builder = builder.Where("level = ?", req.Level)
		}
		builders = append(builders, builder)
		unions = append(unions, "?")
	}

	var resp []UserApiPermissionInquireResp
	var total int64
	if pageSet.Valid() {
		var ans []int64
		err = db.Raw(fmt.Sprintf("SELECT t.policy_id FROM ( %s ) AS t ORDER BY t.v1,t.v0", strings.Join(unions, " UNION ")), builders...).Find(&ans).Error
		if err != nil {
			return nil, err
		}
		total = int64(len(ans))
		err = db.Raw(fmt.Sprintf("SELECT t.* FROM ( %s ) AS t ORDER BY t.v1,t.v0 LIMIT %d OFFSET %d", strings.Join(unions, " UNION "), pageSet.PageSize, (pageSet.PageIndex-1)*pageSet.PageSize), builders...).Find(&resp).Error
		if err != nil {
			return nil, err
		}
	} else {
		err = db.Raw(fmt.Sprintf("SELECT t.* FROM ( %s ) AS t ORDER BY t.v1,t.v0", strings.Join(unions, " UNION ")), builders...).Find(&resp).Error
		if err != nil {
			return nil, err
		}
		total = int64(len(resp))
	}
	pageSet.Total = total
	return resp, nil
}

func (s *userPermissionService) uniqueArray(arr ...[]string) []string {
	var intersection []string
	diffElem := make(map[string]bool)

	for _, a := range arr {
		for _, v := range a {
			if _, ok := diffElem[v]; ok {
				continue
			} else {
				diffElem[v] = true
			}
			intersection = append(intersection, v)
		}
	}
	return intersection
}
