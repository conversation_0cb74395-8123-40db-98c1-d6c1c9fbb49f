package main

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/model/xmigrate"

	"github.com/Norray/xrocket/xsentry"
	"github.com/getsentry/sentry-go"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xgoogle"

	"github.com/Norray/medic-crew/docs"
	"github.com/Norray/medic-crew/internal/job"
	"github.com/Norray/medic-crew/internal/task"
	"github.com/Norray/medic-crew/routers"
	"github.com/Norray/xrocket/xcasbin"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xexcel"
	"github.com/Norray/xrocket/xgeoip2"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xi18n"
	"github.com/Norray/xrocket/xjwt"
	"github.com/Norray/xrocket/xredis"
	"github.com/Norray/xrocket/xs3"
	"github.com/gin-gonic/gin"
	_ "github.com/go-sql-driver/mysql"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	log "github.com/sirupsen/logrus"
)

func init() {
	xconfig.Setup("config/app.ini")
	xgoogle.DefaultSetUp()
	xredis.DefaultSetup()
	xgorm.DefaultSetup()
	xjwt.DefaultSetup()
	xs3.DefaultSetup()

	_ = xgorm.DB.AutoMigrate(
		&model.SystemNotification{},
		&model.SystemNotificationUser{},
	)
	_ = xmigrate.MigrateAll()
	services.DefaultSetup()
	xgeoip2.Setup("resource/geoip/GeoLite2-City.mmdb")
	xi18n.Setup([]string{
		"resource/i18n/active.en.toml",
		"resource/i18n/active.zh-CN.toml",
		"resource/i18n/active.zh-HK.toml",
	})
	xexcel.SetupI18n(
		map[string]i18n.Message{
			xexcel.IncorrectTitle: {ID: "xexcel.import.incorrect_title", Other: "Incorrect import heading."},
			xexcel.Required:       {ID: "xexcel.import.required", Other: "Row %d: Column [%s] can not be empty."},
			xexcel.Oneof:          {ID: "xexcel.import.oneof", Other: "Row %d: Column [%s] must be one of '%s'."},
			xexcel.Date:           {ID: "xexcel.import.date", Other: "Row %d: Column [%s] is not the correct date format."},
			xexcel.DBExist:        {ID: "xexcel.import.db_exist", Other: "Row %d: Column %s dose not exist."},
		},
	)
	xcasbin.DefaultSetup("console_casbin", "config/rbac_model.conf", "console_casbin")
}

// @title  medic-crew
// @description medic-crew API doc.

// @BasePath /medic-crew/api
// @query.collection.format json

// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name X-Token
func main() {
	defer log.Println("server exited")

	if xconfig.SwaggerConf.SwaggerHostPort == 80 || xconfig.SwaggerConf.SwaggerHostPort == 443 {
		docs.SwaggerInfo.Host = xconfig.SwaggerConf.SwaggerHost
	} else {
		docs.SwaggerInfo.Host = fmt.Sprintf("%s:%d", xconfig.SwaggerConf.SwaggerHost, xconfig.SwaggerConf.SwaggerHostPort)
	}
	if xconfig.ServerConf.HttpProxy != "" {
		if err := os.Setenv("HTTP_PROXY", xconfig.ServerConf.HttpProxy); err != nil {
			return
		}
		log.Println("HTTP_PROXY:", os.Getenv("HTTP_PROXY"))
	}
	if xconfig.ServerConf.HttpsProxy != "" {
		if err := os.Setenv("HTTPS_PROXY", xconfig.ServerConf.HttpsProxy); err != nil {
			return
		}
		log.Println("HTTPS_PROXY:", os.Getenv("HTTPS_PROXY"))
	}
	gin.SetMode(xconfig.ServerConf.RunMode)
	endPoint := fmt.Sprintf(":%d", xconfig.ServerConf.HttpPort)
	readTimeout := xconfig.ServerConf.ReadTimeout
	writeTimeout := xconfig.ServerConf.WriteTimeout
	routersInit := routers.InitRouter()
	s := &http.Server{
		Addr:         endPoint,
		Handler:      routersInit,
		ReadTimeout:  readTimeout,
		WriteTimeout: writeTimeout,
	}
	defer xredis.Close()
	job.ArrangeCronJob()
	task.SetupWorkers()
	xsentry.DefaultSetup()
	if xsentry.SentryInitialized() {
		defer sentry.Flush(2 * time.Second) // 確保事件送出
	}
	go func() {
		//進入協程,開始listen
		if err := s.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Fatalf("server fail to run: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shut down the server with
	// a timeout of 5 seconds.
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt)
	<-quit
	log.Println("shutdown server")
	job.StopCronJob()
	task.Close()
	xgeoip2.Close()
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := s.Shutdown(ctx); err != nil {
		log.Fatal("server fail to shutdown:", err)
	}
}
