package model

import "github.com/Norray/xrocket/xmodel"

const (
	FaqStatusEnable  = "ENABLE"
	FaqStatusDisable = "DISABLE"

	FaqCategoryProfessional = "PROFESSIONAL" // 專業人士
	FaqCategoryFacility     = "FACILITY"     // 機構
)

// 常見問題
type Faq struct {
	Id         uint64 `json:"id" gorm:"primary_key"`
	Category   string `json:"category" gorm:"type:varchar(16);not null"`    // 分類 PROFESSIONAL=專業人士 FACILITY=機構
	Question   string `json:"question" gorm:"type:varchar(1024);not null"`  // 問題
	Answer     string `json:"answer" gorm:"type:mediumtext;not null"`       // 答案
	AnswerText string `json:"answerText" gorm:"type:varchar(255);not null"` // 答案純文本
	Seq        int32  `json:"seq" gorm:"not null"`                          // 排序
	Status     string `json:"status" gorm:"type:varchar(16);not null"`      // 狀態 ENABLE=啟用 DISABLE=停用
	xmodel.Model
}

func (Faq) TableName() string {
	return "faq"
}

func (Faq) SwaggerDescription() string {
	return "常見問題"
}
