package facility_api

import (
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/gin-gonic/gin"
)

type GoogleController struct{}

func NewGoogleController() GoogleController {
	return GoogleController{}
}

// @Tags Google
// @Summary 獲取時區
// @Description
// @Router /v1/facility/google/timezone [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.TimezoneRequest true "parameter"
// @Success 200 {object} services.TimezoneResponse "Success"
func (con GoogleController) Timezone(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.TimezoneRequest
	if err := c.ShouldBindQuery(&req); err == nil {
		resp, err := services.GoogleMapsService.GetTimezone(c, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
