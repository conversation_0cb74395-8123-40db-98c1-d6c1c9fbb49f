package services

import (
	"fmt"
	"time"

	"github.com/Norray/xrocket/xmodel/xtype"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xtool"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

var ProfessionalDashboardService = new(professionalDashboardService)

type professionalDashboardService struct{}

// region ---------------------------------------------------- 專業人員收入統計 ----------------------------------------------------

type ProfessionalIncomeRequest struct {
	UserId    uint64 `form:"-" json:"-"`                                       // 用戶ID
	BeginDate string `form:"beginDate" binding:"required,datetime=2006-01-02"` // 開始日期
	EndDate   string `form:"endDate" binding:"required,datetime=2006-01-02"`   // 結束日期
}

type PeriodIncomeItem struct {
	Date         xtype.Date      `swaggertype:"string" json:"date"` // 日期
	TotalAmount  decimal.Decimal `json:"totalAmount"`               // 總金額
	PaidAmount   decimal.Decimal `json:"paidAmount"`                // 已付款金額
	UnpaidAmount decimal.Decimal `json:"unpaidAmount"`              // 未付款金額
}

func (s *professionalDashboardService) ProfessionalIncome(db *gorm.DB, req ProfessionalIncomeRequest) ([]PeriodIncomeItem, error) {
	var resp []PeriodIncomeItem

	builder := db.Table("document d").
		Where("d.category = ?", model.DocumentCategoryInvoice).
		Where("d.user_id = ?", req.UserId).
		Where("d.data_type IN (?)", []string{model.DocumentDataTypeProfessionalToFacility, model.DocumentDataTypeProfessionalToSystem}).
		Where("d.document_date >= ?", req.BeginDate).
		Where("d.document_date <= ?", req.EndDate).
		Select([]string{
			"d.document_date as date",
			"SUM(d.grand_total) as total_amount",
			fmt.Sprintf("SUM(CASE WHEN d.paid = '%s' THEN d.grand_total ELSE 0 END) as paid_amount", model.DocumentPaidY),
			fmt.Sprintf("SUM(CASE WHEN d.paid = '%s' THEN d.grand_total ELSE 0 END) as unpaid_amount", model.DocumentPaidN),
		})
	var result []PeriodIncomeItem
	if err := builder.
		Group("d.document_date").
		Order("d.document_date ASC").
		Scan(&result).Error; err != nil {
		return resp, err
	}

	// 創建數據映射
	dataMap := make(map[string]PeriodIncomeItem)
	for _, item := range result {
		dataMap[item.Date.String()] = item
	}

	// 使用 GenerateRange 生成完整的日期數組
	dateRange, err := GenerateRange(req.BeginDate, req.EndDate)
	if err != nil {
		return resp, err
	}

	for _, dateStr := range dateRange {
		if item, exists := dataMap[dateStr]; exists {
			resp = append(resp, item)
		} else {
			resp = append(resp, PeriodIncomeItem{
				Date:         xtype.NewDate(dateStr),
				TotalAmount:  decimal.Zero,
				PaidAmount:   decimal.Zero,
				UnpaidAmount: decimal.Zero,
			})
		}
	}

	return resp, nil
}

// endregion

// region ------------------------------------------ 儀表板匯總統計 ------------------------------------------

type DashboardSummaryRequest struct {
	UserId    uint64 `form:"userId" binding:"required"`                        // 用戶ID
	BeginDate string `form:"beginDate" binding:"required,datetime=2006-01-02"` // 開始日期
	EndDate   string `form:"endDate" binding:"required,datetime=2006-01-02"`   // 結束日期
	TimeZone  string `form:"timeZone" binding:"required,timezone"`             // 時區
}

type DashboardSummaryResponse struct {
	TotalIncome       decimal.Decimal `json:"totalIncome"`       // 總收入
	CompletedJobs     int             `json:"completedJobs"`     // 已完成工作數
	TotalWorkingHours decimal.Decimal `json:"totalWorkingHours"` // 總工作時數
}

func (s *professionalDashboardService) DashboardSummary(db *gorm.DB, req DashboardSummaryRequest) (DashboardSummaryResponse, error) {
	var resp DashboardSummaryResponse

	// 解析時區
	tz, err := time.LoadLocation(req.TimeZone)
	if err != nil {
		return resp, err
	}

	// 解析開始和結束日期
	beginDate, err := time.ParseInLocation(xtool.DateDayA, req.BeginDate, tz)
	if err != nil {
		return resp, err
	}
	endDate, err := time.Parse(xtool.DateDayA, req.EndDate)
	if err != nil {
		return resp, err
	}
	beginDateUTC := beginDate.UTC()
	endDateUTC := endDate.UTC()

	// 調整結束日期到當天的23:59:59
	endDate = time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 23, 59, 59, 0, tz)

	// 1. 計算總收入 - 使用與ProfessionalIncome一致的統計方式
	var totalIncomeResult struct {
		TotalAmount decimal.Decimal `json:"totalAmount"`
	}

	incomeQuery := db.Table("document d").
		Where("d.category = ?", model.DocumentCategoryInvoice).
		Where("d.user_id = ?", req.UserId).
		Where("d.data_type IN (?)", []string{model.DocumentDataTypeProfessionalToFacility, model.DocumentDataTypeProfessionalToSystem}).
		Where("d.document_date >= ?", req.BeginDate).
		Where("d.document_date <= ?", req.EndDate).
		Select("COALESCE(SUM(d.grand_total), 0) as total_amount")

	if err = incomeQuery.Scan(&totalIncomeResult).Error; err != nil {
		return resp, err
	}
	resp.TotalIncome = totalIncomeResult.TotalAmount

	// 2. 計算已完成工作數
	var completedJobsCount int64
	jobCountQuery := db.Table("job_application ja").
		Joins("JOIN job j ON ja.job_id = j.id").
		Where("ja.user_id = ?", req.UserId).
		Where("ja.status = ?", model.JobApplicationStatusAccept).
		Where("j.end_time >= ? AND j.end_time <= ?", beginDateUTC, endDateUTC)

	if err = jobCountQuery.Count(&completedJobsCount).Error; err != nil {
		return resp, err
	}
	resp.CompletedJobs = int(completedJobsCount)

	// 3. 計算總工作時數 - 使用現有的WorkingHoursAnalytics邏輯
	dailyHours, err := s.WorkingHoursAnalytics(db, WorkingHoursRequest{
		UserId:    req.UserId,
		BeginDate: req.BeginDate,
		EndDate:   req.EndDate,
		TimeZone:  req.TimeZone,
	})
	if err != nil {
		return resp, err
	}

	// 累加所有日期的工作時數
	totalHours := decimal.Zero
	for _, item := range dailyHours {
		totalHours = totalHours.Add(item.Hours)
	}
	resp.TotalWorkingHours = totalHours

	return resp, nil
}

// endregion

// region ---------------------------------------------------- 工作時間統計 ----------------------------------------------------

type WorkingHoursRequest struct {
	UserId    uint64 `form:"-" json:"-"`                                       // 用戶ID
	BeginDate string `form:"beginDate" binding:"required,datetime=2006-01-02"` // 開始日期
	EndDate   string `form:"endDate" binding:"required,datetime=2006-01-02"`   // 結束日期
	TimeZone  string `form:"timeZone" binding:"required,timezone"`             // 時區
}

type DailyHoursItem struct {
	Date  string          `json:"date" swaggertype:"string"` // 日期
	Hours decimal.Decimal `json:"hours"`                     // 工作時數
}

func (s *professionalDashboardService) WorkingHoursAnalytics(db *gorm.DB, req WorkingHoursRequest) ([]DailyHoursItem, error) {
	var resp []DailyHoursItem

	// 解析時區
	tz, err := time.LoadLocation(req.TimeZone)
	if err != nil {
		return resp, err
	}

	// 解析開始和結束日期
	startTime, err := time.ParseInLocation(xtool.DateDayA, req.BeginDate, tz)
	if err != nil {
		return resp, err
	}
	endTime, err := time.ParseInLocation(xtool.DateDayA, req.EndDate, tz)
	if err != nil {
		return resp, err
	}
	// 結束時間設為當天的23:59:59
	endTime = endTime.Add(23*time.Hour + 59*time.Minute + 59*time.Second)

	// 轉換為UTC時間用於資料庫查詢
	startTimeUTC := startTime.UTC()
	endTimeUTC := endTime.UTC()

	// 查詢所有與時間範圍有重疊的工作班次
	type JobShiftData struct {
		BeginTime time.Time       `json:"beginTime"`
		EndTime   time.Time       `json:"endTime"`
		Duration  decimal.Decimal `json:"duration"`
	}

	var shifts []JobShiftData
	shiftQuery := db.Table("job_shift js").
		Joins("JOIN job j ON js.job_id = j.id").
		Joins("JOIN job_application ja ON ja.job_id = j.id").
		Where("ja.user_id = ?", req.UserId).
		Where("ja.status = ?", model.JobApplicationStatusAccept).
		Where("j.end_time >= ? AND j.end_time <= ?", startTimeUTC, endTimeUTC). // 根據job.end_time判斷已完成工作
		Select([]string{
			"js.begin_time",
			"js.end_time",
			"js.duration",
		}).
		Order("js.begin_time ASC")

	if err = shiftQuery.Scan(&shifts).Error; err != nil {
		return resp, err
	}

	// 創建每日工作時數映射（以分鐘為單位）
	dailyMinutesMap := make(map[string]decimal.Decimal)

	// 處理每個班次，按天分割工作時數
	for _, shift := range shifts {
		// 轉換到指定時區
		shiftStart := shift.BeginTime.In(tz)
		shiftEnd := shift.EndTime.In(tz)

		// 計算班次的總時長（分鐘）
		totalDuration := shiftEnd.Sub(shiftStart)
		totalMinutes := decimal.NewFromFloat(totalDuration.Minutes())

		// 如果duration為0(分鐘)，使用計算出的時長
		actualMinutes := shift.Duration.Mul(decimal.NewFromInt(60))

		// 按天分割工作時數
		currentTime := shiftStart
		for currentTime.Before(shiftEnd) {
			// 當天的結束時間（24:00:00，即下一天的00:00:00）
			dayEnd := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()+1, 0, 0, 0, 0, tz)
			if dayEnd.After(shiftEnd) {
				dayEnd = shiftEnd
			}
			// 計算在當天的工作時長（分鐘）
			dayDuration := dayEnd.Sub(currentTime)
			dayMinutes := decimal.NewFromFloat(dayDuration.Minutes())
			// 按比例分配actual_minutes
			if !totalMinutes.IsZero() {
				proportionalMinutes := actualMinutes.Mul(dayMinutes).Div(totalMinutes)
				dateStr := currentTime.Format(xtool.DateDayA)
				dailyMinutesMap[dateStr] = dailyMinutesMap[dateStr].Add(proportionalMinutes)
			}
			// 移動到下一天的開始
			currentTime = dayEnd
		}
	}

	// 使用 GenerateRange 生成完整的日期數組
	dateRange, err := GenerateRange(req.BeginDate, req.EndDate)
	if err != nil {
		return resp, err
	}

	// 生成最終結果（將分鐘轉換為小時）
	for _, dateStr := range dateRange {
		minutes := dailyMinutesMap[dateStr]
		// 將分鐘轉換為小時（保留精度）
		hours := minutes.Div(decimal.NewFromInt(60)).Round(2)
		resp = append(resp, DailyHoursItem{
			Date:  dateStr,
			Hours: hours,
		})
	}

	return resp, nil
}

// endregion
