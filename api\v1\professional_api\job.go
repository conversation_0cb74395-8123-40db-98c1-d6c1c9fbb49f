package professional_api

import (
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type JobController struct{}

func NewJobController() JobController {
	return JobController{}
}

// @Tags Job
// @Summary 搜索工作
// @Description 根據地理位置和半徑搜索職位，計算距離並可按距離排序
// @Router /v1/professional/jobs/actions/search [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.JobSearchForProfessionalReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Param sortingKey query string false "hourlyRate 時薪,duration 總時長,distance 距離, beginTime 开始时间"
// @Param sortingType query string false "a=順序,d=倒序"
// @Success 200 {object} []services.JobSearchForProfessionalResp "Success"
func (con JobController) Search(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobSearchForProfessionalReq
	var pageSet xresp.PageSet
	var sortSet xresp.SortingSet
	_ = c.ShouldBindQuery(&pageSet)
	_ = c.ShouldBindQuery(&sortSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.ReqUserId = nc.GetJWTUserId()
		resp, err := services.JobService.JobSearchForProfessional(db, req, &pageSet, sortSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Job
// @Summary 查詢工作職位詳情
// @Description 查詢工作職位詳情
// @Router /v1/professional/jobs/actions/inquire [GET]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.ProfessionalJobInquireReq true "parameter"
// @Success 200 {object} services.ProfessionalJobInquireResp "Success"
func (con JobController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalJobInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.JobService.CheckIdExistForProfessional(db, req.JobId, nc.GetJWTUserId())
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		req.ReqUserId = nc.GetJWTUserId()
		resp, err := services.JobService.InquireForProfessional(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Job
// @Summary 申請工作職位
// @Description 專業人員申請工作職位
// @Router /v1/professional/jobs/actions/apply [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalJobApplyReq true "parameter"
// @Success 200 {object} services.ProfessionalJobApplyResp "Success"
func (con JobController) Apply(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalJobApplyReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		// 檢查工作ID是否存在
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.JobService.CheckIdExistForProfessional(db, req.JobId, nc.GetJWTUserId())
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		var alertMsg []string
		alerter := xapp.NewCK(c)
		alerter.
			Run(func() (bool, i18n.Message, error) {
				return services.JobService.CheckJobCanApplyByProfessional(db, req.JobId, nc.GetJWTUserId())
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.TrainingService.CheckProfessionalModuleNotCompleted(db, nc.GetJWTUserId())
			})
		alertMsg, err = alerter.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(alertMsg) > 0 {
			nc.AlertResponse(alertMsg)
			return
		}

		// 執行申請工作
		tx := db.Begin()
		req.ReqUserId = nc.GetJWTUserId()
		resp, err := services.JobService.ApplyForProfessional(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()

		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
