package model

import (
	"github.com/Norray/xrocket/xmodel"
	"time"
)

const (
	JobFeedbackRatingExceedsStandard     = "EXCEEDS_STANDARD"     // 超出標準
	JobFeedbackRatingProficient          = "PROFICIENT"           // 熟練
	JobFeedbackRatingRequiresDevelopment = "REQUIRES_DEVELOPMENT" // 需要發展

	JobFeedbackLikeYes = "Y" // 是
	JobFeedbackLikeNo  = "N" // 否 創建時加入黑名單
)

// 機構工作反饋
type JobFeedback struct {
	Id                  uint64    `json:"id" gorm:"primary_key"`                                      // 主鍵
	FacilityId          uint64    `json:"facilityId" gorm:"index:facility_idx;not null"`              // 機構Id
	ProfessionalId      uint64    `json:"professionalId" gorm:"index:professional_idx;not null"`      // 專業人士Id
	JobId               uint64    `json:"jobId" gorm:"index:job_idx;not null"`                        // 工作Id
	JobApplicationId    uint64    `json:"jobApplicationId" gorm:"index:job_application_idx;not null"` // 工作申請Id
	AttitudeTowardsWork string    `json:"attitudeTowardsWork" gorm:"type:varchar(32);not null"`       // 態度
	Skills              string    `json:"skills" gorm:"type:varchar(32);not null"`                    // 技能
	Communication       string    `json:"communication" gorm:"type:varchar(32);not null"`             // 溝通
	Cooperation         string    `json:"cooperation" gorm:"type:varchar(32);not null"`               // 合作
	TimeManagement      string    `json:"timeManagement" gorm:"type:varchar(32);not null"`            // 時間管理
	OverallDemeanour    string    `json:"overallDemeanour" gorm:"type:varchar(32);not null"`          // 整體表現
	Comment             string    `json:"comment" gorm:"type:varchar(1024);not null"`                 // 評論內容
	Rating              int       `json:"rating" gorm:"type:int;not null"`                            // 評分(1-5)
	Like                string    `json:"like" gorm:"type:varchar(1);not null"`                       // 是否願意繼續錄用該專業人士 Y N
	CreateTime          time.Time `json:"createTime" gorm:"type:datetime(0);not null"`                // 創建時間
	CreateUserId        uint64    `json:"createUserId" gorm:"index:create_user_idx;not null"`         // 創建者Id(機構)
	xmodel.Model
}

func (JobFeedback) TableName() string {
	return "job_feedback"
}

func (JobFeedback) SwaggerDescription() string {
	return "機構工作反饋"
}
