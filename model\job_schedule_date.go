package model

import (
	"github.com/Norray/xrocket/xmodel"
)

const (
	JobScheduleDateStatusPending   = "PENDING"   // 待發佈
	JobScheduleDateStatusPublished = "PUBLISHED" // 已發佈
)

// JobScheduleDate 工作排程日期
type JobScheduleDate struct {
	Id            uint64 `json:"id" gorm:"primary_key"`                                // 主鍵
	FacilityId    uint64 `json:"facilityId" gorm:"index:facility_idx;not null"`        // 機構Id
	JobScheduleId uint64 `json:"jobScheduleId" gorm:"index:job_schedule_idx;not null"` // 工作排程Id
	JobId         uint64 `json:"jobId" gorm:"index:job_idx;not null"`                  // 工作Id 創建工作時生成
	Date          string `json:"date" gorm:"type:varchar(10);not null"`                // 日期(YYYY-MM-DD)
	Status        string `json:"status" gorm:"type:varchar(32);not null"`              // 狀態 PENDING PUBLISHED
	xmodel.Model
}

func (JobScheduleDate) TableName() string {
	return "job_schedule_date"
}

func (JobScheduleDate) SwaggerDescription() string {
	return "工作排程日期"
}
