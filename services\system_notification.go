package services

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xmodel"
	"gorm.io/gorm"

	"github.com/Norray/medic-crew/model"
)

var SystemNotificationService = new(systemNotificationService)

// 通知文字常量
const (
	// Professional Job 通知文字
	MsgProfessionalJobNewAvailableTitle   = "New Job Available"
	MsgProfessionalJobNewAvailableContent = "A new suitable job is available for you. Please have a look."

	MsgProfessionalJobInvitationTitle   = "New Job Invitation"
	MsgProfessionalJobInvitationContent = "You have received a new job invitation. Please take a look and respond."

	MsgProfessionalJobAcceptedTitle   = "Job Invitation Accepted"
	MsgProfessionalJobAcceptedContent = "The job invitation has been accepted and added to your calendar."

	MsgProfessionalJobCancelledTitle   = "Job Cancelled"
	MsgProfessionalJobCancelledContent = "This job has been cancelled. Please take note and adjust your schedule accordingly."

	MsgProfessionalJobCompensationTitle   = "Job Cancellation Compensation"
	MsgProfessionalJobCompensationContent = "The job was cancelled, and a compensation invoice has been generated. Please review and confirm."

	// Professional Calendar 通知文字
	MsgProfessionalCalendar24HourTitle   = "Upcoming Shift Reminder"
	MsgProfessionalCalendar24HourContent = "You have a job in 24 hours. Please take note of the time."

	MsgProfessionalCalendar2HourTitle   = "Shift Starting Soon"
	MsgProfessionalCalendar2HourContent = "Your shift starts in 2 hours. Please be on time."

	// Professional Billing 通知文字
	MsgProfessionalBillingConfirmationNoteGenerateTitle   = "Generate Confirmation Note"
	MsgProfessionalBillingConfirmationNoteGenerateContent = "You have completed your shift. Please generate your confirmation note."

	MsgProfessionalBillingConfirmationNoteApprovedTitle   = "Confirmation Note Approved"
	MsgProfessionalBillingConfirmationNoteApprovedContent = "Your confirmation note has been approved, and an invoice has been automatically generated for you."

	MsgProfessionalBillingConfirmationNoteRejectedTitle   = "Confirmation Note Rejected"
	MsgProfessionalBillingConfirmationNoteRejectedContent = "Your confirmation note was not approved. Please review the reason and resubmit."

	// Professional Profile 通知文字
	MsgProfessionalProfileApprovedTitle   = "Profile Approved"
	MsgProfessionalProfileApprovedContent = "Your profile has been approved. Please complete the training to be eligible for jobs."

	MsgProfessionalProfileRejectedTitle   = "Profile Rejected"
	MsgProfessionalProfileRejectedContent = "Your profile was not approved. Please review the reason and make the necessary updates."

	MsgProfessionalProfileNeedSuperTitle   = "Complete Superannuation Details"
	MsgProfessionalProfileNeedSuperContent = "Your profile has been approved. Please complete your Superannuation details."

	MsgProfessionalProfileDocumentExpireTitle   = "Document Expiring Soon"
	MsgProfessionalProfileDocumentExpireContent = "%s will expire within 30 days. Please update it promptly."

	MsgProfessionalProfileReferenceDeclinedTitle   = "Reference Declined"
	MsgProfessionalProfileReferenceDeclinedContent = "Your referee %s has declined to provide the required information. Please contact them or select another referee."

	// Facility Job 通知文字
	MsgFacilityJobNeedConfirmTitle   = "Confirm Professional Assignment"
	MsgFacilityJobNeedConfirmContent = "Only 24 hours remain until the shift starts. Please confirm the assigned professional as soon as possible."

	MsgFacilityJobNoApplicationTitle   = "No Applications Received"
	MsgFacilityJobNoApplicationContent = "Only 2 hours remain until the shift starts and no applications have been received. If no applications are received within the next hour, the job will be automatically cancelled in 1 hour."

	MsgFacilityJobAutoCancelTitle   = "Job Automatically Cancelled"
	MsgFacilityJobAutoCancelContent = "Only 1 hour remains until the shift starts and no applications were received. The job has been automatically cancelled."

	MsgFacilityJobAcceptedTitle   = "Job Invitation Accepted"
	MsgFacilityJobAcceptedContent = "The job invitation has been accepted and added to your calendar."

	MsgFacilityJobDeclineTitle   = "Job Invitation Declined"
	MsgFacilityJobDeclineContent = "The job invitation has been declined by %s. Please contact other applicants."

	MsgFacilityJobInvitationExpiredTitle   = "Job Invitation Expired"
	MsgFacilityJobInvitationExpiredContent = "The job invitation has expired without being accepted. You may contact this applicant again, or reach out to other applicants."

	MsgFacilityJobCancelledTitle   = "Job Cancelled by Professional"
	MsgFacilityJobCancelledContent = "The job has been cancelled by the professional. Please adjust your arrangements accordingly."

	// Facility Calendar 通知文字
	MsgFacilityCalendar24HourTitle   = "Upcoming Shift Reminder"
	MsgFacilityCalendar24HourContent = "Your shift starts in 24 hours. Please confirm the arrangements."

	// Facility Billing 通知文字
	MsgFacilityBillingConfirmationNoteReceivedTitle   = "New Confirmation Note"
	MsgFacilityBillingConfirmationNoteReceivedContent = "You have received a new confirmation note. Please review it."

	MsgFacilityBillingInvoiceReceivedTitle   = "New Invoice"
	MsgFacilityBillingInvoiceReceivedContent = "You have received a new invoice. Please review and process it."

	// Facility Information 通知文字
	MsgFacilityInformationApprovedTitle   = "Information Approved"
	MsgFacilityInformationApprovedContent = "Your information has been approved. You can now post new jobs."

	MsgFacilityInformationRejectedTitle   = "Information Rejected"
	MsgFacilityInformationRejectedContent = "Your information was not approved. Please review the reason and make the necessary updates."
)

// systemNotificationService 系統通知服務
type systemNotificationService struct{}

// region ---------------------------------------------------- Professional Job Notifications 專業人員工作通知 ----------------------------------------------------

// 創建專業人員新工作可用通知請求
type CreateProfessionalJobNewAvailableReq struct {
	UserId       uint64 `json:"userId"`       // 用戶ID
	JobId        uint64 `json:"jobId"`        // 工作ID
	JobTitle     string `json:"jobTitle"`     // 工作標題
	FacilityName string `json:"facilityName"` // 機構名稱
	StartTime    string `json:"startTime"`    // 開始時間
}

// 創建專業人員新工作可用通知
func (s *systemNotificationService) CreateProfessionalJobNewAvailable(db *gorm.DB, req CreateProfessionalJobNewAvailableReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"jobTitle":     req.JobTitle,
		"facilityName": req.FacilityName,
		"startTime":    req.StartTime,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalJobNewAvailable,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            MsgProfessionalJobNewAvailableTitle,
		Content:          MsgProfessionalJobNewAvailableContent,
		RelatedId:        req.JobId,
		RelatedType:      model.SystemNotificationRelatedTypeJob,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// 創建專業人員工作邀請通知請求
type CreateProfessionalJobInvitationReq struct {
	UserId           uint64 `json:"userId"`           // 用戶ID
	JobId            uint64 `json:"jobId"`            // 工作ID
	JobApplicationId uint64 `json:"jobApplicationId"` // 工作申請ID
	JobTitle         string `json:"jobTitle"`         // 工作標題
	FacilityName     string `json:"facilityName"`     // 機構名稱
	CreatorUserId    uint64 `json:"creatorUserId"`    // 創建用戶ID
}

// 創建專業人員工作邀請通知
func (s *systemNotificationService) CreateProfessionalJobInvitation(db *gorm.DB, req CreateProfessionalJobInvitationReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"jobId":        req.JobId,
		"facilityName": req.FacilityName,
		"jobTitle":     req.JobTitle,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalJobInvitation,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgProfessionalJobInvitationTitle,
		Content:          MsgProfessionalJobInvitationContent,
		RelatedId:        req.JobApplicationId,
		RelatedType:      model.SystemNotificationRelatedTypeJobApplication,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// 創建專業人員工作接受通知請求
type CreateProfessionalJobAcceptedReq struct {
	UserId       uint64 `json:"userId"`       // 用戶ID
	JobId        uint64 `json:"jobId"`        // 工作ID
	JobTitle     string `json:"jobTitle"`     // 工作標題
	FacilityName string `json:"facilityName"` // 機構名稱
}

// 創建專業人員工作接受通知
func (s *systemNotificationService) CreateProfessionalJobAccepted(db *gorm.DB, req CreateProfessionalJobAcceptedReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"jobId":        req.JobId,
		"facilityName": req.FacilityName,
		"jobTitle":     req.JobTitle,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalJobAccepted,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            MsgProfessionalJobAcceptedTitle,
		Content:          MsgProfessionalJobAcceptedContent,
		RelatedId:        req.JobId,
		RelatedType:      model.SystemNotificationRelatedTypeJob,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// 創建專業人員工作取消通知請求
type CreateProfessionalJobCancelledReq struct {
	UserId             uint64 `json:"userId"`             // 用戶ID
	JobId              uint64 `json:"jobId"`              // 工作ID
	JobTitle           string `json:"jobTitle"`           // 工作標題
	FacilityName       string `json:"facilityName"`       // 機構名稱
	CancellationReason string `json:"cancellationReason"` // 取消原因
	CreatorUserId      uint64 `json:"creatorUserId"`      // 創建用戶ID
}

// 創建專業人員工作取消通知
func (s *systemNotificationService) CreateProfessionalJobCancelled(db *gorm.DB, req CreateProfessionalJobCancelledReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"jobId":              req.JobId,
		"facilityName":       req.FacilityName,
		"jobTitle":           req.JobTitle,
		"cancellationReason": req.CancellationReason,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalJobCancelled,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgProfessionalJobCancelledTitle,
		Content:          MsgProfessionalJobCancelledContent,
		RelatedId:        req.JobId,
		RelatedType:      model.SystemNotificationRelatedTypeJob,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// 創建專業人員工作賠付通知請求
type CreateProfessionalJobCompensationReq struct {
	UserId             uint64  `json:"userId"`             // 用戶ID
	JobId              uint64  `json:"jobId"`              // 工作ID
	ConfirmationNoteId uint64  `json:"confirmationNoteId"` // 確認單ID
	FacilityName       string  `json:"facilityName"`       // 機構名稱
	CompensationAmount float64 `json:"compensationAmount"` // 賠付金額
}

// 創建專業人員工作賠付通知
func (s *systemNotificationService) CreateProfessionalJobCompensation(db *gorm.DB, req CreateProfessionalJobCompensationReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"jobId":              req.JobId,
		"facilityName":       req.FacilityName,
		"compensationAmount": req.CompensationAmount,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalJobCompensation,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgProfessionalJobCompensationTitle,
		Content:          MsgProfessionalJobCompensationContent,
		RelatedId:        req.ConfirmationNoteId,
		RelatedType:      model.SystemNotificationRelatedTypeConfirmationNote,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// endregion ---------------------------------------------------- Professional Job Notifications 專業人員工作通知 ----------------------------------------------------

// region ---------------------------------------------------- Professional Calendar Notifications 專業人員日程通知 ----------------------------------------------------

// 創建專業人員24小時提醒通知請求
type CreateProfessionalCalendar24HourReq struct {
	UserId       uint64 `json:"userId"`       // 用戶ID
	JobId        uint64 `json:"jobId"`        // 工作ID
	JobTitle     string `json:"jobTitle"`     // 工作標題
	FacilityName string `json:"facilityName"` // 機構名稱
	StartTime    string `json:"startTime"`    // 開始時間
}

// 創建專業人員24小時提醒通知
func (s *systemNotificationService) CreateProfessionalCalendar24Hour(db *gorm.DB, req CreateProfessionalCalendar24HourReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"jobId":        req.JobId,
		"facilityName": req.FacilityName,
		"jobTitle":     req.JobTitle,
		"startTime":    req.StartTime,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalCalendar24Hour,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            MsgProfessionalCalendar24HourTitle,
		Content:          MsgProfessionalCalendar24HourContent,
		RelatedId:        req.JobId,
		RelatedType:      model.SystemNotificationRelatedTypeJob,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// 創建專業人員2小時提醒通知請求
type CreateProfessionalCalendar2HourReq struct {
	UserId       uint64 `json:"userId"`       // 用戶ID
	JobId        uint64 `json:"jobId"`        // 工作ID
	JobTitle     string `json:"jobTitle"`     // 工作標題
	FacilityName string `json:"facilityName"` // 機構名稱
	StartTime    string `json:"startTime"`    // 開始時間
}

// 創建專業人員2小時提醒通知
func (s *systemNotificationService) CreateProfessionalCalendar2Hour(db *gorm.DB, req CreateProfessionalCalendar2HourReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"jobId":        req.JobId,
		"facilityName": req.FacilityName,
		"jobTitle":     req.JobTitle,
		"startTime":    req.StartTime,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalCalendar2Hour,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgProfessionalCalendar2HourTitle,
		Content:          MsgProfessionalCalendar2HourContent,
		RelatedId:        req.JobId,
		RelatedType:      model.SystemNotificationRelatedTypeJob,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// endregion ---------------------------------------------------- Professional Calendar Notifications 專業人員日程通知 ----------------------------------------------------

// region ---------------------------------------------------- Professional Billing Notifications 專業人員帳單通知 ----------------------------------------------------

// 創建專業人員生成確認單通知請求
type CreateProfessionalBillingConfirmationNoteGenerateReq struct {
	UserId       uint64 `json:"userId"`       // 用戶ID
	JobId        uint64 `json:"jobId"`        // 工作ID
	JobTitle     string `json:"jobTitle"`     // 工作標題
	FacilityName string `json:"facilityName"` // 機構名稱
}

// 創建專業人員生成確認單通知
func (s *systemNotificationService) CreateProfessionalBillingConfirmationNoteGenerate(db *gorm.DB, req CreateProfessionalBillingConfirmationNoteGenerateReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"jobId":        req.JobId,
		"facilityName": req.FacilityName,
		"jobTitle":     req.JobTitle,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalBillingConfirmationNoteGenerate,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            MsgProfessionalBillingConfirmationNoteGenerateTitle,
		Content:          MsgProfessionalBillingConfirmationNoteGenerateContent,
		RelatedId:        req.JobId,
		RelatedType:      model.SystemNotificationRelatedTypeJob,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// 創建專業人員確認單通過通知請求
type CreateProfessionalBillingConfirmationNoteApprovedReq struct {
	UserId             uint64  `json:"userId"`             // 用戶ID
	JobId              uint64  `json:"jobId"`              // 工作ID
	ConfirmationNoteId uint64  `json:"confirmationNoteId"` // 確認單ID
	InvoiceId          uint64  `json:"invoiceId"`          // 發票ID
	FacilityName       string  `json:"facilityName"`       // 機構名稱
	Amount             float64 `json:"amount"`             // 金額
	CreatorUserId      uint64  `json:"creatorUserId"`      // 創建用戶ID
}

// 創建專業人員確認單通過通知
func (s *systemNotificationService) CreateProfessionalBillingConfirmationNoteApproved(db *gorm.DB, req CreateProfessionalBillingConfirmationNoteApprovedReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"jobId":        req.JobId,
		"facilityName": req.FacilityName,
		"invoiceId":    req.InvoiceId,
		"amount":       req.Amount,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalBillingConfirmationNoteApproved,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            MsgProfessionalBillingConfirmationNoteApprovedTitle,
		Content:          MsgProfessionalBillingConfirmationNoteApprovedContent,
		RelatedId:        req.ConfirmationNoteId,
		RelatedType:      model.SystemNotificationRelatedTypeConfirmationNote,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// 創建專業人員確認單拒絕通知請求
type CreateProfessionalBillingConfirmationNoteRejectedReq struct {
	UserId             uint64 `json:"userId"`             // 用戶ID
	JobId              uint64 `json:"jobId"`              // 工作ID
	ConfirmationNoteId uint64 `json:"confirmationNoteId"` // 確認單ID
	FacilityName       string `json:"facilityName"`       // 機構名稱
	RejectionReason    string `json:"rejectionReason"`    // 拒絕原因
	CreatorUserId      uint64 `json:"creatorUserId"`      // 創建用戶ID
}

// 創建專業人員確認單拒絕通知
func (s *systemNotificationService) CreateProfessionalBillingConfirmationNoteRejected(db *gorm.DB, req CreateProfessionalBillingConfirmationNoteRejectedReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"jobId":           req.JobId,
		"facilityName":    req.FacilityName,
		"rejectionReason": req.RejectionReason,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalBillingConfirmationNoteRejected,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgProfessionalBillingConfirmationNoteRejectedTitle,
		Content:          MsgProfessionalBillingConfirmationNoteRejectedContent,
		RelatedId:        req.ConfirmationNoteId,
		RelatedType:      model.SystemNotificationRelatedTypeConfirmationNote,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// endregion ---------------------------------------------------- Professional Billing Notifications 專業人員帳單通知 ----------------------------------------------------

// region ---------------------------------------------------- Professional Profile Notifications 專業人員個人資料通知 ----------------------------------------------------

// 創建專業人員資料通過通知請求
type CreateProfessionalProfileApprovedReq struct {
	UserId          uint64 `json:"userId"`          // 用戶ID
	ProfileId       uint64 `json:"profileId"`       // 個人資料ID
	TrainingModules int    `json:"trainingModules"` // 培訓模組數量
	CreatorUserId   uint64 `json:"creatorUserId"`   // 創建用戶ID
}

// 創建專業人員資料通過通知
func (s *systemNotificationService) CreateProfessionalProfileApproved(db *gorm.DB, req CreateProfessionalProfileApprovedReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"trainingModules": req.TrainingModules,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalProfileApproved,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            MsgProfessionalProfileApprovedTitle,
		Content:          MsgProfessionalProfileApprovedContent,
		RelatedId:        req.ProfileId,
		RelatedType:      model.SystemNotificationRelatedTypeProfessionalProfile,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// 創建專業人員資料駁回通知請求
type CreateProfessionalProfileRejectedReq struct {
	UserId          uint64 `json:"userId"`          // 用戶ID
	ProfileId       uint64 `json:"profileId"`       // 個人資料ID
	RejectionReason string `json:"rejectionReason"` // 駁回原因
	CreatorUserId   uint64 `json:"creatorUserId"`   // 創建用戶ID
}

// 創建專業人員資料駁回通知
func (s *systemNotificationService) CreateProfessionalProfileRejected(db *gorm.DB, req CreateProfessionalProfileRejectedReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"rejectionReason": req.RejectionReason,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalProfileRejected,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgProfessionalProfileRejectedTitle,
		Content:          MsgProfessionalProfileRejectedContent,
		RelatedId:        req.ProfileId,
		RelatedType:      model.SystemNotificationRelatedTypeProfessionalProfile,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// 創建專業人員需要完善super資料通知請求
type CreateProfessionalProfileNeedSuperReq struct {
	UserId           uint64 `json:"userId"`           // 用戶ID
	SuperannuationId uint64 `json:"superannuationId"` // 養老金ID
	AbnType          string `json:"abnType"`          // ABN類型
	CreatorUserId    uint64 `json:"creatorUserId"`    // 創建用戶ID
}

// 創建專業人員需要完善super資料通知
func (s *systemNotificationService) CreateProfessionalProfileNeedSuper(db *gorm.DB, req CreateProfessionalProfileNeedSuperReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"abnType": req.AbnType,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalProfileNeedSuper,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            MsgProfessionalProfileNeedSuperTitle,
		Content:          MsgProfessionalProfileNeedSuperContent,
		RelatedId:        req.SuperannuationId,
		RelatedType:      model.SystemNotificationRelatedTypeProfessionalSuperannuation,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// 創建專業人員文件即將到期通知請求
type CreateProfessionalProfileDocumentExpireReq struct {
	UserId       uint64 `json:"userId"`       // 用戶ID
	FileId       uint64 `json:"fileId"`       // 文件ID
	DocumentType string `json:"documentType"` // 文件類型
	ExpiryDate   string `json:"expiryDate"`   // 到期日期
}

// 創建專業人員文件即將到期通知
func (s *systemNotificationService) CreateProfessionalProfileDocumentExpire(db *gorm.DB, req CreateProfessionalProfileDocumentExpireReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"documentType": req.DocumentType,
		"expiryDate":   req.ExpiryDate,
	})

	// 創建通知對象，使用格式化文字
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalProfileDocumentExpire,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgProfessionalProfileDocumentExpireTitle,
		Content:          fmt.Sprintf(MsgProfessionalProfileDocumentExpireContent, req.DocumentType),
		RelatedId:        req.FileId,
		RelatedType:      model.SystemNotificationRelatedTypeProfessionalFile,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// 創建專業人員推薦人拒絕通知請求
type CreateProfessionalProfileReferenceDeclinedReq struct {
	UserId       uint64 `json:"userId"`       // 用戶ID
	ReferenceId  uint64 `json:"referenceId"`  // 推薦人ID
	RefereeName  string `json:"refereeName"`  // 推薦人姓名
	RefereeEmail string `json:"refereeEmail"` // 推薦人郵箱
}

// 創建專業人員推薦人拒絕通知
func (s *systemNotificationService) CreateProfessionalProfileReferenceDeclined(db *gorm.DB, req CreateProfessionalProfileReferenceDeclinedReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"refereeName":  req.RefereeName,
		"refereeEmail": req.RefereeEmail,
	})

	// 創建通知對象，使用格式化文字
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalProfileReferenceDeclined,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgProfessionalProfileReferenceDeclinedTitle,
		Content:          fmt.Sprintf(MsgProfessionalProfileReferenceDeclinedContent, req.RefereeName),
		RelatedId:        req.ReferenceId,
		RelatedType:      model.SystemNotificationRelatedTypeProfessionalReference,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// endregion ---------------------------------------------------- Professional Profile Notifications 專業人員個人資料通知 ----------------------------------------------------

// region ---------------------------------------------------- Facility Job Notifications 機構工作通知 ----------------------------------------------------

// 創建機構工作需要確認通知請求
type CreateFacilityJobNeedConfirmReq struct {
	UserId            uint64 `json:"userId"`            // 用戶ID
	JobId             uint64 `json:"jobId"`             // 工作ID
	JobTitle          string `json:"jobTitle"`          // 工作標題
	StartTime         string `json:"startTime"`         // 開始時間
	ApplicationsCount int    `json:"applicationsCount"` // 申請數量
}

// 創建機構工作需要確認通知
func (s *systemNotificationService) CreateFacilityJobNeedConfirm(db *gorm.DB, req CreateFacilityJobNeedConfirmReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"jobTitle":          req.JobTitle,
		"startTime":         req.StartTime,
		"applicationsCount": req.ApplicationsCount,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeFacilityJobNeedConfirm,
		TargetType:       model.SystemNotificationTargetTypeFacility,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgFacilityJobNeedConfirmTitle,
		Content:          MsgFacilityJobNeedConfirmContent,
		RelatedId:        req.JobId,
		RelatedType:      model.SystemNotificationRelatedTypeJob,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// 創建機構工作無申請通知請求
type CreateFacilityJobNoApplicationReq struct {
	UserId    uint64 `json:"userId"`    // 用戶ID
	JobId     uint64 `json:"jobId"`     // 工作ID
	JobTitle  string `json:"jobTitle"`  // 工作標題
	StartTime string `json:"startTime"` // 開始時間
}

// 創建機構工作無申請通知
func (s *systemNotificationService) CreateFacilityJobNoApplication(db *gorm.DB, req CreateFacilityJobNoApplicationReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"jobTitle":  req.JobTitle,
		"startTime": req.StartTime,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeFacilityJobNoApplication,
		TargetType:       model.SystemNotificationTargetTypeFacility,
		Priority:         model.SystemNotificationPriorityUrgent,
		Title:            MsgFacilityJobNoApplicationTitle,
		Content:          MsgFacilityJobNoApplicationContent,
		RelatedId:        req.JobId,
		RelatedType:      model.SystemNotificationRelatedTypeJob,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// 創建機構工作自動取消通知請求
type CreateFacilityJobAutoCancelReq struct {
	UserId    uint64 `json:"userId"`    // 用戶ID
	JobId     uint64 `json:"jobId"`     // 工作ID
	JobTitle  string `json:"jobTitle"`  // 工作標題
	StartTime string `json:"startTime"` // 開始時間
}

// 創建機構工作自動取消通知
func (s *systemNotificationService) CreateFacilityJobAutoCancel(db *gorm.DB, req CreateFacilityJobAutoCancelReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"jobTitle":  req.JobTitle,
		"startTime": req.StartTime,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeFacilityJobAutoCancel,
		TargetType:       model.SystemNotificationTargetTypeFacility,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgFacilityJobAutoCancelTitle,
		Content:          MsgFacilityJobAutoCancelContent,
		RelatedId:        req.JobId,
		RelatedType:      model.SystemNotificationRelatedTypeJob,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// 創建機構工作接受通知請求
type CreateFacilityJobAcceptedReq struct {
	UserId           uint64 `json:"userId"`           // 用戶ID
	JobId            uint64 `json:"jobId"`            // 工作ID
	JobTitle         string `json:"jobTitle"`         // 工作標題
	ProfessionalName string `json:"professionalName"` // 專業人員姓名
	CreatorUserId    uint64 `json:"creatorUserId"`    // 創建用戶ID
}

// 創建機構工作接受通知
func (s *systemNotificationService) CreateFacilityJobAccepted(db *gorm.DB, req CreateFacilityJobAcceptedReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"jobId":            req.JobId,
		"professionalName": req.ProfessionalName,
		"jobTitle":         req.JobTitle,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeFacilityJobAccepted,
		TargetType:       model.SystemNotificationTargetTypeFacility,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            MsgFacilityJobAcceptedTitle,
		Content:          MsgFacilityJobAcceptedContent,
		RelatedId:        req.JobId,
		RelatedType:      model.SystemNotificationRelatedTypeJob,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// 創建機構工作拒絕通知請求
type CreateFacilityJobDeclineReq struct {
	UserId           uint64 `json:"userId"`           // 用戶ID
	JobId            uint64 `json:"jobId"`            // 工作ID
	JobApplicationId uint64 `json:"jobApplicationId"` // 工作申請ID
	JobTitle         string `json:"jobTitle"`         // 工作標題
	ProfessionalName string `json:"professionalName"` // 專業人員姓名
	DeclineReason    string `json:"declineReason"`    // 拒絕原因
	CreatorUserId    uint64 `json:"creatorUserId"`    // 創建用戶ID
}

// 創建機構工作拒絕通知
func (s *systemNotificationService) CreateFacilityJobDecline(db *gorm.DB, req CreateFacilityJobDeclineReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"jobId":            req.JobId,
		"professionalName": req.ProfessionalName,
		"jobTitle":         req.JobTitle,
		"declineReason":    req.DeclineReason,
	})

	// 創建通知對象，使用格式化文字
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeFacilityJobDecline,
		TargetType:       model.SystemNotificationTargetTypeFacility,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            MsgFacilityJobDeclineTitle,
		Content:          fmt.Sprintf(MsgFacilityJobDeclineContent, req.ProfessionalName),
		RelatedId:        req.JobApplicationId,
		RelatedType:      model.SystemNotificationRelatedTypeJobApplication,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// 創建機構工作邀請過期通知請求
type CreateFacilityJobInvitationExpiredReq struct {
	UserId           uint64 `json:"userId"`           // 用戶ID
	JobId            uint64 `json:"jobId"`            // 工作ID
	JobApplicationId uint64 `json:"jobApplicationId"` // 工作申請ID
	JobTitle         string `json:"jobTitle"`         // 工作標題
	ProfessionalName string `json:"professionalName"` // 專業人員姓名
}

// 創建機構工作邀請過期通知
func (s *systemNotificationService) CreateFacilityJobInvitationExpired(db *gorm.DB, req CreateFacilityJobInvitationExpiredReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"jobId":            req.JobId,
		"professionalName": req.ProfessionalName,
		"jobTitle":         req.JobTitle,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeFacilityJobInvitationExpired,
		TargetType:       model.SystemNotificationTargetTypeFacility,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            MsgFacilityJobInvitationExpiredTitle,
		Content:          MsgFacilityJobInvitationExpiredContent,
		RelatedId:        req.JobApplicationId,
		RelatedType:      model.SystemNotificationRelatedTypeJobApplication,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// 創建機構工作被取消通知請求
type CreateFacilityJobCancelledReq struct {
	UserId             uint64 `json:"userId"`             // 用戶ID
	JobId              uint64 `json:"jobId"`              // 工作ID
	JobTitle           string `json:"jobTitle"`           // 工作標題
	ProfessionalName   string `json:"professionalName"`   // 專業人員姓名
	CancellationReason string `json:"cancellationReason"` // 取消原因
	CreatorUserId      uint64 `json:"creatorUserId"`      // 創建用戶ID
}

// 創建機構工作被取消通知
func (s *systemNotificationService) CreateFacilityJobCancelled(db *gorm.DB, req CreateFacilityJobCancelledReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"jobId":              req.JobId,
		"professionalName":   req.ProfessionalName,
		"jobTitle":           req.JobTitle,
		"cancellationReason": req.CancellationReason,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeFacilityJobCancelled,
		TargetType:       model.SystemNotificationTargetTypeFacility,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgFacilityJobCancelledTitle,
		Content:          MsgFacilityJobCancelledContent,
		RelatedId:        req.JobId,
		RelatedType:      model.SystemNotificationRelatedTypeJob,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// endregion ---------------------------------------------------- Facility Job Notifications 機構工作通知 ----------------------------------------------------

// region ---------------------------------------------------- Facility Calendar Notifications 機構日程通知 ----------------------------------------------------

// 創建機構24小時提醒通知請求
type CreateFacilityCalendar24HourReq struct {
	UserId           uint64 `json:"userId"`           // 用戶ID
	JobId            uint64 `json:"jobId"`            // 工作ID
	JobTitle         string `json:"jobTitle"`         // 工作標題
	ProfessionalName string `json:"professionalName"` // 專業人員姓名
	StartTime        string `json:"startTime"`        // 開始時間
}

// 創建機構24小時提醒通知
func (s *systemNotificationService) CreateFacilityCalendar24Hour(db *gorm.DB, req CreateFacilityCalendar24HourReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"jobId":            req.JobId,
		"professionalName": req.ProfessionalName,
		"jobTitle":         req.JobTitle,
		"startTime":        req.StartTime,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeFacilityCalendar24Hour,
		TargetType:       model.SystemNotificationTargetTypeFacility,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            MsgFacilityCalendar24HourTitle,
		Content:          MsgFacilityCalendar24HourContent,
		RelatedId:        req.JobId,
		RelatedType:      model.SystemNotificationRelatedTypeJob,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// endregion ---------------------------------------------------- Facility Calendar Notifications 機構日程通知 ----------------------------------------------------

// region ---------------------------------------------------- Facility Billing Notifications 機構帳單通知 ----------------------------------------------------

// 創建機構收到確認單通知請求
type CreateFacilityBillingConfirmationNoteReceivedReq struct {
	UserId             uint64  `json:"userId"`             // 用戶ID
	JobId              uint64  `json:"jobId"`              // 工作ID
	ConfirmationNoteId uint64  `json:"confirmationNoteId"` // 確認單ID
	ProfessionalName   string  `json:"professionalName"`   // 專業人員姓名
	Amount             float64 `json:"amount"`             // 金額
	CreatorUserId      uint64  `json:"creatorUserId"`      // 創建用戶ID
}

// 創建機構收到確認單通知
func (s *systemNotificationService) CreateFacilityBillingConfirmationNoteReceived(db *gorm.DB, req CreateFacilityBillingConfirmationNoteReceivedReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"jobId":            req.JobId,
		"professionalName": req.ProfessionalName,
		"amount":           req.Amount,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeFacilityBillingConfirmationNoteReceived,
		TargetType:       model.SystemNotificationTargetTypeFacility,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgFacilityBillingConfirmationNoteReceivedTitle,
		Content:          MsgFacilityBillingConfirmationNoteReceivedContent,
		RelatedId:        req.ConfirmationNoteId,
		RelatedType:      model.SystemNotificationRelatedTypeConfirmationNote,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// 創建機構收到發票通知請求
type CreateFacilityBillingInvoiceReceivedReq struct {
	UserId           uint64  `json:"userId"`           // 用戶ID
	JobId            uint64  `json:"jobId"`            // 工作ID
	InvoiceId        uint64  `json:"invoiceId"`        // 發票ID
	ProfessionalName string  `json:"professionalName"` // 專業人員姓名
	Amount           float64 `json:"amount"`           // 金額
	DueDate          string  `json:"dueDate"`          // 到期日期
}

// 創建機構收到發票通知
func (s *systemNotificationService) CreateFacilityBillingInvoiceReceived(db *gorm.DB, req CreateFacilityBillingInvoiceReceivedReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"jobId":            req.JobId,
		"professionalName": req.ProfessionalName,
		"amount":           req.Amount,
		"dueDate":          req.DueDate,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeFacilityBillingInvoiceReceived,
		TargetType:       model.SystemNotificationTargetTypeFacility,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgFacilityBillingInvoiceReceivedTitle,
		Content:          MsgFacilityBillingInvoiceReceivedContent,
		RelatedId:        req.InvoiceId,
		RelatedType:      model.SystemNotificationRelatedTypeInvoice,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// endregion ---------------------------------------------------- Facility Billing Notifications 機構帳單通知 ----------------------------------------------------

// region ---------------------------------------------------- Facility Information Notifications 機構信息通知 ----------------------------------------------------

// 創建機構信息通過通知請求
type CreateFacilityInformationApprovedReq struct {
	UserId        uint64 `json:"userId"`        // 用戶ID
	ProfileId     uint64 `json:"profileId"`     // 個人資料ID
	CreatorUserId uint64 `json:"creatorUserId"` // 創建用戶ID
}

// 創建機構信息通過通知
func (s *systemNotificationService) CreateFacilityInformationApproved(db *gorm.DB, req CreateFacilityInformationApprovedReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeFacilityInformationApproved,
		TargetType:       model.SystemNotificationTargetTypeFacility,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            MsgFacilityInformationApprovedTitle,
		Content:          MsgFacilityInformationApprovedContent,
		RelatedId:        req.ProfileId,
		RelatedType:      model.SystemNotificationRelatedTypeFacilityProfile,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// 創建機構信息駁回通知請求
type CreateFacilityInformationRejectedReq struct {
	UserId          uint64 `json:"userId"`          // 用戶ID
	ProfileId       uint64 `json:"profileId"`       // 個人資料ID
	RejectionReason string `json:"rejectionReason"` // 駁回原因
	CreatorUserId   uint64 `json:"creatorUserId"`   // 創建用戶ID
}

// 創建機構信息駁回通知
func (s *systemNotificationService) CreateFacilityInformationRejected(db *gorm.DB, req CreateFacilityInformationRejectedReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"rejectionReason": req.RejectionReason,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeFacilityInformationRejected,
		TargetType:       model.SystemNotificationTargetTypeFacility,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgFacilityInformationRejectedTitle,
		Content:          MsgFacilityInformationRejectedContent,
		RelatedId:        req.ProfileId,
		RelatedType:      model.SystemNotificationRelatedTypeFacilityProfile,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now(),
	}

	return s.createNotificationForUser(db, notification, req.UserId)
}

// endregion ---------------------------------------------------- Facility Information Notifications 機構信息通知 ----------------------------------------------------

// region ---------------------------------------------------- System Notifications 系統通知 ----------------------------------------------------

// 創建系統公告通知請求
type CreateSystemAnnouncementReq struct {
	Title         string   `json:"title"`         // 標題
	Content       string   `json:"content"`       // 內容
	FeatureList   []string `json:"featureList"`   // 功能列表
	CreatorUserId uint64   `json:"creatorUserId"` // 創建用戶ID
}

// 創建系統公告通知
func (s *systemNotificationService) CreateSystemAnnouncement(db *gorm.DB, req CreateSystemAnnouncementReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"featureList": req.FeatureList,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeSystemAnnouncement,
		TargetType:       model.SystemNotificationTargetTypeAll,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            req.Title,
		Content:          req.Content,
		RelatedId:        0,
		RelatedType:      "",
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now(),
	}

	return s.createSystemNotification(db, notification)
}

// 創建維護通知請求
type CreateMaintenanceNoticeReq struct {
	Title         string `json:"title"`         // 標題
	Content       string `json:"content"`       // 內容
	StartTime     string `json:"startTime"`     // 開始時間
	EndTime       string `json:"endTime"`       // 結束時間
	CreatorUserId uint64 `json:"creatorUserId"` // 創建用戶ID
}

// 創建維護通知
func (s *systemNotificationService) CreateMaintenanceNotice(db *gorm.DB, req CreateMaintenanceNoticeReq) error {
	// 準備元數據
	metadata, _ := json.Marshal(map[string]interface{}{
		"startTime": req.StartTime,
		"endTime":   req.EndTime,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeMaintenanceNotice,
		TargetType:       model.SystemNotificationTargetTypeAll,
		Priority:         model.SystemNotificationPriorityUrgent,
		Title:            req.Title,
		Content:          req.Content,
		RelatedId:        0,
		RelatedType:      "",
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now(),
	}

	return s.createSystemNotification(db, notification)
}

// endregion ---------------------------------------------------- System Notifications 系統通知 ----------------------------------------------------

// region ---------------------------------------------------- Notification Query APIs 通知查詢API ----------------------------------------------------

// 獲取用戶通知列表請求
type GetUserNotificationListReq struct {
	UserId uint64 `form:"-" json:"-"`                         // 用戶ID，在控制器中設置
	Read   string `form:"read" binding:"omitempty,oneof=Y N"` // 已讀狀態: Y=已讀, N=未讀
}

// 用戶通知項目
type UserNotificationItem struct {
	Id               uint64 `json:"id"`               // 通知ID
	NotificationType string `json:"notificationType"` // 通知類型
	TargetType       string `json:"targetType"`       // 目標類型
	Priority         string `json:"priority"`         // 優先級
	Title            string `json:"title"`            // 通知標題
	Content          string `json:"content"`          // 通知內容
	RelatedId        uint64 `json:"relatedId"`        // 關聯業務ID
	RelatedType      string `json:"relatedType"`      // 關聯業務類型
	Metadata         string `json:"metadata"`         // 額外元數據JSON
	CreatorUserId    uint64 `json:"creatorUserId"`    // 創建用戶ID
	CreateTime       string `json:"createTime"`       // 創建時間
	Read             string `json:"read"`             // 是否已讀 Y/N
	ReadTime         string `json:"readTime"`         // 已讀時間
}

// 獲取用戶通知列表
func (s *systemNotificationService) GetUserNotificationList(db *gorm.DB, req GetUserNotificationListReq, pageSet *xresp.PageSet) ([]UserNotificationItem, error) {
	// 構建基礎查詢
	builder := db.Table("system_notification_user AS snu").
		Joins("JOIN system_notification AS sn ON sn.id = snu.system_notification_id").
		Where("snu.user_id = ?", req.UserId).
		Where("snu.deleted = ?", "N")

	// 根據已讀狀態篩選
	if req.Read != "" {
		builder = builder.Where("snu.read = ?", req.Read)
	}

	// 查詢通知列表
	var notifications []UserNotificationItem
	err := builder.
		Select([]string{
			"sn.id",
			"sn.notification_type",
			"sn.target_type",
			"sn.priority",
			"sn.title",
			"sn.content",
			"sn.related_id",
			"sn.related_type",
			"sn.metadata",
			"sn.creator_user_id",
			"sn.create_time",
			"snu.read",
			"COALESCE(snu.read_time, '') as read_time",
		}).
		Scopes(xresp.Paginate(pageSet)).
		Order("sn.create_time DESC").
		Scan(&notifications).Error

	if err != nil {
		return nil, err
	}

	return notifications, nil
}

// 標記通知為已讀請求
type MarkNotificationAsReadReq struct {
	UserId          uint64   `json:"-"`                                  // 用戶ID，在控制器中設置
	NotificationIds []uint64 `json:"notificationIds" binding:"required"` // 通知ID列表
}

// 標記通知為已讀
func (s *systemNotificationService) MarkNotificationAsRead(db *gorm.DB, req MarkNotificationAsReadReq) error {
	if len(req.NotificationIds) == 0 {
		return nil
	}

	// 更新通知為已讀狀態
	now := time.Now()
	err := db.Table("system_notification_user").
		Where("user_id = ?", req.UserId).
		Where("system_notification_id IN ?", req.NotificationIds).
		Where("deleted = ?", "N").
		Updates(map[string]interface{}{
			"read":      "Y",
			"read_time": now,
		}).Error

	return err
}

// 標記所有通知為已讀請求
type MarkAllNotificationsAsReadReq struct {
	UserId uint64 `json:"-"` // 用戶ID，在控制器中設置
}

// 標記所有通知為已讀
func (s *systemNotificationService) MarkAllNotificationsAsRead(db *gorm.DB, req MarkAllNotificationsAsReadReq) error {
	// 更新所有未讀通知為已讀狀態
	now := time.Now()
	err := db.Table("system_notification_user").
		Where("user_id = ?", req.UserId).
		Where("read = ?", "N").
		Where("deleted = ?", "N").
		Updates(map[string]interface{}{
			"read":      "Y",
			"read_time": now,
		}).Error

	return err
}

// 刪除通知請求
type DeleteNotificationReq struct {
	UserId          uint64   `json:"-"`                                  // 用戶ID，在控制器中設置
	NotificationIds []uint64 `json:"notificationIds" binding:"required"` // 通知ID列表
}

// 刪除通知（軟刪除）
func (s *systemNotificationService) DeleteNotification(db *gorm.DB, req DeleteNotificationReq) error {
	if len(req.NotificationIds) == 0 {
		return nil
	}

	// 軟刪除通知
	err := db.Table("system_notification_user").
		Where("user_id = ?", req.UserId).
		Where("system_notification_id IN ?", req.NotificationIds).
		Update("deleted", "Y").Error

	return err
}

// 獲取未讀數量請求
type GetUnreadCountReq struct {
	UserId uint64 `json:"-"` // 用戶ID，在控制器中設置
}

// 獲取未讀數量響應
type GetUnreadCountResp struct {
	UnreadCount int64 `json:"unreadCount"` // 未讀數量
}

// 獲取未讀通知數量
func (s *systemNotificationService) GetUnreadCount(db *gorm.DB, req GetUnreadCountReq) (*GetUnreadCountResp, error) {
	var unreadCount int64
	err := db.Table("system_notification_user").
		Where("user_id = ?", req.UserId).
		Where("read = ?", "N").
		Where("deleted = ?", "N").
		Count(&unreadCount).Error

	if err != nil {
		return nil, err
	}

	return &GetUnreadCountResp{
		UnreadCount: unreadCount,
	}, nil
}

// endregion ---------------------------------------------------- Notification Query APIs 通知查詢API ----------------------------------------------------

// region ---------------------------------------------------- Helper Methods 輔助方法 ----------------------------------------------------

// createNotificationForUser 為單個用戶創建通知
func (s *systemNotificationService) createNotificationForUser(db *gorm.DB, notification model.SystemNotification, userId uint64) error {
	// 創建系統通知記錄
	if err := db.Create(&notification).Error; err != nil {
		return err
	}

	// 創建用戶通知關聯記錄
	userNotification := model.SystemNotificationUser{
		UserId:               userId,
		SystemNotificationId: notification.Id,
		Read:                 "N",
		ReadTime:             nil,
		Deleted:              "N",
	}

	return db.Create(&userNotification).Error
}

// createSystemNotification 為所有用戶創建系統通知
func (s *systemNotificationService) createSystemNotification(db *gorm.DB, notification model.SystemNotification) error {
	return db.Create(&notification).Error
}

// createNotificationForUsers 批量為多個用戶創建通知
func (s *systemNotificationService) createNotificationForUsers(db *gorm.DB, notification model.SystemNotification, userIds []uint64) error {
	// 創建系統通知記錄
	if err := db.Create(&notification).Error; err != nil {
		return err
	}

	// 批量創建用戶通知關聯記錄
	var userNotifications []model.SystemNotificationUser
	for _, userId := range userIds {
		userNotifications = append(userNotifications, model.SystemNotificationUser{
			UserId:               userId,
			SystemNotificationId: notification.Id,
			Read:                 "N",
			ReadTime:             nil,
			Deleted:              "N",
		})
	}

	if len(userNotifications) > 0 {
		return db.CreateInBatches(userNotifications, 100).Error
	}

	return nil
}

// 根據用戶類型發送通知請求
type SendNotificationByUserTypeReq struct {
	Notification model.SystemNotification `json:"notification"` // 通知對象
	UserType     string                   `json:"userType"`     // 用戶類型: PROFESSIONAL, FACILITY
}

// 根據用戶類型發送通知
func (s *systemNotificationService) SendNotificationByUserType(db *gorm.DB, req SendNotificationByUserTypeReq) error {
	// 創建系統通知記錄
	if err := db.Create(&req.Notification).Error; err != nil {
		return err
	}

	// 根據用戶類型獲取用戶列表
	var userIds []uint64
	var users []xmodel.User

	switch req.UserType {
	case model.SystemNotificationTargetTypeProfessional:
		err := db.Where("user_type = ?", model.UserUserTypeProfessional).Find(&users).Error
		if err != nil {
			return err
		}
	case model.SystemNotificationTargetTypeFacility:
		err := db.Where("user_type = ?", model.UserUserTypeFacilityUser).Find(&users).Error
		if err != nil {
			return err
		}
	}

	for _, user := range users {
		userIds = append(userIds, user.Id)
	}

	// 批量創建用戶通知關聯記錄
	var userNotifications []model.SystemNotificationUser
	for _, userId := range userIds {
		userNotifications = append(userNotifications, model.SystemNotificationUser{
			UserId:               userId,
			SystemNotificationId: req.Notification.Id,
			Read:                 "N",
			ReadTime:             nil,
			Deleted:              "N",
		})
	}

	if len(userNotifications) > 0 {
		return db.CreateInBatches(userNotifications, 100).Error
	}

	return nil
}

// endregion ---------------------------------------------------- Helper Methods 輔助方法 ----------------------------------------------------
