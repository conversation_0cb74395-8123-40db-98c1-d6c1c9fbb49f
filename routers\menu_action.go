package routers

import (
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func menuActionSystemRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/system").Use(handlers...)
	{
		control := system_api.NewMenuActionController()
		r.POST("/menus/actions/create", control.CreateMenu)
		r.POST("/menus/actions/edit", control.EditMenu)
		r.GET("/menus/actions/inquire", control.InquireMenu)
		r.POST("/menus/actions/delete", control.DeleteMenu)
		r.POST("/menus/actions/update-sort", control.UpdateMenuSort)

		r.POST("/actions/actions/create", control.CreateAction)
		r.POST("/actions/actions/edit", control.EditAction)
		r.GET("/actions/actions/inquire", control.InquireAction)
		r.POST("/actions/actions/delete", control.DeleteAction)
		r.GET("/menu-actions/actions/tree", control.MenuActionTree)
	}
}
