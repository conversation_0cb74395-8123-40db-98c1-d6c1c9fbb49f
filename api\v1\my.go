package v1

import (
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xi18n"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type MyController struct{}

func NewMyController() MyController {
	return MyController{}
}

// @Tags My
// @Summary 用戶個人信息
// @Router /v1/my/user-info [GET]
// @Produce  json
// @Success 200 {object} services.SystemUserInfo "平台用戶信息"
// @Success 200 {object} services.FacilityUserInfo "機構用戶信息"
// @Success 200 {object} services.ProfessionalUserInfo "專業用戶信息"
// @Success 200 {object} services.BaseUserInfo "用戶信息 (SystemUserInfo, FacilityUserInfo, ProfessionalUserInfo)"
func (con MyController) MyUserInfo(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	db := xgorm.DB.WithContext(c)
	result, err := services.MyService.MyUserInfo(db, nc.GetJWTUserId())
	if err != nil {
		nc.ErrorResponse(nil, err)
		return
	}
	nc.OKResponse(result)
}

// @Tags My
// @Summary 進行修改密码
// @Description
// @Router /v1/my/change-password [POST]
// @Produce  json
// @Security ApiKeyAuth
// @Param json body services.ChangePasswordReq true "parameter"
// @Success 200 "Success"
func (con MyController) ChangePassword(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ChangePasswordReq
	if err := c.ShouldBindJSON(&req); err == nil {
		var err error
		db := xgorm.DB.WithContext(c)
		tx := db.Begin()
		err = services.MyService.ChangePassword(tx, nc.GetJWTUserId(), req)
		if xgorm.IsSqlErr(err) {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		if xgorm.IsNotFoundErr(err) {
			tx.Rollback()
			msg := i18n.Message{
				ID:    "change_password.failed",
				Other: "Old password is incorrect.",
			}
			nc.AlertResponse([]string{xi18n.Localize(c.Request, &msg)})
			return
		}
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags My
// @Summary 查看自己的菜單權限
// @Description
// @Router /v1/my/action-list [GET]
// @Produce  json
// @Security ApiKeyAuth
// @Success 200 {object} []services.MyUserActionListResp "Success"
func (con MyController) MyUserActionList(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}

	userId := nc.GetJWTUserId()
	db := xgorm.DB.WithContext(c)
	resp, err := services.MyService.MyUserActionList(db, userId)
	if err != nil {
		nc.ErrorResponse(nil, err)
		return
	}
	nc.OKResponse(resp)
}
