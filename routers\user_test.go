package routers

import (
	"testing"

	"github.com/Norray/xrocket/xtool"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

func TestSystemUserList(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/users",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "系統用戶列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.UserListReq{
					UserType: model.UserUserTypeSystemAdmin,
					Name:     "test",
					Email:    "<EMAIL>",
					RoleId:   1,
					Status:   "ENABLE",
				},
				PageSet: xresp.PageSet{
					PageIndex: 1,
					PageSize:  30,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSystemUserSearch(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/users/actions/search",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "系統用戶查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.UserSearchReq{
					UserType: model.UserUserTypeSystemAdmin,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSystemUserInquire(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/users/actions/inquire",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "系統用戶查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.UserInquireReq{
					UserId: 50,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSystemUserCreate(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/users/actions/create",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "創建系統用戶",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常創建",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.UserCreateReq{
					UserType: model.UserUserTypeSystemAdmin,
					Name:     "norrayadmin",
					Email:    "<EMAIL>",
					Password: xtool.FrontendEncodeStringWithSalt("123", "medic-crew"),
					RoleId:   2,
					Status:   "ENABLE",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSystemUserEdit(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/users/actions/edit",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "編輯系統用戶",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常編輯",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.UserEditReq{
					UserType: model.UserUserTypeSystemAdmin,
					UserId:   1,
					Name:     "修改後的用戶",
					Email:    "<EMAIL>",
					RoleId:   2,
					Status:   "ENABLE",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSystemUserDelete(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/users/actions/delete",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "刪除系統用戶",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常刪除",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.UserDeleteReq{
					UserType: model.UserUserTypeSystemAdmin,
					UserId:   1,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityUserList(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/facility/users",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "機構用戶列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.UserListReq{
					UserType: model.UserUserTypeFacilityUser,
					Name:     "test",
					Email:    "<EMAIL>",
					RoleId:   1,
					Status:   "ENABLE",
				},
				PageSet: xresp.PageSet{
					PageIndex: 1,
					PageSize:  30,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityUserCreate(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/facility/users/actions/create",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "創建機構用戶",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常創建",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.UserCreateReq{
					UserType: model.UserUserTypeFacilityUser,
					Name:     "測試機構用戶",
					Email:    "<EMAIL>",
					Password: "123456",
					RoleId:   1,
					Status:   "ENABLE",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityUserEdit(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/facility/users/actions/edit",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "編輯機構用戶",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常編輯",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.UserEditReq{
					UserType: model.UserUserTypeFacilityUser,
					UserId:   1,
					Name:     "修改後的機構用戶",
					Email:    "<EMAIL>",
					RoleId:   2,
					Status:   "ENABLE",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityUserDelete(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/facility/users/actions/delete",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "刪除機構用戶",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常刪除",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.UserDeleteReq{
					UserType:   model.UserUserTypeFacilityUser,
					FacilityId: 1,
					UserId:     1,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityUserSearch(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/facility/users/actions/search",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "機構用戶查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.UserSearchReq{},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityUserInquire(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/facility/users/actions/inquire",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "機構用戶查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.UserInquireReq{
					UserId: 44,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSystemUserSearchAll(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/users/actions/search-all",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "系統全用戶查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.UserSearchAllReq{},
			},
		},
	}
	xtest.RunTests(t, test)
}
