package routers

import (
	"encoding/json"
	"fmt"
	"os"
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

func TestProfessionalFileUploadFile(t *testing.T) {
	file, _ := os.Open("AHPRA.jpg")
	defer file.Close()

	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/professional-files/actions/upload-file",
		UserId:           8,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Form,
		Name:             "上傳",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ProfessionalFileUploadFileReq{
					FileCode: "AHPRA_CERT",
				},
				File:          file,
				FileFieldName: "file",
				CheckResultHandler: func(resp interface{}) bool {
					response := resp.(xresp.Response)
					var data services.ProfessionalFileUploadFileResp
					dataByte, _ := json.Marshal(response.Data)
					_ = json.Unmarshal(dataByte, &data)
					fmt.Println(data.ProfessionalFileId)
					return true
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
