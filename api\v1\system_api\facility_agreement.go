package system_api

import (
	"fmt"
	"net/http"

	"github.com/Norray/xrocket/xtool"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type FacilityAgreementController struct{}

func NewFacilityAgreementController() FacilityAgreementController {
	return FacilityAgreementController{}
}

// @Tags Facility Agreement
// @Summary 獲取機構協議列表
// @Description
// @Router /v1/system/facility-agreements [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FacilityAgreementListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Success 200 {object} []services.FacilityAgreementListResp "Success"
func (con FacilityAgreementController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityAgreementListReq
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.FacilityAgreementService.List(db, req, &pageSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility Agreement
// @Summary 查询機構協議
// @Description
// @Router /v1/system/facility-agreements/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FacilityAgreementInquireReq true "parameter"
// @Success 200 {object} services.FacilityAgreementInquireResp "Success"
func (con FacilityAgreementController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityAgreementInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.FacilityAgreementService.Inquire(db, req)
		if xgorm.IsNotFoundErr(err) {
			nc.BadRequestResponse(err)
			return
		}
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility Agreement
// @Summary 新增機構協議
// @Description
// @Router /v1/system/facility-agreements/actions/create [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FacilityAgreementCreateReq true "parameter"
// @Success 200 {object} services.FacilityAgreementCreateResp "Success"
func (con FacilityAgreementController) Create(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityAgreementCreateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityService.CheckIdExist(db, &model.Facility{}, req.FacilityId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityAgreementService.CheckTimeValid(db, req.FacilityId, req.BeginTime, req.EndTime)
			})
		checkMsg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		tx := db.Begin()
		resp, err := services.FacilityAgreementService.Create(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility Agreement
// @Summary 修改機構協議
// @Description
// @Router /v1/system/facility-agreements/actions/edit [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FacilityAgreementEditReq true "parameter"
// @Success 200 "Success"
func (con FacilityAgreementController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityAgreementEditReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		var checkMsg []string
		var facilityAgreement model.FacilityAgreement
		checker := xapp.NewCK(c, true)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityAgreementService.CheckIdExist(db, &facilityAgreement, req.FacilityAgreementId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityAgreementService.CheckTimeValid(db, facilityAgreement.FacilityId, req.BeginTime, req.EndTime, req.FacilityAgreementId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		tx := db.Begin()
		err = services.FacilityAgreementService.Edit(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility Agreement
// @Summary 發送機構協議
// @Description 將協議發送給機構並更新狀態為UNSIGNED
// @Router /v1/system/facility-agreements/actions/send-agreement [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.SendAgreementReq true "parameter"
// @Success 200 "Success"
func (con FacilityAgreementController) SendAgreement(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.SendAgreementReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityAgreementService.CheckIdExist(db, &model.FacilityAgreement{}, req.FacilityAgreementId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityAgreementService.CheckCanSendAgreementEmail(db, req.FacilityAgreementId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		tx := db.Begin()
		err = services.FacilityAgreementService.SendAgreement(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility Agreement
// @Summary 下載機構協議
// @Description 下載機構協議
// @Router /v1/system/facility-agreements/actions/download [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FacilityAgreementDownloadReq true "parameter"
// @Success 200 "Success"
func (con FacilityAgreementController) Download(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityAgreementDownloadReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityAgreementService.CheckIdExist(db, &model.FacilityAgreement{}, req.FacilityAgreementId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		filename, fileBytes, err := services.FacilityAgreementService.Download(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		c.Writer.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename*=utf-8''%s", xtool.ReplacePlus(filename)))
		c.Data(http.StatusOK, services.FacilityFileService.GetFileMimeType(filename), fileBytes)

	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Agreement
// @Summary 機構協議的列表
// @Description
// @Router /v1/system/facility-agreements/actions/search [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FacilityAgreementSearchByFacilityProfileReq true "parameter"
// @Success 200 {object} []services.FacilityAgreementSearchByFacilityProfileResp "Success"
func (con FacilityAgreementController) Search(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityAgreementSearchByFacilityProfileReq
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		resp, err := services.FacilityAgreementService.SearchByFacilityProfile(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.ErrorResponse(req, err)
	}
}
