package services

import (
	"errors"
	"strings"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xtool"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gorm.io/gorm"
)

var MenuActionService = new(menuActionService)

type menuActionService struct{}

// region ---------------------------------------------------- Checker ----------------------------------------------------

// 檢查菜單ID是否存在
func (s *menuActionService) CheckMenuIdExist(db *gorm.DB, menu *xmodel.Menu, menuId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.menu.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	var err error
	if err = db.Where("id = ?", menuId).Take(menu).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	}
	return true, i18n.Message{}, nil
}

// 檢查菜單Code是否唯一
func (s *menuActionService) CheckMenuCodeUnique(db *gorm.DB, menuCode string, menuId ...uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.menu.code.already_exists",
		Other: "The menu code is already exists. Please try another code.",
	}
	var err error
	builder := db.Where("code = ?", menuCode)
	if len(menuId) > 0 {
		builder = builder.Where("id != ?", menuId[0])
	}
	if err = builder.First(&xmodel.Menu{}).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return true, i18n.Message{}, nil
	}
	return false, msg, nil
}

// 檢查菜單ID是否可以刪除
func (s *menuActionService) CheckMenuIdCanBeDelete(db *gorm.DB, menuId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.menu.id.cannot_be_deleted",
		Other: "This menu cannot be deleted.",
	}
	var count int64
	if err := db.Table("action").Where("menu_id = ?", menuId).Count(&count).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if count > 0 {
		return false, msg, nil
	}
	return true, i18n.Message{}, nil
}

// 檢查功能ID是否存在
func (s *menuActionService) CheckActionIdExist(db *gorm.DB, action *xmodel.Action, actionId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.action.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	var err error
	if err = db.Where("id = ?", actionId).Take(action).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	}
	return true, i18n.Message{}, nil
}

// 檢查功能Code是否唯一
func (s *menuActionService) CheckActionCodeUnique(db *gorm.DB, actionCode string, actionId ...uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.action.code.already_exists",
		Other: "The action code is already exists. Please try another code.",
	}
	var err error
	builder := db.Where("code = ?", actionCode)
	if len(actionId) > 0 {
		builder = builder.Where("id != ?", actionId[0])
	}
	if err = builder.First(&xmodel.Action{}).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return true, i18n.Message{}, nil
	}
	return false, msg, nil
}

// 檢查功能ID是否存在於指定系統
func (s *menuActionService) CheckActionIdsExist(db *gorm.DB, system string, actionIds []uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.action.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	var count int64
	if err := db.Table("action a").
		Joins("JOIN menu m ON m.id = a.menu_id").
		Where("m.system IN (?)", strings.Split(system, ",")).
		Where("a.id IN (?)", actionIds).
		Count(&count).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if count != int64(len(actionIds)) {
		return false, msg, nil
	}
	return true, i18n.Message{}, nil
}

// 檢查角色功能ID是否存在
func (s *menuActionService) CheckRoleActionIds(db *gorm.DB, roleUserType string, actionIds []uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.role.action.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}

	var err error
	builder := db.Table("action AS a").
		Joins("JOIN menu AS m ON m.id = a.menu_id").
		Where("a.id IN (?)", actionIds)
	switch roleUserType {
	case model.UserUserTypeSystemAdmin:
		builder = builder.Where("m.system = ?", "SYSTEM")
	case model.UserUserTypeSuperAdmin:
		builder = builder.Where("m.system = ? OR m.system = ?", "SYSTEM", "PROGRAM")
	case model.UserUserTypeFacilityUser:
		builder = builder.Where("m.system = ?", "FACILITY")
	default:
		return false, msg, nil
	}
	var count int64
	if err = builder.
		Count(&count).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	return count == int64(len(actionIds)), msg, nil
}

// endregion ---------------------------------------------------- Checker ----------------------------------------------------

// region ---------------------------------------------------- 菜單列表 ----------------------------------------------------

type MenuListReq struct {
	System string `form:"system" binding:"required,oneof=FACILITY SYSTEM PROGRAM"`
}

type MenuListResp struct {
	MenuId  uint64           `json:"menuId"`
	System  string           `json:"system"`
	Code    string           `json:"code"`
	NameCn  string           `json:"nameCn"`
	NameHk  string           `json:"nameHk"`
	NameEn  string           `json:"nameEn"`
	Sort    int32            `json:"sort"`
	Actions []ActionListResp `json:"actions" gorm:"-"`
}

type ActionListResp struct {
	ActionId uint64 `json:"actionId"`
	MenuId   uint64 `json:"menuId"`
	Code     string `json:"code"`
	NameCn   string `json:"nameCn"`
	NameHk   string `json:"nameHk"`
	NameEn   string `json:"nameEn"`
	Group    int32  `json:"group"`
	Sort     int32  `json:"sort"`
}

func (s *menuActionService) List(db *gorm.DB, req MenuListReq) ([]MenuListResp, error) {
	var menus []*xmodel.Menu
	if err := db.
		Where("system = ?", req.System).
		Order("sort").
		Order("id").
		Find(&menus).Error; xgorm.IsSqlErr(err) {
		return nil, err
	}

	var actions []ActionListResp
	if err := db.
		Select([]string{
			"a.id AS action_id",
			"a.menu_id",
			"a.code",
			"a.name_cn",
			"a.name_hk",
			"a.name_en",
			"a.group",
			"a.sort",
		}).
		Table("action a").
		Joins("JOIN menu m ON m.id = a.menu_id").
		Where("m.system = ?", req.System).
		Order("a.menu_id").
		Order("a.sort").
		Find(&actions).Error; xgorm.IsSqlErr(err) {
		return nil, err
	}

	actionsMap := s.rebuildActionsToMap(actions)
	resp := make([]MenuListResp, 0)

	for _, menu := range menus {
		var m MenuListResp
		_ = copier.Copy(&m, &menu)
		m.MenuId = menu.Id
		ma := MenuListResp{
			MenuId:  m.MenuId,
			System:  m.System,
			Code:    m.Code,
			NameCn:  m.NameCn,
			NameHk:  m.NameHk,
			NameEn:  m.NameEn,
			Sort:    m.Sort,
			Actions: make([]ActionListResp, 0),
		}
		as, ok := actionsMap[m.MenuId]
		if ok {
			ma.Actions = as
		}
		resp = append(resp, ma)
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 菜單列表 ----------------------------------------------------

// region ---------------------------------------------------- 菜單創建 ----------------------------------------------------

type MenuCreateReq struct {
	System string `json:"system" binding:"required,oneof=FACILITY SYSTEM PROGRAM"`
	Code   string `json:"code" binding:"required"`
	NameCn string `json:"nameCn" binding:"required"`
	NameHk string `json:"nameHk" binding:"required"`
	NameEn string `json:"nameEn" binding:"required"`
	Sort   int32  `json:"sort"`
}

type MenuCreateResp struct {
	MenuId uint64 `json:"menuId"`
}

func (s *menuActionService) CreateMenu(db *gorm.DB, req MenuCreateReq) (MenuCreateResp, error) {
	var resp MenuCreateResp
	var menu xmodel.Menu
	_ = copier.Copy(&menu, &req)
	if err := db.Create(&menu).Error; xgorm.IsSqlErr(err) {
		return resp, err
	}
	resp.MenuId = menu.Id
	return resp, nil
}

// endregion ---------------------------------------------------- 菜單創建 ----------------------------------------------------

// region ---------------------------------------------------- 菜單修改 ----------------------------------------------------

type MenuEditReq struct {
	MenuId uint64 `json:"menuId" binding:"required"`
	Code   string `json:"code" binding:"required"`
	NameCn string `json:"nameCn" binding:"required"`
	NameHk string `json:"nameHk" binding:"required"`
	NameEn string `json:"nameEn" binding:"required"`
	Sort   int32  `json:"sort"`
}

func (s *menuActionService) EditMenu(db *gorm.DB, req MenuEditReq) error {
	var menu xmodel.Menu
	if err := db.Where("id = ?", req.MenuId).First(&menu).Error; xgorm.IsSqlErr(err) {
		return err
	}
	_ = copier.Copy(&menu, &req)
	if err := db.Save(&menu).Error; xgorm.IsSqlErr(err) {
		return err
	}
	return nil
}

// endregion ---------------------------------------------------- 菜單修改 ----------------------------------------------------

// region ---------------------------------------------------- 菜單刪除 ----------------------------------------------------

type MenuDeleteReq struct {
	MenuId uint64 `json:"menuId"`
}

func (s *menuActionService) DeleteMenu(db *gorm.DB, req MenuDeleteReq) error {
	var menu xmodel.Menu
	if err := db.Where("id = ?", req.MenuId).Delete(&menu).Error; xgorm.IsSqlErr(err) {
		return err
	}
	return nil
}

// endregion ---------------------------------------------------- 菜單刪除 ----------------------------------------------------

// region ---------------------------------------------------- 菜單排序 ----------------------------------------------------

type MenuSortReq struct {
	MenuId uint64 `json:"menuId" binding:"required"`
	Sort   int32  `json:"sort" binding:"required"`
}

type MenuSortUpdateReq struct {
	Menus []MenuSortReq `json:"menus" binding:"required,dive"`
}

func (s *menuActionService) UpdateMenuSort(db *gorm.DB, req MenuSortUpdateReq) error {
	if len(req.Menus) > 0 {
		tx := db.Begin()
		for _, menuSort := range req.Menus {
			if err := tx.Model(&xmodel.Menu{}).Where("id = ?", menuSort.MenuId).Update("sort", menuSort.Sort).Error; xgorm.IsSqlErr(err) {
				tx.Rollback()
				return err
			}
		}
		tx.Commit()
	}
	return nil
}

// endregion ---------------------------------------------------- 菜單排序 ----------------------------------------------------

// region ---------------------------------------------------- 功能創建 ----------------------------------------------------

type ActionCreateReq struct {
	MenuId uint64                    `json:"menuId" binding:"required"`
	Code   string                    `json:"code" binding:"required"`
	NameCn string                    `json:"nameCn" binding:"required"`
	NameHk string                    `json:"nameHk" binding:"required"`
	NameEn string                    `json:"nameEn" binding:"required"`
	Group  int32                     `json:"group"`
	Sort   int32                     `json:"sort"`
	Casbin []CasbinUpdateOrCreateReq `json:"casbin" binding:"required,min=1,dive"`
}

type ActionCreateResp struct {
	ActionId uint64 `json:"actionId"`
}

func (s *menuActionService) CreateAction(db *gorm.DB, req ActionCreateReq) (ActionCreateResp, error) {
	var resp ActionCreateResp
	var action xmodel.Action
	_ = copier.Copy(&action, &req)
	if err := db.Create(&action).Error; xgorm.IsSqlErr(err) {
		return resp, err
	}
	resp.ActionId = action.Id

	err := s.UpdateOrCreateCasbin(db, action.Code, req.Casbin)
	if err != nil {
		return ActionCreateResp{}, err
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 功能創建 ----------------------------------------------------

// region ---------------------------------------------------- 功能修改 ----------------------------------------------------

type ActionEditReq struct {
	ActionId uint64                    `json:"actionId" binding:"required"`
	Code     string                    `json:"code" binding:"required"`
	NameCn   string                    `json:"nameCn" binding:"required"`
	NameHk   string                    `json:"nameHk" binding:"required"`
	NameEn   string                    `json:"nameEn" binding:"required"`
	Group    int32                     `json:"group"`
	Sort     int32                     `json:"sort"`
	Casbin   []CasbinUpdateOrCreateReq `json:"casbin" binding:"required,min=1,dive"`
}

func (s *menuActionService) EditAction(db *gorm.DB, req ActionEditReq) error {
	var action xmodel.Action
	if err := db.Where("id = ?", req.ActionId).First(&action).Error; xgorm.IsSqlErr(err) {
		return err
	}
	_ = copier.Copy(&action, &req)
	if err := db.Save(&action).Error; xgorm.IsSqlErr(err) {
		return err
	}

	err := s.UpdateOrCreateCasbin(db, action.Code, req.Casbin)
	if err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 功能修改 ----------------------------------------------------

// region ---------------------------------------------------- 功能刪除 ----------------------------------------------------

type ActionDeleteReq struct {
	ActionId uint64 `json:"actionId" binding:"required"` // 功能ID
}

func (s *menuActionService) DeleteAction(db *gorm.DB, req ActionDeleteReq) error {
	if err := db.Where("action_id = ?", req.ActionId).Delete(&xmodel.RoleAction{}).Error; xgorm.IsSqlErr(err) {
		db.Rollback()
		return err
	}
	if err := db.Delete(&xmodel.Action{}, req.ActionId).Error; xgorm.IsSqlErr(err) {
		return err
	}
	return nil
}

// endregion ---------------------------------------------------- 功能刪除 ----------------------------------------------------

// region ---------------------------------------------------- 角色功能選擇 ----------------------------------------------------

type RoleActionSelectReq struct {
	RoleId  uint64   `json:"roleId" binding:"required"`
	Actions []uint64 `json:"actions" binding:"required"`
	System  string   `json:"-"`
}

func (s *menuActionService) SelectRoleActions(db *gorm.DB, req RoleActionSelectReq) error {
	// 去重
	actionIds := xtool.Uint64ArrayDeduplication(req.Actions)

	// 刪除不需要的
	var deleteActions []xmodel.RoleAction
	builder := db.
		Select("ra.*").
		Table("role_action ra").
		Joins("JOIN action a ON a.id = ra.action_id").
		Joins("JOIN menu m ON m.id = a.menu_id").
		Where("m.system IN (?)", strings.Split(req.System, ",")).
		Where("ra.role_id = ?", req.RoleId)
	if len(actionIds) != 0 {
		builder = builder.Where("ra.action_id NOT IN (?)", actionIds)
	}
	if err := builder.Find(&deleteActions).Error; xgorm.IsSqlErr(err) {
		return err
	}
	deleteIds := make([]uint64, 0)
	for _, a := range deleteActions {
		deleteIds = append(deleteIds, a.Id)
	}
	if len(deleteIds) > 0 {
		if err := db.Where("id IN (?)", deleteIds).Delete(xmodel.RoleAction{}).Error; xgorm.IsSqlErr(err) {
			return err
		}
	}
	// 排除已存在的
	var existingRoleActions []xmodel.RoleAction
	if err := db.Where("role_id = ?", req.RoleId).Where("action_id IN (?)", actionIds).Find(&existingRoleActions).Error; xgorm.IsSqlErr(err) {
		return err
	}
	addIds := make([]uint64, 0)
	existingRoleActionIds := make(map[uint64]bool)
	for _, a := range existingRoleActions {
		existingRoleActionIds[a.ActionId] = true
	}
	for _, a := range actionIds {
		if !existingRoleActionIds[a] {
			addIds = append(addIds, a)
		}
	}
	// 新建剩餘的
	for _, a := range addIds {
		if err := db.Create(&xmodel.RoleAction{
			RoleId:   req.RoleId,
			ActionId: a,
		}).Error; xgorm.IsSqlErr(err) {
			return err
		}
	}
	return nil
}

// endregion ---------------------------------------------------- 角色功能選擇 ----------------------------------------------------

// region ---------------------------------------------------- 角色功能列表 ----------------------------------------------------

type RoleActionListReq struct {
	RoleId uint64 `form:"roleId" binding:"required"`
	System string `form:"-"`
}

type RoleActionListResp struct {
	MenuId   uint64                       `json:"menuId"`
	System   string                       `json:"system"`
	Code     string                       `json:"code"`
	NameCn   string                       `json:"nameCn"`
	NameHk   string                       `json:"nameHk"`
	NameEn   string                       `json:"nameEn"`
	Sort     int32                        `json:"sort"`
	Selected int32                        `json:"selected"`
	Actions  []RoleActionListSelectedResp `json:"actions" gorm:"-"`
}

type RoleActionListSelectedResp struct {
	ActionId uint64 `json:"actionId"`
	MenuId   uint64 `json:"menuId"`
	Code     string `json:"code"`
	NameCn   string `json:"nameCn"`
	NameHk   string `json:"nameHk"`
	NameEn   string `json:"nameEn"`
	Group    int32  `json:"group"`
	Sort     int32  `json:"sort"`
	Selected int32  `json:"selected"`
}

func (s *menuActionService) ListRoleActions(db *gorm.DB, req RoleActionListReq) ([]RoleActionListResp, error) {
	var menus []*xmodel.Menu
	if err := db.
		Where("system IN (?)", strings.Split(req.System, ",")).
		Order("sort").
		Find(&menus).Error; xgorm.IsSqlErr(err) {
		return nil, err
	}

	var actions []RoleActionListSelectedResp
	if err := db.
		Select([]string{
			"a.id AS action_id",
			"a.menu_id",
			"a.code",
			"a.name_cn",
			"a.name_hk",
			"a.name_en",
			"a.group",
			"a.sort",
			"IF(ra.id IS NULL,0,1) AS selected",
		}).
		Table("action a").
		Joins("JOIN menu m ON m.id = a.menu_id").
		Joins("LEFT JOIN role_action ra ON ra.action_id = a.id AND role_id = ?", req.RoleId).
		Where("m.system IN (?)", strings.Split(req.System, ",")).
		Order("m.system").
		Order("a.menu_id").
		Order("a.sort").
		Find(&actions).Error; xgorm.IsSqlErr(err) {
		return nil, err
	}

	actionsMap := s.rebuildSelectedActionsToMap(actions)
	resp := make([]RoleActionListResp, 0)

	for _, menu := range menus {
		var m RoleActionListResp
		_ = copier.Copy(&m, &menu)
		m.MenuId = menu.Id
		ma := RoleActionListResp{
			MenuId:   m.MenuId,
			System:   m.System,
			Code:     m.Code,
			NameCn:   m.NameCn,
			NameHk:   m.NameHk,
			NameEn:   m.NameEn,
			Sort:     m.Sort,
			Selected: 0,
			Actions:  make([]RoleActionListSelectedResp, 0),
		}
		as, ok := actionsMap[m.MenuId]
		if ok {
			ma.Actions = as
		}
		sel := false
		for _, a := range ma.Actions {
			if a.Selected == 1 {
				sel = true
			}
		}
		if sel {
			ma.Selected = 1
		}
		resp = append(resp, ma)
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 角色功能列表 ----------------------------------------------------

// region ---------------------------------------------------- 菜單查詢 ----------------------------------------------------

type MenuInquireReq struct {
	MenuId uint64 `form:"menuId" binding:"required"`
}

func (s *menuActionService) InquireMenu(db *gorm.DB, req MenuInquireReq) (MenuListResp, error) {
	var resp MenuListResp
	err := db.
		Select([]string{
			"id AS menu_id",
			"system",
			"code",
			"name_cn",
			"name_hk",
			"name_en",
			"sort",
		}).
		Table("menu").
		Where("id = ?", req.MenuId).
		Take(&resp).Error
	return resp, err
}

// endregion ---------------------------------------------------- 菜單查詢 ----------------------------------------------------

// region ---------------------------------------------------- 功能查詢 ----------------------------------------------------

type ActionInquireReq struct {
	ActionId uint64 `form:"actionId" binding:"required"`
}

type ActionInquireResp struct {
	ActionId uint64           `json:"actionId"`
	MenuId   uint64           `json:"menuId"`
	Code     string           `json:"code"`
	NameCn   string           `json:"nameCn"`
	NameHk   string           `json:"nameHk"`
	NameEn   string           `json:"nameEn"`
	Group    int32            `json:"group"`
	Sort     int32            `json:"sort"`
	Casbin   []CasbinListResp `json:"casbin" gorm:"-"`
}

func (s *menuActionService) InquireAction(db *gorm.DB, req ActionInquireReq) (ActionInquireResp, error) {
	var resp ActionInquireResp
	err := db.
		Select([]string{
			"id AS action_id",
			"menu_id",
			"code",
			"name_cn",
			"name_hk",
			"name_en",
			"group",
			"sort",
		}).
		Table("action").
		Where("id = ?", req.ActionId).
		Take(&resp).Error

	casbin, err := CasbinService.List(db, CasbinListReq{
		CasbinName: "CONSOLE",
		V0:         resp.Code,
	}, &xresp.PageSet{})
	if err != nil {
		return resp, err
	}
	resp.Casbin = casbin

	return resp, err
}

// endregion ---------------------------------------------------- 功能查詢 ----------------------------------------------------

// region ---------------------------------------------------- 工具函數 ----------------------------------------------------

func (s *menuActionService) rebuildActionsToMap(actions []ActionListResp) map[uint64][]ActionListResp {
	result := make(map[uint64][]ActionListResp)
	for _, action := range actions {
		arr, ok := result[action.MenuId]
		var a ActionListResp
		_ = copier.Copy(&a, &action)
		if ok {
			arr = append(arr, a)
			result[action.MenuId] = arr
		} else {
			result[action.MenuId] = []ActionListResp{a}
		}
	}
	return result
}

func (s *menuActionService) rebuildSelectedActionsToMap(actions []RoleActionListSelectedResp) map[uint64][]RoleActionListSelectedResp {
	result := make(map[uint64][]RoleActionListSelectedResp)
	for _, action := range actions {
		arr, ok := result[action.MenuId]
		var a RoleActionListSelectedResp
		_ = copier.Copy(&a, &action)
		if ok {
			arr = append(arr, a)
			result[action.MenuId] = arr
		} else {
			result[action.MenuId] = []RoleActionListSelectedResp{a}
		}
	}
	return result
}

// endregion ---------------------------------------------------- 工具函數 ----------------------------------------------------

func (s *menuActionService) GetSystemByRole(role xmodel.Role) string {
	switch role.UserType {
	case model.UserUserTypeProfessional:
		return "PROFESSIONAL"
	case model.UserUserTypeFacilityUser:
		return "FACILITY"
	case model.UserUserTypeSystemAdmin:
		return "SYSTEM"
	case model.UserUserTypeSuperAdmin:
		return "SYSTEM,PROGRAM"
	default:
		return ""
	}
}

type CasbinUpdateOrCreateReq struct {
	CasbinName string `json:"casbinName" binding:"required"`
	CasbinId   uint64 `json:"casbinId"`
	V1         string `json:"v1" binding:"required"`
	V2         string `json:"v2" binding:"required"`
}

func (s *menuActionService) UpdateOrCreateCasbin(db *gorm.DB, actionCode string, casbinArr []CasbinUpdateOrCreateReq) error {
	if strings.ToUpper(actionCode) == actionCode {
		// 全大寫的是 UseType 的權限
		return errors.New("can not use this acton code")
	}
	var err error
	tableName := casbinTableNameMap["CONSOLE"]
	if len(casbinArr) == 0 {
		// 刪除
		if err = db.Table(tableName).Where("ptype = ?", "p").Where("v0 = ?", actionCode).Delete(&Casbin{}).Error; err != nil {
			return err
		}
		return nil
	}

	var ids []uint64
	for _, casbin := range casbinArr {
		if casbin.CasbinId > 0 {
			var data Casbin
			if err = db.Table(tableName).Where("ptype = ?", "p").Where("v0 = ?", actionCode).Where("id = ?", casbin.CasbinId).First(&data).Error; err != nil {
				return err
			}
			data.V1 = casbin.V1
			data.V2 = casbin.V2
			if err = db.Table(tableName).Save(&data).Error; err != nil {
				return err
			}
			ids = append(ids, data.Id)
		} else {
			// 新增
			data := Casbin{
				Ptype: "p",
				V0:    actionCode,
				V1:    casbin.V1,
				V2:    casbin.V2,
			}
			if err = db.Table(tableName).Create(&data).Error; err != nil {
				return err
			}
			ids = append(ids, data.Id)
		}
	}

	if err = db.Table(tableName).Where("ptype = ?", "p").Where("v0 = ?", actionCode).Where("id NOT IN (?)", ids).Delete(&Casbin{}).Error; err != nil {
		return err
	}

	return nil
}
