package model

import (
	"time"

	"github.com/Norray/xrocket/xmodel"
)

// 聊天會話
type WsSession struct {
	Id                      uint64    `json:"id" gorm:"primary_key"`
	SessionUuid             string    `json:"sessionUuid" gorm:"type:varchar(64);unique;not null"`                          // 會話UUID
	FacilityId              uint64    `json:"facilityId" gorm:"not null;index:facility_id_idx"`                             // 機構ID
	ProfessionalUserId      uint64    `json:"professionalUserId" gorm:"not null;index:professional_user_id_idx"`            // 專業人士ID
	LastMessageId           uint64    `json:"lastMessageId"`                                                                // 最後一條消息ID
	LastMessageType         string    `json:"lastMessageType" gorm:"type:varchar(64)"`                                      // 最後一條消息類型
	LastMessageTime         time.Time `json:"lastMessageTime" gorm:"type:datetime(0);index:last_message_time_idx;not null"` // 最後一條消息時間
	LastMessageContent      string    `json:"lastMessageContent" gorm:"type:varchar(255)"`                                  // 最後一條消息內容
	FacilityUnreadCount     int32     `json:"facilityUnreadCount"`                                                          // 機構未讀消息數
	ProfessionalUnreadCount int32     `json:"professionalUnreadCount"`                                                      // 專業人士未讀消息數
	CreateTime              time.Time `json:"createTime" gorm:"type:datetime(0);index:create_time_idx;not null;"`           // 創建時間
	xmodel.Model
}

func (m *WsSession) TableName() string {
	return "ws_session"
}

func (m *WsSession) SwaggerDescription() string {
	return "聊天會話"
}
