package task

import (
	"context"
	"encoding/json"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xgorm"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
)

const CreateInvoiceDraftTask = "create_invoice_draft_task" // rabbitmq

type CreateInvoiceDraftReq struct {
	JobId uint64 `json:"jobId"` // 工作Id
}

// 執行隊列 - 生成發票草稿
func CreateInvoiceDraft(taskJson string) (context.Context, error) {
	var err error
	ctx := context.Background()
	traceId := uuid.NewV4().String()
	ctx = context.WithValue(ctx, "traceId", traceId)

	logger := log.WithField("traceId", traceId).WithField("task", CreateInvoiceDraftTask)

	taskData := CreateInvoiceDraftReq{}
	err = json.Unmarshal([]byte(taskJson), &taskData)
	if err != nil {
		logger.Errorf("fail to unmarshal task: %v, taskJson: %s", err, taskJson)
		return ctx, err
	}
	logger = logger.WithField("JobId", taskData.JobId)
	logger.Info("Start to create invoice draft")

	// 獲取數據庫連接
	db := xgorm.DB.WithContext(ctx)

	// 查詢是否有待生成的工作申請
	var count int64
	if err = db.Model(&model.JobApplication{}).
		Where("job_id = ?", taskData.JobId).
		Where("invoice_generated = ?", model.JobApplicationInvoiceGeneratedN).
		Where("status = ?", model.JobApplicationStatusAccept).
		Where("accept = ?", model.JobApplicationAcceptY).
		Count(&count).Error; err != nil {
		logger.Errorf("fail to get job application: %v", err)
		return ctx, err
	}
	if count == 0 {
		logger.Info("no job application found")
		return ctx, nil
	}

	// 開啟事務
	tx := db.Begin()

	// 嘗試生成發票，同時會在內部更新工作申請記錄的狀態
	err = services.InvoiceService.CreateUpfrontInvoiceByFacility(tx, taskData.JobId)
	if err != nil {
		tx.Rollback()
		logger.Errorf("fail to create upfront invoice: %v", err)
		return ctx, err
	}

	// 提交事務
	if err = tx.Commit().Error; err != nil {
		logger.Errorf("fail to commit transaction: %v", err)
		return ctx, err
	}

	logger.Info("create invoice draft task completed")
	return ctx, nil
}
