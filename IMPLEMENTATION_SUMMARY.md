# 專業人士資格證書上傳功能實現總結

## 需求描述

為Medical Practitioner、Registered Nurse、Enrolled Nurse這三個職業補充ProfessionalFileCodeQualificationCertificate類型的文件上傳和進度功能。

## 實現內容

### 1. 進度計算邏輯更新

**文件**: `services/professional_profile.go`
**函數**: `GetProgress`

在工作偏好及經驗部分添加了資格證書的進度計算：

```go
// 資格證書上傳 - 只有Medical Practitioner、Registered Nurse、Enrolled Nurse需要
if professional.Profession == model.ProfessionalProfessionMedicalPractitioner ||
    professional.Profession == model.ProfessionalProfessionRegisteredNurse ||
    professional.Profession == model.ProfessionalProfessionEnrolledNurse {
    resp.WorkPreferencesAndExperience.AddFilePoints(profile.Files, model.ProfessionalFileCodeQualificationCertificate, 1)
}
```

### 2. 文件更新邏輯

**文件**: `services/professional_profile.go`
**函數**: `UpdateWorkPreferencesAndExperience`

添加了資格證書文件的處理邏輯：

```go
// 處理資格證書文件 - 只有Medical Practitioner、Registered Nurse、Enrolled Nurse需要
if professional.Profession == model.ProfessionalProfessionMedicalPractitioner ||
    professional.Profession == model.ProfessionalProfessionRegisteredNurse ||
    professional.Profession == model.ProfessionalProfessionEnrolledNurse {
    if err = s.updateSingleFileType(db, req, profile, req.WorkPreferencesAndExperience.Files, model.ProfessionalFileCodeQualificationCertificate); err != nil {
        return err
    }
}
```

### 3. 職業變更時的清理邏輯

**文件**: `services/professional_profile.go`
**函數**: `UpdatePersonalInformation`

在職業變更時清理資格證書文件：

```go
// 清空資格證書文件（當職業變更時）
if err = s.updateSingleFileType(db, req, profile, emptyFile, model.ProfessionalFileCodeQualificationCertificate); err != nil {
    return err
}
```

### 4. API文檔更新

**文件**: `services/professional_profile.go`
**結構體**: `ProfessionalProfileEditWorkPreferencesAndExperienceReq`

更新了Files字段的註釋，添加了資格證書類型：

```go
Files map[string][]model.ProfessionalProfileFile `json:"files" binding:"omitempty,dive"` // 文件 ... 資格證書=QUALIFICATION_CERTIFICATE
```

### 5. 機構訪問權限配置

**文件**: `services/job_application.go`
**變量**: `FacilityAccessProfessionalFileTypeMap`

添加了機構對資格證書文件的訪問權限：

```go
model.ProfessionalFileCodeQualificationCertificate: true,
```

### 6. 測試文件

**文件**: `routers/professional_qualification_certificate_test.go`

創建了完整的測試用例，包括：
- 文件上傳功能測試
- 進度計算功能測試

### 7. 文檔

**文件**: `docs/professional_qualification_certificate_feature.md`

創建了詳細的功能實現文檔，包括：
- 功能概述
- 實現詳情
- 使用說明
- API調用示例
- 注意事項

## 已存在的配置

以下配置在實現前已經存在，無需修改：

1. **文件類型常量**: `model.ProfessionalFileCodeQualificationCertificate = "QUALIFICATION_CERTIFICATE"`
2. **文件參數配置**: `ProfessionalFileCodeQualificationCertificate: ProfessionalProfileFileNoNeedParam`
3. **文件上傳API**: 已支持所有文件類型的上傳
4. **職業類型常量**: 三個目標職業的常量已定義

## 功能特點

1. **職業限制**: 只有Medical Practitioner、Registered Nurse、Enrolled Nurse需要上傳資格證書
2. **進度計算**: 上傳至少1個資格證書文件後，該項進度標記為完成
3. **文件管理**: 支持文件的增加、刪除和更新
4. **職業變更**: 當用戶變更職業時，會自動清理不相關的資格證書文件
5. **機構訪問**: 機構可以查看專業人士上傳的資格證書文件
6. **無額外參數**: 資格證書文件不需要到期日期、號碼或描述等額外參數

## 前端實現要點

1. **顯示條件**: 只對指定的三個職業顯示資格證書上傳區域
2. **文件格式**: 支持.pdf, .jpg, .png格式，最多上傳2張圖片
3. **文件代碼**: 使用`QUALIFICATION_CERTIFICATE`作為fileCode
4. **進度顯示**: 進度計算包含在"Work Preferences & Experience"部分
5. **拖拽上傳**: 支持拖拽文件到上傳區域或點擊上傳圖標

## 測試驗證

可以通過以下方式驗證功能：

1. 運行測試文件：`go test -v ./routers -run TestProfessionalQualificationCertificate`
2. 運行驗證腳本：`go run scripts/verify_qualification_certificate_feature.go`
3. 手動測試API端點和進度計算

## 總結

此實現完全滿足需求，為指定的三個職業提供了資格證書上傳功能和相應的進度計算，同時保持了與現有系統的兼容性和一致性。
