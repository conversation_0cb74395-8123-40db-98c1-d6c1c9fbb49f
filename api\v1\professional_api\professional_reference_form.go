package professional_api

import (
	"encoding/base64"
	"errors"

	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type ProfessionalReferenceFormController struct {
	v1.CommonController
}

func NewProfessionalReferenceFormController() ProfessionalReferenceFormController {
	return ProfessionalReferenceFormController{}
}

// @Tags Professional Profile
// @Summary 獲取 reference form 詳情
// @Description
// @Router /v1/professional/reference-forms/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalReferenceFormInquireReq true "parameter"
// @Success 200 {object} services.ProfessionalReferenceFormInquireResp "Success"
func (con ProfessionalReferenceFormController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalReferenceFormInquireReq
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var resp services.ProfessionalReferenceFormInquireResp
		resp, err = services.ProfessionalReferenceFormService.Inquire(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Profile
// @Summary 驗證 reference form
// @Description
// @Router /v1/professional/reference-forms/actions/verify [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalReferenceFormVerifyReq true "parameter"
// @Success 200 {object} services.ProfessionalReferenceFormVerifyResp "Success"
func (con ProfessionalReferenceFormController) Verify(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalReferenceFormVerifyReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var resp services.ProfessionalReferenceFormVerifyResp

		tx := db.Begin()
		resp, err = services.ProfessionalReferenceFormService.Verify(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()

		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Profile
// @Summary 發送 reference form 驗證碼
// @Description 向指定郵箱發送驗證碼
// @Router /v1/professional/reference-forms/actions/send-verification-code [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalReferenceFormSendVerificationCodeReq true "parameter"
// @Success 200 {object} services.ProfessionalReferenceFormSendVerificationCodeResp "Success"
func (con ProfessionalReferenceFormController) SendVerificationCode(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalReferenceFormSendVerificationCodeReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var resp services.ProfessionalReferenceFormSendVerificationCodeResp
		resp, err = services.ProfessionalReferenceFormService.SendVerificationCode(db, nc.GetLanguage(), req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Profile
// @Summary 保存 reference form
// @Description 保存專業參考表單數據
// @Router /v1/professional/reference-forms/actions/save [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalReferenceFormSaveReq true "parameter"
// @Success 200 {object} services.ProfessionalReferenceFormSaveResp "Success"
func (con ProfessionalReferenceFormController) Save(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalReferenceFormSaveReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var data services.ProfessionalReferenceFormInquireResp
		data, err = services.ProfessionalReferenceFormService.Inquire(db, services.ProfessionalReferenceFormInquireReq{
			ProfessionalId: req.ProfessionalId,
			FormUuid:       req.FormUuid,
			MatchUuid:      req.MatchUuid,
		})
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		if data.Valid == "N" {
			nc.OKResponse(services.ProfessionalReferenceFormSaveResp{
				Status: "NOT_FOUND",
			})
			return
		}

		if req.ElectronicSignatureUuid != "" {
			var checkMsg []string
			checker := xapp.NewCK(c)
			checker.
				Run(func() (bool, i18n.Message, error) {
					return services.ProfessionalFileService.CheckFileExistByUuid(db, data.UserId, []string{model.ProfessionalFileCodeReferenceFormSignature}, []string{req.ElectronicSignatureUuid})
				})
			checkMsg, err = checker.Result()
			if err != nil {
				nc.ErrorResponse(req, err)
				return
			}
			if len(checkMsg) > 0 {
				nc.BadRequestResponseWithCheckMsg(checkMsg)
				return
			}
		}

		// 開始事務
		tx := db.Begin()
		var resp services.ProfessionalReferenceFormSaveResp
		resp, err = services.ProfessionalReferenceFormService.Save(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()

		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Profile
// @Summary 生成 reference form 上傳文件二維碼
// @Description 生成用於上傳 reference form 相關文件的二維碼
// @Router /v1/professional/reference-forms/actions/gen-upload-qrcode [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalReferenceFormGenUploadQrCodeReq true "parameter"
// @Success 200 {object} services.ProfessionalReferenceFormGenUploadQrCodeResp "Success"
func (con ProfessionalReferenceFormController) GenUploadQrcode(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalReferenceFormGenUploadQrCodeReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var data services.ProfessionalReferenceFormInquireResp
		data, err = services.ProfessionalReferenceFormService.Inquire(db, services.ProfessionalReferenceFormInquireReq{
			ProfessionalId: req.ProfessionalId,
			FormUuid:       req.FormUuid,
			MatchUuid:      req.MatchUuid,
		})
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		if data.Valid == "N" {
			nc.OKResponse(services.ProfessionalReferenceFormGenUploadQrCodeResp{
				Valid: "N",
			})
			return
		}

		resp, err := services.UploadFileByQrcodeService.GenQrcodeByFileCode(db, services.GenUploadFileQrcodeReq{
			ReqUserId: data.UserId,
			FileCode:  model.ProfessionalFileCodeReferenceFormSignature,
		})
		if xgorm.IsNotFoundErr(err) {
			nc.BadRequestResponse(err)
			return
		}
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(services.ProfessionalReferenceFormGenUploadQrCodeResp{
			Valid:      "Y",
			Uuid:       resp.Uuid,
			ExpireTime: resp.ExpireTime,
		})

	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Reference Form
// @Summary 獲取專業人士推薦表單簽名
// @Description 直接獲取專業人士推薦表單的電子簽名圖片，可以在瀏覽器中直接顯示
// @Router /v1/professional/reference-forms/actions/preview [GET]
// @Security ApiKeyAuth
// @Param json body services.ProfessionalReferenceFormFileGetPreviewReq true "parameter"
// @Success 200 {object} services.ProfessionalReferenceFormFileGetPreviewResp "Success"
func (con ProfessionalReferenceFormController) Preview(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalReferenceFormFileGetPreviewReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var data services.ProfessionalReferenceFormInquireResp
		data, err = services.ProfessionalReferenceFormService.Inquire(db, services.ProfessionalReferenceFormInquireReq{
			ProfessionalId: req.ProfessionalId,
			FormUuid:       req.FormUuid,
			MatchUuid:      req.MatchUuid,
		})
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		if data.Valid == "N" {
			nc.OKResponse(services.ProfessionalReferenceFormFileGetPreviewResp{
				Valid: "N",
			})
			return
		}

		resp, err := services.UploadFileByQrcodeService.InquireByQrcode(c, services.InquireFileIdByQrcodeReq{
			ReqUserId: data.UserId,
			Uuid:      req.Uuid,
		})
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if resp == nil {
			// 還沒有上傳
			nc.OKResponse(services.ProfessionalReferenceFormFileGetPreviewResp{
				Valid:    "Y",
				FileUuid: "",
				FileData: "",
			})
			return
		}
		// 將 resp 強制轉為 UploadFileByQrcodeCache
		uploadFileByQrcodeCache, ok := resp.(services.ProfessionalFileUploadFileResp)
		if !ok {
			nc.ErrorResponse(req, errors.New("can not change to ProfessionalFileUploadFileResp"))
			return
		}
		if uploadFileByQrcodeCache.ProfessionalFileId == 0 {
			// 還沒有上傳
			nc.OKResponse(services.ProfessionalReferenceFormFileGetPreviewResp{
				Valid:    "Y",
				FileUuid: "",
				FileData: "",
			})
			return
		}
		imageResult, err := services.ProfessionalFileService.Preview(db, services.ProfessionalFileGetPreviewReq{
			ProfessionalFileId: uploadFileByQrcodeCache.ProfessionalFileId,
			Thumb:              "N",
		})
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		// 將 imageResult.FileBytes 轉成base64
		base64Str := base64.StdEncoding.EncodeToString(imageResult.FileBytes)
		nc.OKResponse(services.ProfessionalReferenceFormFileGetPreviewResp{
			Valid:    "Y",
			FileUuid: imageResult.FileUuid,
			FileData: base64Str,
		})

	} else {
		nc.BadRequestResponse(err)
	}
}
