# github.com/Norray/medic-crew

## 目錄
### `/api`
業務模塊
### `/config`
主程序配置文件
### `/docs`
swagger文件
### `/hooks`
用於docker hub構建，無需理會
### `/internal`
一般用於存放定時任務和隊列
### `/resource`
資源文件
### `/routers`
路由及路由控制器
### `/services`
可複用的方法

## 使用
### 配置文件
查看`github.com/Norray/xrocket/xconfig`包裡的示例，複製到config文件夾下，並重命名為`app.ini`

### 國際化
用於自動生成國際化文件,當用戶在任意地方加入
```
msg = i18n.Message{
    ID:    "xxxx.xxxx.xxx",
    Other: "msg",
}
```
執行`/gen_i18n.sh`,就會在`/x/xi18n`下自動更新`active.*.toml`文件,然後補充新的翻譯即可

### swagger 
生成swagger文件
```
swag init
```
以上命令可能有部分對象無法正確生成，在dockerfile中需執行以下命令,一般來講深度是2就夠
```
swag init --parseDependency --parseDepth 2
```

### 更新xrocket
拉取最新的xrocket包
```
go get github.com/Norray/xrocket
```

### Dockerfile 本地打包
可通過 https://github.com/settings/tokens 獲取 ACCESS_TOKEN
```
docker build -t xxx:0.0.1-dev . --build-arg GITHUB_USER=xxxxx --build-arg ACCESS_TOKEN=xxxx 
```

### WebSocket 部署
若需設定反向代理，請在 Apache 的配置檔中加入下列設定
```
<LocationMatch "^/medic-crew/api/v1/.*/ws/actions/connect$">
    ProxyPass "ws://127.0.0.1:8080"
    ProxyPassReverse "ws://127.0.0.1:8080"
    RequestHeader set Connection "upgrade"
    RequestHeader set Upgrade "websocket"
</LocationMatch>
```

### MySQL 時區設定
MySQL要執行以下命令，將系統的時區表導入 MySQL：
```
mysql_tzinfo_to_sql /usr/share/zoneinfo | mysql -u root -p mysql
```