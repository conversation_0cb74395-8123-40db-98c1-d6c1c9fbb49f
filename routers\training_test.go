package routers

import (
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

var testModuleId uint64 = 1 // 假設存在的培訓模塊ID

// region ------------------------------------------ 專業人員培訓 ------------------------------------------

func TestTrainingModuleList(t *testing.T) {
	// 構建測試用例
	user := getTestUser(15) // 使用測試用戶
	test := xtest.Test{
		Url:              programPath + "/v1/professional/training-modules",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "獲取培訓模塊列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestTrainingModuleInquire(t *testing.T) {
	// 構建測試用例
	user := getTestUser(15) // 使用測試用戶
	test := xtest.Test{
		Url:              programPath + "/v1/professional/training-modules/actions/inquire",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "獲取培訓模塊詳情",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.TrainingModuleDetailReq{
					ModuleId: testModuleId,
				},
			},
			{
				SubName:           "缺少模塊ID",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params:            services.TrainingModuleDetailReq{},
			},
			{
				SubName:           "無效的模塊ID",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.TrainingModuleDetailReq{
					ModuleId: 99999,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestTrainingModuleQuestions(t *testing.T) {
	// 構建測試用例
	user := getTestUser(15) // 使用測試用戶
	test := xtest.Test{
		Url:              programPath + "/v1/professional/training-modules/actions/questions",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "獲取培訓模塊問題",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.TrainingModuleQuestionsReq{
					ModuleId: testModuleId,
				},
			},
			{
				SubName:           "查詢下一級的題目",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.TrainingModuleQuestionsReq{
					ModuleId: testModuleId + 1,
				},
			},
			{
				SubName:           "缺少模塊ID",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params:            services.TrainingModuleQuestionsReq{},
			},
			{
				SubName:           "無效的模塊ID",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.TrainingModuleQuestionsReq{
					ModuleId: 99999,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestTrainingModuleProgress(t *testing.T) {
	// 構建測試用例
	user := getTestUser(15) // 使用測試用戶
	test := xtest.Test{
		Url:              programPath + "/v1/professional/training-modules/actions/progress",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "獲取用戶培訓進度",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.TrainingUserProgressReq{
					ModuleId: testModuleId,
				},
			},
			{
				SubName:           "缺少模塊ID",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params:            services.TrainingUserProgressReq{},
			},
			{
				SubName:           "無效的模塊ID",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.TrainingUserProgressReq{
					ModuleId: 99999,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestTrainingAnswer(t *testing.T) {
	// 構建測試用例
	user := getTestUser(15) // 使用測試用戶
	test := xtest.Test{
		Url:              programPath + "/v1/professional/training-modules/actions/answer",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "批量回答培訓問題",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常批量回答",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.TrainingAnswerQuestionReq{
					ModuleId: testModuleId,
					Answers: []services.TrainingAnswer{
						{QuestionSeq: 1, OptionSeq: 1},
						{QuestionSeq: 2, OptionSeq: 2},
						{QuestionSeq: 3, OptionSeq: 1},
					},
				},
			},
			{
				SubName:           "缺少模塊ID",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.TrainingAnswerQuestionReq{
					Answers: []services.TrainingAnswer{
						{QuestionSeq: 1, OptionSeq: 1},
					},
				},
			},
			{
				SubName:           "缺少答案列表",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.TrainingAnswerQuestionReq{
					ModuleId: testModuleId,
				},
			},
			{
				SubName:           "空答案列表",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.TrainingAnswerQuestionReq{
					ModuleId: testModuleId,
					Answers:  []services.TrainingAnswer{},
				},
			},
			{
				SubName:           "無效的選項序號",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.TrainingAnswerQuestionReq{
					ModuleId: testModuleId,
					Answers: []services.TrainingAnswer{
						{QuestionSeq: 1, OptionSeq: 0}, // 選項序號從1開始
					},
				},
			},
			{
				SubName:           "部分答案（應該被檢查器拒絕）",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.TrainingAnswerQuestionReq{
					ModuleId: testModuleId,
					Answers: []services.TrainingAnswer{
						{QuestionSeq: 1, OptionSeq: 1}, // 只回答了一個問題，但模塊可能有多個問題
					},
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestTrainingMarkVideoWatched(t *testing.T) {
	// 構建測試用例
	user := getTestUser(15) // 使用測試用戶
	test := xtest.Test{
		Url:              programPath + "/v1/professional/training-modules/actions/mark-video-watched",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "標記視頻觀看完成",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常標記",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.TrainingMarkVideoWatchedReq{
					ModuleId: testModuleId,
				},
			},
			{
				SubName:           "缺少模塊ID",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params:            services.TrainingMarkVideoWatchedReq{},
			},
			{
				SubName:           "無效的模塊ID",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.TrainingMarkVideoWatchedReq{
					ModuleId: 99999,
				},
			},
			{
				SubName:           "重複標記（應該成功）",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.TrainingMarkVideoWatchedReq{
					ModuleId: testModuleId,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// endregion ------------------------------------------ 專業人員培訓 ------------------------------------------
