package model

import (
	"github.com/Norray/xrocket/xmodel/xtype"
	"time"

	"github.com/Norray/xrocket/xmodel"
)

// 工作排程相關常量
const (
	// 排程重複類型
	JobScheduleRepeatDaily   = "DAILY"   // 每日重複
	JobScheduleRepeatWeekly  = "WEEKLY"  // 每週重複
	JobScheduleRepeatMonthly = "MONTHLY" // 每月重複

	// 排程狀態
	JobScheduleStatusEnable  = "ENABLE"  // 啟用
	JobScheduleStatusDisable = "DISABLE" // 停用

	// 月重複類型
	JobScheduleMonthlyTypeDay     = "DAY"     // 按日期重複
	JobScheduleMonthlyTypeWeekday = "WEEKDAY" // 按週幾重複

	// 星期幾常量
	JobScheduleWeekdaySunday    = 7 // 週日
	JobScheduleWeekdayMonday    = 1 // 週一
	JobScheduleWeekdayTuesday   = 2 // 週二
	JobScheduleWeekdayWednesday = 3 // 週三
	JobScheduleWeekdayThursday  = 4 // 週四
	JobScheduleWeekdayFriday    = 5 // 週五
	JobScheduleWeekdaySaturday  = 6 // 週六

	// 月重複週索引
	JobScheduleMonthlyWeekFirst  = 1 // 第一週
	JobScheduleMonthlyWeekSecond = 2 // 第二週
	JobScheduleMonthlyWeekThird  = 3 // 第三週
	JobScheduleMonthlyWeekFourth = 4 // 第四週
	JobScheduleMonthlyWeekLast   = 5 // 最後一週
)

// JobSchedule 工作排程
type JobSchedule struct {
	Id                 uint64         `json:"id" gorm:"primary_key"`                                // 主鍵
	FacilityId         uint64         `json:"facilityId" gorm:"index:facility_idx;not null"`        // 機構Id
	Name               string         `json:"name" gorm:"type:varchar(255);not null"`               // 排程名稱
	PositionProfession string         `json:"positionProfession" gorm:"type:varchar(255);not null"` // 職位專業
	RepeatType         string         `json:"repeatType" gorm:"type:varchar(32);not null"`          // 重複類型 DAILY WEEKLY MONTHLY
	BeginDate          xtype.NullDate `json:"beginDate" gorm:"type:date"`                           // 計劃開始日期(YYYY-MM-DD)
	EndDate            xtype.NullDate `json:"endDate" gorm:"type:date"`                             // 計劃結束日期(YYYY-MM-DD)
	AdvanceDays        int32          `json:"advanceDays" gorm:"not null"`                          // 提前幾天發佈
	Status             string         `json:"status" gorm:"type:varchar(32);not null"`              // 計劃狀態 ENABLE DISABLE

	// 日重複設定
	DailyInterval int32 `json:"dailyInterval" gorm:"not null"` // 每幾天

	// 週重複設定
	WeeklyInterval int32  `json:"weeklyInterval" gorm:"not null"`             // 每幾週
	WeekDays       string `json:"weekDays" gorm:"type:varchar(255);not null"` // 週幾 1-7，多個以逗號分隔

	// 月重複設定
	MonthlyInterval   int32  `json:"monthlyInterval" gorm:"not null"`              // 每幾個月
	MonthlyType       string `json:"monthlyType" gorm:"type:varchar(32);not null"` // 按日期還是按週幾 DAY WEEKDAY
	MonthlyDayOfMonth int32  `json:"monthlyDayOfMonth" gorm:"not null"`            // 指定日期 1-31
	MonthlyWeekIndex  int32  `json:"monthlyWeekIndex" gorm:"not null"`             // 第幾週 1-5
	MonthlyWeekDay    int32  `json:"monthlyWeekDay" gorm:"not null"`               // 週幾 1-7

	CreatedUserId uint64     `json:"createdUserId" gorm:"index:created_user_idx;not null"`    // 創建者Id
	UpdatedUserId uint64     `json:"updatedUserId" gorm:"index:updated_user_idx;not null"`    // 更新者Id
	CreateTime    time.Time  `json:"createTime" gorm:"type:datetime(0);not null"`             // 創建時間
	UpdateTime    *time.Time `swaggertype:"string" json:"updateTime" gorm:"type:datetime(0)"` // 更新時間
	xmodel.Model
}

func (JobSchedule) TableName() string {
	return "job_schedule"
}

func (JobSchedule) SwaggerDescription() string {
	return "工作排程"
}
