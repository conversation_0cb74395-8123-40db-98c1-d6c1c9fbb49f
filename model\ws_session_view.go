package model

import (
	"time"

	"github.com/Norray/xrocket/xmodel"
)

// 聊天會話視圖
type WsSessionView struct {
	Id                 uint64    `json:"id" gorm:"primary_key"`
	SessionId          uint64    `json:"sessionId" gorm:"not null;index:session_id_idx"`                                 // 會話ID
	ProfessionalUserId uint64    `json:"professionalUserId" gorm:"not null;index:professional_user_id_idx"`              // 用戶ID (專業人士才有)
	FacilityId         uint64    `json:"facilityId" gorm:"not null;index:facility_id_idx"`                               // 機構ID (機構才有)
	LastInteractTime   time.Time `json:"lastInteractTime" gorm:"type:datetime(0);index:last_interact_time_idx;not null"` // 最後互動時間
	xmodel.Model
}

func (m *WsSessionView) TableName() string {
	return "ws_session_view"
}

func (m *WsSessionView) SwaggerDescription() string {
	return "聊天會話視圖"
}
