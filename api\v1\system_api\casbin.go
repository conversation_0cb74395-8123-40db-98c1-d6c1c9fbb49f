package system_api

import (
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xredis"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type CasbinController struct{}

func NewCasbinController() CasbinController {
	return CasbinController{}
}

// @Tags Policy
// @Summary 重載casbin
// @Router /v1/system/casbin/actions/reload [POST]
// @Produce  json
// @Security ApiKeyAuth
// @Param json body services.CasbinReloadReq true "parameter"
// @Success 200 "Success"
func (con CasbinController) Reload(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.CasbinReloadReq
	if err := c.ShouldBindJSON(&req); err == nil {
		err = xredis.Publish(c, services.CasbinTableName(req), "casbin reload")
		if err != nil {
			nc.ErrorResponse(nil, err)
			return
		}
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Casbin
// @Summary 新增Casbin
// @Description
// @Router /v1/system/casbin/actions/create [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.CasbinCreateReq true "parameter"
// @Success 200 {object} services.CasbinCreateResp "Success"
func (con CasbinController) Create(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.CasbinCreateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		// 檢查合法性
		var checkMsg []string
		checkMsg, err = xapp.NewCK(c).
			Run(func() (bool, i18n.Message, error) {
				return services.CasbinService.CheckCasbinUnique(db, req, req.Ptype, req.V0, req.V1, req.V2, req.V3, req.V4, req.V5)
			}).Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		resp, err := services.CasbinService.Create(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Casbin
// @Summary 獲取Casbin列表
// @Description
// @Router /v1/system/casbin [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.CasbinListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Success 200 {object} []services.CasbinListResp "Success"
func (con CasbinController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.CasbinListReq
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.CasbinService.List(db, req, &pageSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Casbin
// @Summary 修改Casbin
// @Description
// @Router /v1/system/casbin/actions/edit [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.CasbinEditReq true "parameter"
// @Success 200 "Success"
func (con CasbinController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.CasbinEditReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.CasbinService.CheckIdExist(db, req, &services.Casbin{}, req.CasbinId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.CasbinService.CheckCasbinUnique(db, req, req.Ptype, req.V0, req.V1, req.V2, req.V3, req.V4, req.V5, req.CasbinId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		tx := db.Begin()
		err := services.CasbinService.Edit(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Casbin
// @Summary 查询Casbin
// @Description
// @Router /v1/system/casbin/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.CasbinInquireReq true "parameter"
// @Success 200 {object} services.CasbinInquireResp "Success"
func (con CasbinController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.CasbinInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.CasbinService.Inquire(db, req)
		if xgorm.IsNotFoundErr(err) {
			nc.BadRequestResponse(err)
			return
		}
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Casbin
// @Summary 删除Casbin
// @Description
// @Router /v1/system/casbin/actions/delete [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.CasbinDeleteReq true "parameter"
// @Success 200 "Success"
func (con CasbinController) Delete(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.CasbinDeleteReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.CasbinService.CheckIdExist(db, req, &services.Casbin{}, req.CasbinId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		tx := db.Begin()
		err := services.CasbinService.Delete(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}
