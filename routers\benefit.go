package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/gin-gonic/gin"
)

func benefitFacilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		r.POST("/benefits/actions/create", facility_api.NewBenefitController().Create)
		r.GET("/benefits", facility_api.NewBenefitController().List)
		r.GET("/benefits/actions/search", facility_api.NewBenefitController().Search)
		r.POST("/benefits/actions/edit", facility_api.NewBenefitController().Edit)
		r.GET("/benefits/actions/inquire", facility_api.NewBenefitController().Inquire)
		r.POST("/benefits/actions/delete", facility_api.NewBenefitController().Delete)
	}
}
