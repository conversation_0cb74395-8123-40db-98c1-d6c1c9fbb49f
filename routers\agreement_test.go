package routers

import (
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
	"github.com/goccy/go-json"
)

var testAgreementId uint64

func TestAgreementCreate(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/agreements/actions/create",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "新增",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.AgreementCreateReq{
					Title:   "測試協議",
					Content: "測試協議內容",
				},
				CheckResultHandler: func(resp interface{}) bool {
					response := resp.(xresp.Response)
					var data services.AgreementCreateResp
					dataByte, _ := json.Marshal(response.Data)
					_ = json.Unmarshal(dataByte, &data)
					testAgreementId = data.AgreementId
					return true
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestAgreementList(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/agreements",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.AgreementListReq{},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestAgreementSearch(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/agreements/actions/search",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "搜索",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.AgreementSearchReq{},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestAgreementEdit(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/agreements/actions/edit",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "修改",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.AgreementEditReq{
					AgreementId: 3,
					Title:       "修改標題33",
					Content:     "修改內容3",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestAgreementInquire(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/agreements/actions/inquire",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.AgreementInquireReq{
					AgreementId: testAgreementId,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestAgreementDelete(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/agreements/actions/delete",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "刪除",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.AgreementDeleteReq{
					AgreementId: 63,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestAgreementFacilityInquire(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/facility-agreements/actions/inquire",
		UserId:           16,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.AgreementInquireByFacilityProfileReq{
					FacilityId:          7,
					FacilityAgreementId: 13,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
