package model

import (
	"github.com/Norray/xrocket/xmodel"
)

// 機構文件關聯
type FacilityFileRelation struct {
	Id                uint64 `json:"id" gorm:"primary_key"`
	FacilityId        uint64 `json:"facilityId" gorm:"index:facility_idx;not null"`                // 機構Id
	FacilityProfileId uint64 `json:"facilityProfileId" gorm:"index:facility_profile_idx;not null"` // 機構資料Id
	FacilityFileId    uint64 `json:"facilityFileId" gorm:"index:facility_file_idx;not null"`       // 機構文件Id
	xmodel.Model
}

func (FacilityFileRelation) TableName() string {
	return "facility_file_relation"
}

func (FacilityFileRelation) SwaggerDescription() string {
	return "機構文件關聯"
}
