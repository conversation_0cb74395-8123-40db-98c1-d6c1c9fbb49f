---
trigger: always_on
alwaysApply: true
---
# 項目開發規範

本項目開發規範旨在為項目開發提供統一的指導原則，涵蓋開發環境與版本控制、項目結構、代碼風格、第三方插件使用規範、API 設計、請求處理、錯誤處理、事務管理、服務層實現、數據庫規範、測試規範以及部署與運維。

## 目錄

1. [開發環境與版本控制](#1-開發環境與版本控制)
   - [1.1 開發環境](#11-開發環境-️)
   - [1.2 版本控制](#12-版本控制-)
     - [分支管理](#分支管理)
     - [Commit 信息格式](#commit-信息格式)
2. [項目結構](#2-項目結構)
   - [2.1 標準目錄結構](#21-標準目錄結構-)
3. [命名規範](#3-命名規範)
   - [3.1 基本規則](#31-基本規則)
   - [3.2 變量命名](#32-變量命名)
   - [3.3 代碼格式](#33-代碼格式-)
   - [3.4 Goland Inspections](#34-goland-inspections-)
   - [3.5 註釋規範](#35-註釋規範-)
4. [代碼結構規範](#4-代碼結構規範)
   - [4.1 導入包規範](#41-導入包規範)
   - [4.2 控制器結構體定義](#42-控制器結構體定義)
   - [4.3 服務結構](#43-服務結構)
5. [第三方插件使用規範](#5-第三方插件使用規範)
   - [5.1 社區活躍度評估](#51-社區活躍度評估-)
   - [5.2 開源協議審查](#52-開源協議審查-️)
   - [5.3 版本控制要求](#53-版本控制要求-)
   - [5.4 引入評估流程](#54-引入評估流程-)
6. [API 設計規範](#6-api-設計規範)
   - [6.1 API 路由設計](#61-api-路由設計-️)
   - [6.2 請求與響應](#62-請求與響應-)
   - [6.3 版本控制](#63-版本控制-️)
7. [API 文檔規範](#7-api-文檔規範)
8. [請求處理流程規範 (Controller)](#8-請求處理流程規範-controller)
9. [錯誤處理規範](#9-錯誤處理規範)
   - [9.1 參數綁定錯誤](#91-參數綁定錯誤)
   - [9.2 權限檢查錯誤](#92-權限檢查錯誤)
   - [9.3 參數驗證](#93-參數驗證)
10. [事務處理規範](#10-事務處理規範)
11. [服務層規範 (Service)](#11-服務層規範-service)
    - [11.1 數據驗證 (Service)](#111-數據驗證-service)
    - [11.2 常用服務方法](#112-常用服務方法)
12. [路由規範](#12-路由規範)
13. [數據庫規範](#13-數據庫規範)
    - [13.1 數據庫設計](#131-數據庫設計-)
    - [13.2 索引規範](#132-索引規範-)
    - [13.3 性能要求](#133-性能要求-)
14. [測試規範](#14-測試規範)
    - [14.1 單元測試](#141-單元測試-)
    - [14.2 技術文檔](#142-技術文檔-)
15. [部署與運維](#15-部署與運維)
    - [15.1 部署流程](#151-部署流程-)
16. [日誌規範](#16-日誌規範-)
    - [16.1 日誌級別](#161-日誌級別)
    - [16.2 日誌輸出配置](#162-日誌輸出配置)
    - [16.3 日誌記錄要求](#163-日誌記錄要求)

## 1. 開發環境與版本控制

### 1.1 開發環境 🛠️

- 使用統一的 IDE 配置和插件 (推薦 GoLand)
- AI 開發可使用 Cursor，但 Goland 用作代碼質量檢查標準

### 1.2 版本控制 📦

#### 分支管理
- 使用 Git 進行版本控制
- 分支命名規範：
  - 主分支：`main` 或 `master`
  - 開發分支：`develop`
  - 多人開發分支：`develop-username`（完成時合併至develop）
  - UAT分支：`uat`
  - 發布分支：`production`

#### Commit 信息格式

| Emoji | 類型 | 格式 | 用途 |
|-------|------|------|------|
| 🐛 | fix | `fix(yyy): xxxxxxx` | 修復錯誤 |
| ✨ | feat | `feat(yyy): xxxxxxx` | 新功能 |
| 💄 | style | `style(yyy): xxxxxxx` | 樣式修改 |
| 📝 | docs | `docs(yyy): xxxxxxx` | 文檔更新 |
| 🔧 | chore | `chore(yyy): xxxxxxx` | 維護工作 |
| 🚀 | build | `build(yyy): xxxxxxx` | 構建相關 |
| ♻️ | refactor | `refactor(yyy): xxxxxxx` | 重構 |
| ✅ | test | `test(yyy): xxxxxxx` | 測試相關 |

> **注意：**
> - yyy：相關功能名稱（使用英文）
> - xxxxxxx：詳細修改內容總結（使用繁體中文）

## 2. 項目結構

### 2.1 標準目錄結構 📁

```
project/
├── api/              # 接口層，基本不實現業務，處理權限和參數檢查為主
├── config/           # 配置
├── docs/             # swagger 文檔
├── hooks/            # Docker Hub 自動構建
├── internel/         # 定時任務和隊列任務
├── resource/         # 靜態資源（字體，IP數據庫等）
├── routers/          # 路由，API Test
├── services/         # 業務邏輯層
├── Dockerfile        # Docker 配置文件
├── gen_i18n.sh       # 生成翻譯文件
├── generator_test.go # Model 基礎 API 生成器
├── go.mod            # Go 依賴文件
├── go.sum            # Go 依賴文件
├── main.go           # 運行入口
└── README.md         # 項目介紹
```

> **提示：** 使用 `swag init --parseDependency --parseDepth 2` 生成 swagger 文檔

## 3. 命名規範

### 3.1 基本規則
- **包名：** 小寫，簡短有意義，不使用下劃線或其他符號
- **文件命名**：使用小寫字母和下劃線分隔，例如 `demo.go`、`demo_service.go`、`demo_test.go`
- **控制器結構體命名**：使用 PascalCase 命名法，例如 `DemoController`
- **服務結構體命名**：使用 PascalCase 命名法，例如 `DemoService`
- **方法命名**：使用 PascalCase 命名法，例如 `Create`、`List`、`Search`、`TestDemoInquire` 等
- **請求結構體命名**：使用 PascalCase 命名法，以 `Req` 結尾，例如 `DemoCreateReq`
- **響應結構體命名**：使用 PascalCase 命名法，以 `Resp` 結尾，例如 `DemoCreateResp`

### 3.2 變量命名
- **局部變量：** 小寫開頭的駝峰式，簡短清晰
- **全局變量：** 大寫開頭的駝峰式，語義完整
- **常量：** 大寫開頭的駝峰式，語義完整，如果常量值是文字使用全大寫，下劃線分隔

### 3.3 代碼格式 ⚡

#### 基本要求
- 使用 `gofmt` 格式化代碼
- 代碼規範：
  - 單行長度：≤ `120` 字符（大概），超過編輯器可視範圍時換行
  - 單個函數：≤ `50` 行
  - 適當添加空行提高可讀性
  - 移除未使用的變量、對象、方法

### 3.4 Goland Inspections 🔍

- 處理所有 `Error`、`Warning` 和 `Typo`
- 確認正確的拼寫錯誤請 `save to dictionary`
- 可忽略規則：
  - `Go > Code style issues > Comment of exported element starts with the incorrect name`
  - 使用 `defer` 時的 `Unhandled error`

### 3.5 註釋規範 💭

- 著重解釋「為什麼」而非「是什麼」
- 使用 `todo` 標記待完成代碼
- 所有靜態變量必須添加註釋

## 4. 代碼結構規範

### 4.1 導入包規範

- 內部包和外部包應分組導入，並使用空行分隔。

```go
// Controller/Service 層導入包範例
import (
    // 內部包
    "github.com/Norray/xxx/model"
    "github.com/Norray/xxx/xapp"
    // ...
    
    // 外部包
    "github.com/gin-gonic/gin"
    // ...
)
```

### 4.2 控制器結構體定義

- 控制器結構體應嵌入 `BaseControllers`。
- 應提供 `New` 方法來實例化控制器。

```go
// Controller 層結構體定義範例
type DemoController struct {
    BaseControllers
}

func NewDemoController() DemoController {
    return DemoController{}
}
```

### 4.3 服務結構

- 服務層應使用單例模式，通過全局變量提供服務。

```go
// Service 層結構體定義範例
var DemoService = new(demoService)

type demoService struct{}
```

## 5. 第三方插件使用規範

### 5.1 社區活躍度評估 👥

- 優先選擇 GitHub Stars 數量較多的項目
- 確認項目有持續更新維護（最近1年內有更新）
- 評估社區討論活躍度（Issues、Discussions）

### 5.2 開源協議審查 ⚖️

#### 允許商用的開源協議
- `MIT License`：最寬鬆，可自由使用、修改、分發
- `Apache License 2.0`：可商用，需包含原協議
- `BSD License`：可商用，需標註原作者版權

### 5.3 版本控制要求 📌

- 嚴禁使用 `latest` 標籤
- 必須在 `go.mod` 中明確指定版本號
- 鎖定具體版本（如：`v1.2.3`）

### 5.4 引入評估流程 ✅

1. 確認社區活躍度（Stars > 1000 優先考慮）
2. 檢查開源協議是否允許商用
3. 評估維護情況（定期更新、Issue 響應）
4. 確認版本穩定性和兼容性

## 6. API 設計規範

### 6.1 API 路由設計 🛣️

#### 基本原則
- 使用 kebab-case 命名路由
- 僅使用 GET（查詢）和 POST（修改）操作
- 採用動作 URL 風格：`/actions/xxxxx`

#### 標準操作名稱
- `/actions/create`
- `/actions/edit`
- `/actions/delete`
- `/actions/inquire`

### 6.2 請求與響應 🔄

#### 請求格式
- **GET 請求：**
  - 使用 `URL` 傳遞參數
- **POST 請求：**
  - 無文件：使用 `JSON`
  - 有文件：使用 `form-data`
  
#### 響應格式
- 文件響應：原始格式
- 其他響應：使用 `xapp.NGinCtx`

#### HTTP 狀態碼
| 狀態碼 | 含義 |
|--------|------|
| `200` | 成功 |
| `400` | 參數不正確 |
| `401` | 未認證授權 |
| `403` | 沒有權限 |
| `500` | 系統報錯 |

### 6.3 版本控制 🏷️

- URL 中包含版本：`/v1/...`
- 主版本號變更表示不兼容的 API 變化

## 7. API 文檔規範

Controller 層每個 API 方法必須包含以下 Swagger 註解：

```go
// @Tags [模塊名稱]
// @Summary [API 功能簡述]
// @Description [API 詳細描述]
// @Router [API 路徑] [HTTP 方法]
// @Produce json
// @Security ApiKeyAuth
// @Param [參數名] [參數位置] [參數類型] [是否必須] [參數描述]
// @Success [成功響應碼] {object} [響應對象類型] [成功描述]
```

## 8. 請求處理流程規範 (Controller)

每個 API 方法應遵循以下處理流程：

1.  **初始化上下文**：創建 `NGinCtx` 對象。
2.  **綁定請求參數**：使用 `ShouldBindJSON` 或 `ShouldBindQuery` 綁定請求參數。
3.  **權限檢查**：調用各項目中的權限檢查方法，檢查用戶是否有權限訪問指定資源。
4.  **參數驗證**：使用 `xapp.NewCK` 創建檢查器，驗證請求參數的有效性。
5.  **業務處理**：調用服務層方法處理業務邏輯。
6.  **響應返回**：根據處理結果返回適當的 HTTP 響應。

## 9. 錯誤處理規範

### 9.1 參數綁定錯誤

```go
// Controller 層參數綁定錯誤處理範例
nc := xapp.NGinCtx{C: c}
if err := c.ShouldBindJSON(&req); err == nil {
    // 處理請求
} else {
    nc.BadRequestResponse(err)
}
```

### 9.2 權限檢查錯誤

- 應在 `BaseControllers` 中實現權限檢查方法。

```go
// Controller 層權限檢查錯誤處理範例
if !con.CheckCompanyCanAccess(nc, db, req.CompanyId) { 
    nc.NoPermissionResponse(resource.ForbiddenMsg)
    return
}
```

### 9.3 參數驗證

- 使用 `xapp.NewCK` 進行參數驗證，並處理驗證結果。

```go
// Controller 層參數驗證範例
checker := xapp.NewCK(c)
checker.
    Run(func() (bool, i18n.Message, error) {
        // 驗證邏輯
    }).
    Run(func() (bool, i18n.Message, error) {
        // 更多驗證邏輯
    })
// 獲取驗證結果
checkMsg, err = checker.Result()
if err != nil {
    nc.ErrorResponse(req, err)
    return
}
if len(checkMsg) > 0 {
    nc.BadRequestResponseWithCheckMsg(checkMsg)
    return
}
```

## 10. 事務處理規範

對於需要修改數據的操作，應使用事務進行處理，並確保在錯誤時回滾事務。

```go
// Controller 層事務處理範例
tx := db.Begin()
// 執行數據修改操作
resp, err := services.DemoService.Create(tx, req)
if err != nil {
    tx.Rollback()
    nc.ErrorResponse(req, err)
    return
}
tx.Commit()
```

## 11. 服務層規範 (Service)

- 每個業務實體都應有對應的 `service`。
- `service` 的 Function 內部不應開啟事務，應由外部（通常是 Controller 層）開啟。
- Function 的傳入參數不應超過4個，若超過應建立結構體。
- Function 名應清晰表達功能。
- 所有 `error` 都應該返回。

### 11.1 數據驗證 (Service)
- 實體
```go
var DemoService = new(demoService)

type demoService struct{}
```

- 各種參數檢查應以獨立 Function 形式建立，並在 Controller 中通過 `checker` 調用。

```go
// Service 層數據驗證範例
func (s *demoService) CheckIdExist(db *gorm.DB, m *model.Demo, id uint64) (bool, i18n.Message, error)
```

### 11.2 常用服務方法

- **記錄創建**：`Create(db *gorm.DB, req DemoCreateReq) (DemoCreateResp, error)`
```go
func (s *demoService) Create(db *gorm.DB, req DemoCreateReq) (DemoCreateResp, error) {
    // 業務邏輯實現
    return DemoCreateResp{}, nil
}
```
- **記錄列表查詢**：`List(db *gorm.DB, req DemoListReq, pageSet *xresp.PageSet) ([]DemoListResp, error)` (支持分頁查詢)
- **記錄搜索**：`Search(db *gorm.DB, req DemoSearchReq) ([]DemoSearchResp, error)` (支持限制返回結果數量、優先返回指定 ID)
- **記錄編輯**：`Edit(db *gorm.DB, req DemoEditReq) error`
- **記錄詳情查詢**：`Inquire(db *gorm.DB, req DemoInquireReq) (DemoInquireResp, error)`
- **記錄刪除**：`Delete(db *gorm.DB, req DemoDeleteReq) error`

## 12. 路由規範

- 路由文件應使用小寫字母和下劃線分隔，例如 `demo.go`。
- 路由應註冊到合適的中間件之後。

```go
// Router 層路由定義範例
func demoRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
    r := Router.Group("/v1").Use(handlers...)
    {
        r.POST("/demos/actions/create", v1.NewDemoController().Create)
        // ... 其他路由
    }
}
```

## 13. 數據庫規範

### 13.1 數據庫設計 💾

#### 命名規則
- **表名：** 小寫，下劃線分隔
- **字段名：** 小寫，下劃線分隔

#### 必備字段
| 字段名 | 類型 | 說明 |
|--------|------|------|
| `id` | BIGINT (uint64) | 主鍵 |
| `created_at` | timestamp | 創建時間 |
| `updated_at` | timestamp | 更新時間 |

### 13.2 索引規範 🔑

- 為常用查詢字段建立索引
- 聯合索引遵循最左前綴原則

### 13.3 性能要求 🚀

- 單條 SQL 查詢不能超過`500ms`
- 預期單表超過`100萬`時需要做分表

## 14. 測試規範

### 14.1 單元測試 🧪

- `services` 層的複雜邏輯必須有單元測試
- `router` 層使用 `xtest` 包進行 API 測試
- 測試文件命名應為 `_test.go` 結尾，例如 `demo_test.go`
- 每個 API 端點都應有相應的單元測試用例，確保功能驗證和測試覆蓋
- Test 方法名應使用大寫字母開頭，例如 `TestDemoInquire`
- 測試應包含多個測試情境，如正常查詢、無效參數和權限不足等

```go
// Test 層測試用例範例
func TestDemoInquire(t *testing.T) {  
     test := xtest.Test{ 
         Url:              programPath + "/v1/demos/actions/inquire", 
         UserId:           90,   
         UserIdWithDevice: true, 
         Method:           xtest.Get, 
         ParamsType:       xtest.Query, 
         Name:             "查詢 demo 項目詳情", 
         Cases: []xtest.TestCase{ 
             { 
                 SubName:           "正常查詢", 
                 ExpectErrRespCode: xresp.StatusOK, 
                 Params: services.InquireDemoReq{  
                     CompanyId: 62, 
                     Uuid:      "X8PJNC8XGV3MQFVVSZVNEQTJ10", 
                 }, 
             }, 
             // ... 其他測試用例
         }, 
     } 
     xtest.RunTests(t, test) 
}
```

### 14.2 技術文檔 📚

#### README.md 必要內容
- 環境要求
- 安裝步驟
- 配置說明
- 部署文檔
- 常見問題解答

## 15. 部署與運維

### 15.1 部署流程 🚀

- 使用 `Docker` 或 `K8S` 進行容器化部署
- 嚴格遵循 `README.md` 的部署步驟

## 16. 日誌規範 📋

### 16.1 日誌級別
| 級別 | 使用場景 |
|------|----------|
| `ERROR` | 影響系統運行的錯誤 |
| `WARN` | 潛在的問題警告 |
| `INFO` | 常規日誌 |

### 16.2 日誌輸出配置

#### PHP 項目
- 日誌輸出到本地文件系統
- 按日期分割日誌文件

#### Golang 項目
- 日誌輸出到 Docker 容器標準輸出
- 使用日誌收集系統（如 EFK）統一管理

### 16.3 日誌記錄要求

#### 必須記錄內容
1. SQL 執行記錄
   - 完整 SQL 語句
   - 執行時間，毫秒級
   - 代碼所在行數

2. API 路由信息
   - 請求方法（GET/POST）
   - 請求路徑
   - 請求參數（出錯時才記錄）
   - 響應狀態碼
   - 響應時間

3. 用戶訪問信息
   - 用戶 IP 地址
   - 來源頁面 URL（Referer）

4. 追踪信息
   - 唯一 TraceID（使用這個 TraceID可以快速搜索出相關的SQL）