package facility_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

func NewWsMessageController() WsMessageController {
	return WsMessageController{}
}

type WsMessageController struct {
	v1.CommonController
}

// @Tags Facility WebSocket
// @Summary 機構 WebSocket 連接
// @Description 機構通過 WebSocket 與專業人士進行即時通訊
// @Router /v1/facility/ws/actions/connect [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.WsMessageReq true "parameter"
// @Success 200 "Success"
func (con WsMessageController) Connect(c *gin.Context) {
	// 處理 WebSocket 連接
	nc := xapp.NGinCtx{C: c}
	db := xgorm.DB.WithContext(c)
	facilityId, err := con.GetUserFacilityId(nc, db)
	if err != nil {
		nc.ErrorResponse(nil, err)
		return
	}
	err = services.WsMessageService.HandleWebSocketConnection(c, services.HandleWebSocketConnectionReq{
		FacilityId:  facilityId,
		ReqUserId:   nc.GetJWTUserId(),
		ReqUserType: model.WsMessageSenderTypeFacility,
	})
	if err != nil {
		nc.ErrorResponse(nil, err)
		return
	}
}

// @Tags Facility WebSocket
// @Summary 機構獲取聊天會話列表
// @Description 機構獲取與專業人士的聊天會話列表
// @Router /v1/facility/ws/sessions [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.WsSessionListReq true "parameter"
// @Success 200 {object} []services.WsSessionListResult "Success"
func (con WsMessageController) SessionList(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.WsSessionListReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		req.ReqUserId = nc.GetJWTUserId()
		req.ReqUserType = model.WsMessageSenderTypeFacility
		req.FacilityId, err = con.GetUserFacilityId(nc, db)
		if err != nil {
			nc.ErrorResponse(nil, err)
			return
		}

		if req.Limit > 50 || req.Limit <= 0 {
			req.Limit = 50
		}

		sessions, err := services.WsMessageService.FacilitySessionList(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(sessions)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility WebSocket
// @Summary 機構獲取聊天會話消息列表
// @Description 機構獲取與專業人士的聊天會話消息列表
// @Router /v1/facility/ws/messages [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.WsMessageListReq true "parameter"
// @Success 200 {object} []services.WsMessageResp "Success"
func (con WsMessageController) MessageList(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.WsMessageListReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		facilityId, err := con.GetUserFacilityId(nc, db)
		if err != nil {
			nc.ErrorResponse(nil, err)
			return
		}
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.WsMessageService.CheckFacilitySessionExist(db, facilityId, req.SessionUuid)
			})
		checkMsg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		if req.Limit > 50 || req.Limit <= 0 {
			req.Limit = 50
		}
		messages, err := services.WsMessageService.MessageList(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(messages)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility WebSocket
// @Summary 機構獲取未讀消息數量
// @Description 機構獲取未讀消息數量
// @Router /v1/facility/ws/actions/unread-count [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.UnreadCountReq true "parameter"
// @Success 200 {object} services.UnreadCountResp "Success"
func (con WsMessageController) GetUnreadCount(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.UnreadCountReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		facilityId, err := con.GetUserFacilityId(nc, db)
		if err != nil {
			nc.ErrorResponse(nil, err)
			return
		}
		resp, err := services.WsMessageService.UnreadCount(db, services.UnreadCountReq{
			UserType:   model.WsMessageSenderTypeFacility,
			UserId:     nc.GetJWTUserId(),
			FacilityId: facilityId,
		})
		if err != nil {
			nc.ErrorResponse(nil, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
