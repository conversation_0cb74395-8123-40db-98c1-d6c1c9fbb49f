package v1

import (
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xi18n"
	"github.com/Norray/xrocket/xmodel"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type AuthController struct{}

func NewAuthController() AuthController {
	return AuthController{}
}

// @Tags Auth
// @Summary 用戶通過賬戶密碼登入
// @Router /v1/login [POST]
// @Produce  json
// @Param json body services.LoginReq true "parameter"
// @Success 200 {object} services.LoginResp "Success"
// @Success 10000 "dk失效,重新獲取dk"
// @Success 20000 "賬號或密碼錯誤"
func (con AuthController) Login(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.LoginReq
	if e := c.ShouldBindJSON(&req); e == nil {
		db := xgorm.DB.WithContext(c)
		result, msg, err := services.AuthService.Login(c, db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if msg != nil {
			if msg.ID == services.MessageIDDeviceNotFound {
				nc.Response(xresp.AuthenticationNeedDk, []string{}, nil)
				return
			} else {
				nc.SingleAlertResponse(xi18n.Localize(c.Request, msg))
				return
			}
		}
		nc.OKResponse(result)
	} else {
		nc.BadRequestResponse(e)
	}
}

// @Tags Auth
// @Summary 忘記密碼
// @Description
// @Router /v1/auth/actions/forget-user-password [POST]
// @Produce  json
// @Security ApiKeyAuth
// @Param json body services.AuthForgetPasswordReq true "parameter"
// @Success 200 "Success"
func (con AuthController) ForgetUserPassword(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}

	var req services.AuthForgetPasswordReq
	if err := c.ShouldBind(&req); err == nil {
		var err error
		var m xmodel.User

		db := xgorm.DB.WithContext(c)

		// 如果沒有找到用戶，則直接返回成功
		exist, _, err := services.AuthService.CheckEmailExist(db, &m, req.Email)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if !exist {
			nc.OKResponse(nil)
			return
		}
		err = services.AuthService.ForgetUserPassword(c, req, m)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
		return
	}
}

// @Tags Auth
// @Summary 重置密碼
// @Description
// @Router /v1/auth/actions/reset-password [POST]
// @Produce  json
// @Security ApiKeyAuth
// @Param json body services.AuthResetPasswordReq true "parameter"
// @Success 200 "Success"
func (con AuthController) ResetPassword(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}

	var req services.AuthResetPasswordReq
	if err := c.ShouldBind(&req); err == nil {
		var err error
		var checkMsg []string

		checker := xapp.NewCK(c)
		checker.Run(func() (bool, i18n.Message, error) {
			return services.AuthService.ResetPassword(c, req)
		})

		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
		return
	}
}

// @Tags Auth
// @Summary 檢查重置密碼uuid是否有效
// @Description
// @Router /v1/auth/actions/reset-password-check [POST]
// @Produce  json
// @Param json body services.AuthCheckResetPasswordUuidReq true "parameter"
// @Success 200 "Success"
// @Success 10002 "uuid錯誤"
func (con AuthController) ResetPasswordUuidCheck(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.AuthCheckResetPasswordUuidReq
	var err error
	if err = c.ShouldBindJSON(&req); err == nil {
		keys, err := services.AuthService.CheckResetPasswordKeys(c, req.Uuid)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(keys) == 0 {
			nc.Response(xresp.ResetPasswordVerifyFailed, []string{}, nil)
			return
		}
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Auth
// @Summary 機構或專業人士授權Google（通過Google）
// @Description 機構或專業人士授權Google（通過Google）
// @Router /v1/google-oauth [POST]
// @Produce json
// @Param json body services.GoogleOAuthReq true "parameter"
// @Success 200 {object} services.GoogleOAuthResp "Success"
func (con AuthController) GoogleOAuth(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.GoogleOAuthReq
	if e := c.ShouldBindJSON(&req); e == nil {
		db := xgorm.DB.WithContext(c)

		checker := xapp.NewCK(c)
		checker.Run(func() (bool, i18n.Message, error) {
			return services.ReCaptchaService.CheckReCaptcha(db, req.RecaptchaToken, c.ClientIP())
		})
		if nc.BadRequestResponseIfCheckerFailed(checker, req) {
			return
		}

		result, err := services.AuthService.GenGoogleOAuthURL(nc, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(result)
	} else {
		nc.BadRequestResponse(e)
	}
}

// @Tags Auth
// @Summary 機構或專業人士Google回調
// @Description 機構或專業人士Google回調
// @Router /v1/google-callback [POST]
// @Produce json
// @Param json body services.GoogleCallbackReq true "parameter"
// @Success 200 {object} services.GoogleCallbackResp "Success"
func (con AuthController) GoogleCallback(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.GoogleCallbackReq
	if e := c.ShouldBindJSON(&req); e == nil {
		db := xgorm.DB.WithContext(c)

		tx := db.Begin()
		result, msg, err := services.AuthService.GoogleCallback(nc, tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		if msg != nil {
			tx.Rollback()
			nc.SingleAlertResponse(xi18n.Localize(c.Request, msg))
			return
		}
		tx.Commit()

		nc.OKResponse(result)
	} else {
		nc.BadRequestResponse(e)
	}
}
