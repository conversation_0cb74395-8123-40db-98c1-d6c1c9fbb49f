package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/Norray/medic-crew/api/v1/professional_api"
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func professionalInvoiceRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	router := Router.Group("/v1/professional").Use(handlers...)
	{
		router.GET("/invoices", professional_api.NewInvoiceController().List)
		router.GET("/invoices/actions/inquire", professional_api.NewInvoiceController().Inquire)
		router.POST("/invoices/actions/payment-received", professional_api.NewInvoiceController().InvoicePaymentReceived)
	}
}

func facilityInvoiceRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	router := Router.Group("/v1/facility").Use(handlers...)
	{
		router.GET("/invoices", facility_api.NewInvoiceController().List)
		router.GET("/invoices/actions/inquire", facility_api.NewInvoiceController().Inquire)
	}
}

func systemInvoiceRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	router := Router.Group("/v1/system").Use(handlers...)
	{
		controller := system_api.NewInvoiceController()
		router.GET("/invoices", controller.List)
		router.GET("/invoices/actions/inquire", controller.Inquire)
		router.POST("/invoices/actions/create", controller.Create)
		router.POST("/invoices/actions/edit", controller.Edit)
		router.POST("/invoices/actions/confirm-payment", controller.ConfirmPayment)
	}
}
