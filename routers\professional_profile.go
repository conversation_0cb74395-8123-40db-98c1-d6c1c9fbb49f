package routers

import (
	"github.com/Norray/medic-crew/api/v1/professional_api"
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func professionalProfilePublicRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/professional").Use(handlers...)
	{
		controller := professional_api.NewProfessionalReferenceFormController()
		r.GET("/reference-forms/actions/inquire", controller.Inquire)
		r.GET("/reference-forms/actions/preview", controller.Preview)
		r.POST("/reference-forms/actions/verify", controller.Verify)
		r.POST("/reference-forms/actions/send-verification-code", controller.SendVerificationCode)
		r.POST("/reference-forms/actions/save", controller.Save)
		r.POST("/reference-forms/actions/gen-upload-qrcode", controller.GenUploadQrcode)
	}
}

func professionalProfileRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/professional").Use(handlers...)
	{
		controller := professional_api.NewProfessionalProfileController()
		r.GET("/professional-profiles/actions/inquire", controller.Inquire)
		r.GET("/professional-profiles/actions/progress", controller.Progress)
		r.GET("/professional-profiles/actions/check-abn", controller.CheckAbn)
		r.POST("/professional-profiles/actions/init", controller.Init)
		r.POST("/professional-profiles/actions/edit", controller.Edit)
		r.POST("/professional-profiles/actions/submit", controller.Submit)
		r.POST("/professional-profiles/actions/withdraw", controller.Withdraw)
		r.GET("/professional-profiles/actions/payment-detail", controller.PaymentDetail)
		r.POST("/professional-profiles/actions/update-payment-detail", controller.UpdatePaymentDetail)
		r.GET("/professional-profiles/actions/compare", controller.Compare)
	}
}

func professionalProfileSystemRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/system").Use(handlers...)
	{
		controller := system_api.NewProfessionalProfileController()
		r.GET("/professional-profiles", controller.List)
		r.GET("/professional-profiles/actions/inquire", controller.Inquire)
		r.GET("/professional-profiles/actions/compare", controller.Compare)
		r.GET("/professional-profiles/actions/inquire-reference", controller.InquireReference)
		r.POST("/professional-profiles/actions/approve-reference", controller.ApproveReference)
		r.GET("/professional-profiles/actions/payment-detail", controller.PaymentDetail)
		r.POST("/professional-profiles/actions/approve", controller.Approve)
		r.POST("/professional-profiles/actions/deactivate", controller.Deactivate)
	}
}
