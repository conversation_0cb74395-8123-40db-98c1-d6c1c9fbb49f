package system_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type CommissionController struct {
	v1.CommonController
}

func NewCommissionController() CommissionController {
	return CommissionController{}
}

// @Tags Commission
// @Summary 新增佣金等級
// @Description
// @Router /v1/system/commissions/actions/create [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.CommissionCreateReq true "parameter"
// @Success 200 {object} services.CommissionCreateResp "Success"
func (con CommissionController) Create(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.CommissionCreateReq
	if err := c.ShouldBindJ<PERSON>N(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		tx := db.Begin()
		resp, err := services.CommissionService.Create(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Commission
// @Summary 獲取佣金等級列表
// @Description
// @Router /v1/system/commissions [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.CommissionListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Success 200 {object} []services.CommissionListResp "Success"
func (con CommissionController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.CommissionListReq
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		resp, err := services.CommissionService.List(db, req, &pageSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Commission
// @Summary 搜索佣金等級
// @Description
// @Router /v1/system/commissions/actions/search [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.CommissionSearchReq true "parameter"
// @Success 200 {object} []services.CommissionSearchResp "Success"
func (con CommissionController) Search(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.CommissionSearchReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		resp, err := services.CommissionService.Search(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Commission
// @Summary 修改佣金等級
// @Description
// @Router /v1/system/commissions/actions/edit [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.CommissionEditReq true "parameter"
// @Success 200 "Success"
func (con CommissionController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.CommissionEditReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var commission model.Commission
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.CommissionService.CheckIdExist(db, &commission, req.CommissionId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.CommissionService.Edit(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Commission
// @Summary 查詢佣金等級
// @Description
// @Router /v1/system/commissions/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.CommissionInquireReq true "parameter"
// @Success 200 {object} services.CommissionInquireResp "Success"
func (con CommissionController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.CommissionInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var commission model.Commission
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.CommissionService.CheckIdExist(db, &commission, req.CommissionId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		resp, err := services.CommissionService.Inquire(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Commission
// @Summary 刪除佣金等級
// @Description
// @Router /v1/system/commissions/actions/delete [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.CommissionDeleteReq true "parameter"
// @Success 200 "Success"
func (con CommissionController) Delete(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.CommissionDeleteReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var commission model.Commission
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.CommissionService.CheckIdExist(db, &commission, req.CommissionId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.CommissionService.Delete(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()

		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}
