package professional_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

func NewWsMessageController() WsMessageController {
	return WsMessageController{}
}

type WsMessageController struct {
	v1.CommonController
}

// @Tags Professional WebSocket
// @Summary 專業人士 WebSocket 連接
// @Description 專業人士通過 WebSocket 與機構進行即時通訊
// @Router /v1/professional/ws/actions/connect [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.WsMessageReq true "parameter"
// @Success 200 "Success"
func (con WsMessageController) Connect(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	err := services.WsMessageService.HandleWebSocketConnection(c, services.HandleWebSocketConnectionReq{
		ReqUserId:   nc.GetJWTUserId(),
		ReqUserType: model.WsMessageSenderTypeProfessional,
	})
	if err != nil {
		nc.ErrorResponse(nil, err)
		return
	}
}

// @Tags Professional WebSocket
// @Summary 專業人士獲取聊天會話列表
// @Description 專業人士獲取與機構的聊天會話列表
// @Router /v1/professional/ws/sessions [get]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.WsSessionListReq true "parameter"
// @Success 200 {object} []services.WsSessionListResult "Success"
// @Success 200 "Success"
func (con WsMessageController) SessionList(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.WsSessionListReq

	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.ReqUserId = nc.GetJWTUserId()
		req.ReqUserType = model.WsMessageSenderTypeProfessional

		sessions, err := services.WsMessageService.ProfessionalSessionList(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(sessions)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional WebSocket
// @Summary 專業人士獲取聊天會話消息列表
// @Description 專業人士獲取與機構的聊天會話消息列表
// @Router /v1/professional/ws/messages [get]
// @Produce json
// @Security ApiKeyAuth
// @Param roomId query string true "聊天會話ID"
// @Param page query int false "頁碼"
// @Param pageSize query int false "每頁數量"
// @Success 200 {object} []services.WsMessageResp "Success"
func (con WsMessageController) MessageList(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.WsMessageListReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		professionalUserId := nc.GetJWTUserId()
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.WsMessageService.CheckProfessionalSessionExist(db, professionalUserId, req.SessionUuid)
			})
		checkMsg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		messages, err := services.WsMessageService.MessageList(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(messages)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional WebSocket
// @Summary 專業人士獲取未讀消息數量
// @Description 專業人士獲取未讀消息數量
// @Router /v1/professional/ws/actions/unread-count [GET]
// @Produce json
// @Security ApiKeyAuth
// @Success 200 "Success"
func (con WsMessageController) GetUnreadCount(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	db := xgorm.DB.WithContext(c)
	userId := nc.GetJWTUserId()

	count, err := services.WsMessageService.UnreadCount(db, services.UnreadCountReq{
		UserType: model.WsMessageSenderTypeProfessional,
		UserId:   userId,
	})
	if err != nil {
		nc.ErrorResponse(nil, err)
		return
	}

	nc.OKResponse(gin.H{
		"unreadCount": count,
	})
}
