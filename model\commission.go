package model

import (
	"github.com/Norray/xrocket/xmodel"
	"github.com/shopspring/decimal"
)

// 機構的提成等級檔案
type Commission struct {
	Id             uint64          `json:"id" gorm:"primary_key"`
	Level          string          `json:"level" gorm:"type:varchar(255);not null"`
	CommissionRate decimal.Decimal `json:"commission_rate" gorm:"type:decimal(6,4);not null"`
	xmodel.Model
}

func (Commission) TableName() string {
	return "commission"
}

func (Commission) SwaggerDescription() string {
	return "機構的提成等級檔案"
}
