package facility_api

import (
	"github.com/gin-gonic/gin/binding"

	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

// JobScheduleController 工作排程控制器
type JobScheduleController struct {
	v1.CommonController
}

// 創建控制器實例
func NewJobScheduleController() JobScheduleController {
	return JobScheduleController{}
}

// @Tags Job Schedule
// @Summary 查詢工作排程列表
// @Description 查詢特定工作的排程列表
// @Router /v1/facility/job-schedules [GET]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.JobScheduleListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Param sortingKey query string false "createTime 建立時間,beginTime 開始時間"
// @Param sortingType query string false "a=順序,d=倒序"
// @Success 200 {object} []services.JobScheduleListResp "Success"
func (ctrl JobScheduleController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobScheduleListReq
	var pageSet xresp.PageSet
	var sortSet xresp.SortingSet
	_ = c.ShouldBindQuery(&pageSet)
	_ = c.ShouldBindQuery(&sortSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		// 檢查用戶是否有權限查看此工作相關數據
		if !ctrl.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.JobScheduleService.List(db, req, &pageSet, sortSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Job Schedule
// @Summary 查詢工作排程詳情
// @Description 查詢指定工作排程的詳細信息，包括排程詳情和關聯的工作信息
// @Router /v1/facility/job-schedules/actions/inquire [GET]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.JobScheduleInquireReq true "parameter"
// @Success 200 {object} services.JobScheduleInquireResp "Success"
func (ctrl JobScheduleController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobScheduleInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		// 檢查用戶是否有權限查看此排程
		if !ctrl.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		// 使用 Checker 進行檢查
		var jobSchedule model.JobSchedule
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.Run(func() (bool, i18n.Message, error) {
			return services.JobScheduleService.CheckIdExist(db, &jobSchedule, req.JobScheduleId, req.FacilityId)
		})

		checkMsg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		// 查詢詳細信息
		resp, err := services.JobScheduleService.Inquire(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Job Schedule
// @Summary 創建工作排程
// @Description 為指定工作創建排程計劃，如未提供JobId則創建新工作
// @Router /v1/facility/job-schedules/actions/create [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.JobScheduleCreateReq true "parameter"
// @Success 200 {object} services.JobScheduleCreateResp "Success"
func (ctrl JobScheduleController) Create(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var err error
	var req services.JobScheduleCreateReq
	var draftReq services.CreateJobScheduleDraftReq

	// 先嘗試以草稿模式綁定，檢查是否為草稿模式
	if err = c.ShouldBindBodyWith(&draftReq, binding.JSON); err != nil {
		nc.BadRequestResponse(err)
		return
	}
	if draftReq.Draft == "Y" {
		_ = copier.Copy(&req, draftReq)
	} else if err = c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		nc.BadRequestResponse(err)
		return
	}

	db := xgorm.DB.WithContext(c)

	// 檢查用戶是否有權限創建排程
	if !ctrl.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
		nc.NoPermissionResponse(resource.ForbiddenMsg)
		return
	}

	// Checker
	checker := xapp.NewCK(c, true)

	if req.Draft != "Y" {
		var serviceLocation model.ServiceLocation
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.JobScheduleService.CheckShiftTimeRange(req.JobShiftItems)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.ServiceLocationService.CheckIdExist(db, &serviceLocation, req.ServiceLocationId, req.FacilityId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.JobScheduleService.CheckShiftTime(req.JobShiftItems, serviceLocation.Timezone)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.JobScheduleService.CheckAllowances(db, req.Allowances)
			}).
			Run(func() (bool, i18n.Message, error) {
				if len(req.Files) == 0 {
					return true, i18n.Message{}, nil
				}
				return services.FacilityFileService.CheckIdsExist(db, req.FacilityId, req.Files)
			})
	}

	// 檢查重複類型設置
	checker.Run(func() (bool, i18n.Message, error) {
		if req.Draft == "Y" && req.RepeatType == "" {
			return true, i18n.Message{}, nil
		}
		return services.JobScheduleService.CheckRepeatTypeSettings(req)
	})

	// 檢查日期範圍
	checker.
		Run(func() (bool, i18n.Message, error) {
			if req.Draft == "Y" && (req.BeginDate == "" || req.EndDate == "") {
				return true, i18n.Message{}, nil
			}
			return services.JobScheduleService.CheckDateRange(req.BeginDate, req.EndDate)
		}).
		Run(func() (bool, i18n.Message, error) {
			if req.Draft == "Y" && (req.BeginDate == "" || req.EndDate == "") {
				return true, i18n.Message{}, nil
			}
			return services.FacilityAgreementService.CheckJobTimeInRange(db, req.FacilityId, req.BeginDate, req.EndDate)
		})

	// 檢查Selection
	ctrl.CheckSelectionExist(checker, db, model.SelectionTypeProfessionalProfession, req.PositionProfession)
	ctrl.CheckSelectionsExist(checker, db, model.SelectionTypeLanguage, req.Language)

	switch req.PositionProfession {
	case model.JobPositionProfessionMedicalPractitioner:
		ctrl.CheckSelectionsExist(checker, db, model.SelectionTypeExperienceLevelMedicalPractitioner, req.MinExperienceLevel)
	case model.JobPositionProfessionRegisteredNurse:
		ctrl.CheckSelectionsExist(checker, db, model.SelectionTypeExperienceLevelRegisteredNurse, req.MinExperienceLevel)
	case model.JobPositionProfessionEnrolledNurse, model.JobPositionProfessionPersonalCareWorker:
		ctrl.CheckSelectionsExist(checker, db, model.SelectionTypeExperienceLevelGeneral, req.MinExperienceLevel)
	}
	if req.Specialisation != "" {
		checker.Run(func() (bool, i18n.Message, error) {
			return services.ProfessionalProfileService.CheckPreferredSpecialities(db, req.PositionProfession, req.Specialisation)
		})
	}

	// 檢查當前設定是否能生成有效的工作日期
	if req.Draft != "Y" {
		checker.Run(func() (bool, i18n.Message, error) {
			return services.JobScheduleService.CheckHasValidJobDates(db, req)
		})
	}

	checkMsg, err := checker.Result()
	if err != nil {
		nc.ErrorResponse(req, err)
		return
	}
	if len(checkMsg) > 0 {
		nc.BadRequestResponseWithCheckMsg(checkMsg)
		return
	}

	req.CreatedUserId = nc.GetJWTUserId()

	tx := db.Begin()
	resp, err := services.JobScheduleService.Create(tx, req)
	if err != nil {
		tx.Rollback()
		nc.ErrorResponse(req, err)
		return
	}
	tx.Commit()
	nc.OKResponse(resp)
}

// @Tags Job Schedule
// @Summary 編輯工作排程
// @Description 編輯指定的工作排程計劃和相關工作信息
// @Router /v1/facility/job-schedules/actions/edit [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.JobScheduleEditReq true "parameter"
// @Success 200 {object} services.UpdateJobScheduleResp "Success"
func (ctrl JobScheduleController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var err error
	var req services.JobScheduleEditReq
	var draftReq services.JobScheduleEditDraftReq

	// 先嘗試以草稿模式綁定，檢查是否為草稿模式
	if err = c.ShouldBindBodyWith(&draftReq, binding.JSON); err != nil {
		nc.BadRequestResponse(err)
		return
	}
	if draftReq.Draft == "Y" {
		_ = copier.Copy(&req, draftReq)
	} else if err = c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		nc.BadRequestResponse(err)
		return
	}

	db := xgorm.DB.WithContext(c)
	// 檢查排程是否存在
	var schedule model.JobSchedule
	exists, errMsg, err := services.JobScheduleService.CheckIdExist(db, &schedule, req.JobScheduleId, req.FacilityId)
	if err != nil {
		nc.ErrorResponse(req, err)
		return
	}
	if !exists {
		nc.BadRequestResponseWithCheckMsg([]string{errMsg.Other})
		return
	}

	// 檢查用戶是否有權限編輯此排程
	if !ctrl.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: schedule.FacilityId}) {
		nc.NoPermissionResponse(resource.ForbiddenMsg)
		return
	}

	// Checker
	checker := xapp.NewCK(c, true)

	if req.Draft == "Y" {
		var serviceLocation model.ServiceLocation
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.JobScheduleService.CheckShiftTimeRange(req.JobShiftItems)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.ServiceLocationService.CheckIdExist(db, &serviceLocation, req.ServiceLocationId, req.FacilityId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.JobScheduleService.CheckShiftTime(req.JobShiftItems, serviceLocation.Timezone)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.JobScheduleService.CheckAllowances(db, req.Allowances)
			}).
			Run(func() (bool, i18n.Message, error) {
				if len(req.Files) == 0 {
					return true, i18n.Message{}, nil
				}
				return services.FacilityFileService.CheckIdsExist(db, req.FacilityId, req.Files)
			})
	}
	// 檢查重複類型設置
	checker.Run(func() (bool, i18n.Message, error) {
		if req.Draft == "Y" && req.RepeatType == "" {
			return true, i18n.Message{}, nil
		}
		return services.JobScheduleService.CheckRepeatTypeSettings(services.JobScheduleCreateReq{
			RepeatType:        req.RepeatType,
			DailyInterval:     req.DailyInterval,
			WeeklyInterval:    req.WeeklyInterval,
			WeekDays:          req.WeekDays,
			MonthlyInterval:   req.MonthlyInterval,
			MonthlyType:       req.MonthlyType,
			MonthlyDayOfMonth: req.MonthlyDayOfMonth,
			MonthlyWeekIndex:  req.MonthlyWeekIndex,
			MonthlyWeekDay:    req.MonthlyWeekDay,
		})
	})

	// 檢查日期範圍
	if req.EndDate != "" {
		checker.
			Run(func() (bool, i18n.Message, error) {
				if req.Draft == "Y" && (req.BeginDate == "" || req.EndDate == "") {
					return true, i18n.Message{}, nil
				}
				return services.JobScheduleService.CheckDateRange(req.BeginDate, req.EndDate)
			}).
			Run(func() (bool, i18n.Message, error) {
				if req.Draft == "Y" && (req.BeginDate == "" || req.EndDate == "") {
					return true, i18n.Message{}, nil
				}
				return services.FacilityAgreementService.CheckJobTimeInRange(db, req.FacilityId, req.BeginDate, req.EndDate)
			})
	}

	// 檢查Selection
	ctrl.CheckSelectionExist(checker, db, model.SelectionTypeProfessionalProfession, req.PositionProfession)
	ctrl.CheckSelectionsExist(checker, db, model.SelectionTypeLanguage, req.Language)

	switch req.PositionProfession {
	case model.JobPositionProfessionMedicalPractitioner:
		ctrl.CheckSelectionsExist(checker, db, model.SelectionTypeExperienceLevelMedicalPractitioner, req.MinExperienceLevel)
	case model.JobPositionProfessionRegisteredNurse:
		ctrl.CheckSelectionsExist(checker, db, model.SelectionTypeExperienceLevelRegisteredNurse, req.MinExperienceLevel)
	case model.JobPositionProfessionEnrolledNurse, model.JobPositionProfessionPersonalCareWorker:
		ctrl.CheckSelectionsExist(checker, db, model.SelectionTypeExperienceLevelGeneral, req.MinExperienceLevel)
	}
	if req.Specialisation != "" {
		checker.Run(func() (bool, i18n.Message, error) {
			return services.ProfessionalProfileService.CheckPreferredSpecialities(db, req.PositionProfession, req.Specialisation)
		})
	}

	// 檢查當前設定是否能生成有效的工作日期
	if req.Draft != "Y" {
		checker.Run(func() (bool, i18n.Message, error) {
			var createReq services.JobScheduleCreateReq
			_ = copier.Copy(&createReq, req)
			return services.JobScheduleService.CheckHasValidJobDates(db, createReq)
		})
	}

	checkMsg, err := checker.Result()
	if err != nil {
		nc.ErrorResponse(req, err)
		return
	}
	if len(checkMsg) > 0 {
		nc.BadRequestResponseWithCheckMsg(checkMsg)
		return
	}

	req.UpdatedUserId = nc.GetJWTUserId()
	tx := db.Begin()
	err = services.JobScheduleService.Edit(tx, req)
	if err != nil {
		tx.Rollback()
		nc.ErrorResponse(req, err)
		return
	}
	tx.Commit()
	nc.OKResponse(nil)
}

// @Tags Job Schedule
// @Summary 更新工作排程狀態
// @Description 更新指定的工作排程計劃的狀態(啟用/停用)
// @Router /v1/facility/job-schedules/actions/update-status [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.JobScheduleUpdateStatusReq true "parameter"
// @Success 200 "Success"
func (ctrl JobScheduleController) UpdateStatus(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobScheduleUpdateStatusReq

	// 解析請求參數
	if err := c.ShouldBindJSON(&req); err != nil {
		nc.BadRequestResponse(err)
		return
	}

	db := xgorm.DB.WithContext(c)

	// 檢查用戶是否有權限刪除此排程
	if !ctrl.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
		nc.NoPermissionResponse(resource.ForbiddenMsg)
		return
	}

	// 使用 Checker 進行檢查
	var jobSchedule model.JobSchedule
	var checkMsg []string
	checker := xapp.NewCK(c)
	checker.
		Run(func() (bool, i18n.Message, error) {
			return services.JobScheduleService.CheckIdExist(db, &jobSchedule, req.JobScheduleId, req.FacilityId)
		}).
		Run(func() (bool, i18n.Message, error) {
			return services.JobScheduleService.CheckCanUpdateStatus(db, req.JobScheduleId, req.Status)
		})

	checkMsg, err := checker.Result()
	if err != nil {
		nc.ErrorResponse(req, err)
		return
	}
	if len(checkMsg) > 0 {
		nc.BadRequestResponseWithCheckMsg(checkMsg)
		return
	}

	// 檢查用戶是否有權限更新此排程狀態
	if !ctrl.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: jobSchedule.FacilityId}) {
		nc.NoPermissionResponse(resource.ForbiddenMsg)
		return
	}

	tx := db.Begin()
	err = services.JobScheduleService.UpdateJobScheduleStatus(tx, req)
	if err != nil {
		tx.Rollback()
		nc.ErrorResponse(req, err)
		return
	}

	tx.Commit()
	nc.OKResponse(nil)
}

// @Tags Job Schedule
// @Summary 刪除工作排程
// @Description 刪除指定的工作排程計劃及相關資料
// @Router /v1/facility/job-schedules/actions/delete [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.JobScheduleDeleteReq true "parameter"
// @Success 200 "Success"
func (ctrl JobScheduleController) Delete(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobScheduleDeleteReq

	// 解析請求參數
	if err := c.ShouldBindJSON(&req); err != nil {
		nc.BadRequestResponse(err)
		return
	}

	db := xgorm.DB.WithContext(c)

	// 檢查用戶是否有權限刪除此排程
	if !ctrl.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
		nc.NoPermissionResponse(resource.ForbiddenMsg)
		return
	}

	// 使用 Checker 進行檢查
	var checkMsg []string
	checker := xapp.NewCK(c)
	checker.
		Run(func() (bool, i18n.Message, error) {
			return services.JobScheduleService.CheckIdExist(db, &model.JobSchedule{}, req.JobScheduleId, req.FacilityId)
		}).
		Run(func() (bool, i18n.Message, error) {
			return services.JobScheduleService.CheckCanDelete(db, req.FacilityId, req.JobScheduleId)
		})

	checkMsg, err := checker.Result()
	if err != nil {
		nc.ErrorResponse(req, err)
		return
	}
	if len(checkMsg) > 0 {
		nc.BadRequestResponseWithCheckMsg(checkMsg)
		return
	}

	// 執行刪除操作
	tx := db.Begin()
	err = services.JobScheduleService.Delete(tx, req)
	if err != nil {
		tx.Rollback()
		nc.ErrorResponse(req, err)
		return
	}

	tx.Commit()
	nc.OKResponse(nil)
}

// @Tags Job Schedule
// @Summary 計算排程可能生成的工作數量
// @Description 根據排程參數，計算可能生成的工作數量及日期列表
// @Router /v1/facility/job-schedules/actions/calc-potential-count [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.JobSchedulePotentialCountReq true "parameter"
// @Success 200 {object} services.JobSchedulePotentialCountResp "Success"
func (ctrl JobScheduleController) CalcPotentialJobCount(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobSchedulePotentialCountReq

	// 解析請求參數
	if err := c.ShouldBindJSON(&req); err != nil {
		nc.BadRequestResponse(err)
		return
	}

	db := xgorm.DB.WithContext(c)

	// 檢查用戶是否有權限刪除此排程
	if !ctrl.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
		nc.NoPermissionResponse(resource.ForbiddenMsg)
		return
	}
	// Checker
	checker := xapp.NewCK(c, true)

	if req.JobScheduleId > 0 {
		checker.Run(func() (bool, i18n.Message, error) {
			return services.JobScheduleService.CheckIdExist(db, &model.JobSchedule{}, req.JobScheduleId, req.FacilityId)
		})
	}
	var serviceLocation model.ServiceLocation
	checker.Run(func() (bool, i18n.Message, error) {
		return services.ServiceLocationService.CheckIdExist(db, &serviceLocation, req.ServiceLocationId, req.FacilityId)
	})

	// 檢查排程參數是否有效
	checker.
		Run(func() (bool, i18n.Message, error) {
			return services.JobScheduleService.CheckShiftTimeRange(req.JobShiftItems)
		}).
		Run(func() (bool, i18n.Message, error) {
			return services.JobScheduleService.CheckShiftTime(req.JobShiftItems, serviceLocation.Timezone)
		})

	// 檢查重複類型設置
	checker.Run(func() (bool, i18n.Message, error) {
		return services.JobScheduleService.CheckRepeatTypeSettings(services.JobScheduleCreateReq{
			RepeatType:        req.RepeatType,
			DailyInterval:     req.DailyInterval,
			WeeklyInterval:    req.WeeklyInterval,
			WeekDays:          req.WeekDays,
			MonthlyInterval:   req.MonthlyInterval,
			MonthlyType:       req.MonthlyType,
			MonthlyDayOfMonth: req.MonthlyDayOfMonth,
			MonthlyWeekIndex:  req.MonthlyWeekIndex,
			MonthlyWeekDay:    req.MonthlyWeekDay,
		})
	})

	// 檢查日期範圍
	checker.
		Run(func() (bool, i18n.Message, error) {
			return services.JobScheduleService.CheckDateRange(req.BeginDate, req.EndDate)
		}).
		Run(func() (bool, i18n.Message, error) {
			return services.FacilityAgreementService.CheckJobTimeInRange(db, req.FacilityId, req.BeginDate, req.EndDate)
		})

	// 檢查結果處理
	checkMsg, err := checker.Result()
	if err != nil {
		nc.ErrorResponse(req, err)
		return
	}
	if len(checkMsg) > 0 {
		nc.BadRequestResponseWithCheckMsg(checkMsg)
		return
	}

	// 計算工作數量
	resp, err := services.JobScheduleService.CalcPotentialJobCount(db, req)
	if err != nil {
		nc.ErrorResponse(req, err)
		return
	}

	nc.OKResponse(resp)
}
