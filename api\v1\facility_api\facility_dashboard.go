package facility_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
)

type FacilityDashboardController struct {
	v1.CommonController
}

func NewFacilityDashboardController() FacilityDashboardController {
	return FacilityDashboardController{}
}

// @Tags FacilityDashboard
// @Summary 機構儀表板匯總統計
// @Description 獲取機構在指定時間範圍內的總支出、已完成工作數和受聘專業人員總數
// @Router /v1/facility/dashboard/summary [GET]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FacilityDashboardSummaryReq true "parameter"
// @Success 200 {object} services.FacilityDashboardSummaryResp "Success"
func (con FacilityDashboardController) DashboardSummary(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityDashboardSummaryReq

	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.FacilityDashboardService.DashboardSummary(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags FacilityDashboard
// @Summary 機構支出統計
// @Description 獲取機構在指定時間範圍內的每日支出統計，包含薪資、佣金和其他費用的詳細分類
// @Router /v1/facility/dashboard/expense [GET]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FacilityExpenseReq true "parameter"
// @Success 200 {object} []services.PeriodExpenseItem "Success"
func (con FacilityDashboardController) FacilityExpense(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityExpenseReq

	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.FacilityDashboardService.FacilityExpense(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags FacilityDashboard
// @Summary 機構職業發佈工作量統計
// @Description 獲取機構在指定時間範圍內的每日職業發佈工作量統計
// @Router /v1/facility/dashboard/job-statistic [GET]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FacilityJobStatisticReq true "parameter"
// @Success 200 {object} []services.PeriodJobStatisticItem "Success"
func (con FacilityDashboardController) FacilityJobStatistic(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityJobStatisticReq

	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.FacilityDashboardService.FacilityJobStatistic(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags FacilityDashboard
// @Summary 機構職業工資統計
// @Description 獲取機構每種職業工資統計
// @Router /v1/facility/dashboard/job-salary-statistic [GET]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FacilityJobSalaryStatisticReq true "parameter"
// @Success 200 {object} []services.PeriodJobSalaryStatisticItem "Success"
func (con FacilityDashboardController) FacilityJobSalaryStatistic(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityJobSalaryStatisticReq

	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.FacilityDashboardService.FacilityJobSalaryStatistic(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
