package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/Norray/medic-crew/api/v1/professional_api"
	"github.com/gin-gonic/gin"
)

func systemNotificationProfessionalRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/professional").Use(handlers...)
	{
		controller := professional_api.NewSystemNotificationController()
		r.GET("/system-notifications", controller.GetList)                              // 獲取用戶通知列表
		r.POST("/system-notifications/actions/mark-read", controller.MarkAsRead)        // 標記通知為已讀
		r.POST("/system-notifications/actions/mark-all-read", controller.MarkAllAsRead) // 標記所有通知為已讀
		r.POST("/system-notifications/actions/delete", controller.Delete)               // 刪除通知
		r.GET("/system-notifications/unread-count", controller.GetUnreadCount)          // 獲取未讀通知數量
	}
}

func systemNotificationFacilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		controller := facility_api.NewSystemNotificationController()
		r.GET("/system-notifications", controller.GetList)                              // 獲取用戶通知列表
		r.POST("/system-notifications/actions/mark-read", controller.MarkAsRead)        // 標記通知為已讀
		r.POST("/system-notifications/actions/mark-all-read", controller.MarkAllAsRead) // 標記所有通知為已讀
		r.POST("/system-notifications/actions/delete", controller.Delete)               // 刪除通知
		r.GET("/system-notifications/unread-count", controller.GetUnreadCount)          // 獲取未讀通知數量
	}
}
