package services

import (
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xmodel"
	"gorm.io/gorm"
)

var CronSettingService = new(cronSettingService)

type cronSettingService struct{}

func (s *cronSettingService) CheckCronRunning(db *gorm.DB, code string) (bool, string, error) {
	var cron xmodel.CronSetting
	var err error
	if err = db.Where("code = ?", code).First(&cron).Error; xgorm.IsSqlErr(err) {
		return false, "", err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, "", nil
	} else {
		if cron.Run == "Y" {
			return true, cron.Param, nil
		} else {
			return false, "", nil
		}
	}
}
