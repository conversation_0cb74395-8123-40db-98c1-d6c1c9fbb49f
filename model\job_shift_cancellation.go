package model

import (
	"time"

	"github.com/Norray/xrocket/xmodel"
	"github.com/shopspring/decimal"
)

// 工作班次取消記錄
type JobShiftCancellation struct {
	Id                   uint64          `json:"id" gorm:"primary_key"`                                      // 主鍵
	FacilityId           uint64          `json:"facilityId" gorm:"index:facility_idx;not null"`              // 機構Id
	JobShiftId           uint64          `json:"jobShiftId" gorm:"index:job_shift_idx;not null"`             // 工作班次Id
	JobApplicationId     uint64          `json:"jobApplicationId" gorm:"index:job_application_idx;not null"` // 工作申請Id
	ProfessionalId       uint64          `json:"professionalId" gorm:"index:professional_idx;not null"`      // Professional Id
	RequiresCompensation string          `json:"requiresCompensation" gorm:"type:varchar(1);not null"`       // 是否需要賠付 (Y/N)
	CompensationHours    decimal.Decimal `json:"compensationHours" gorm:"type:decimal(10,2)"`                // 賠付時長（小時）
	CompensationAmount   decimal.Decimal `json:"compensationAmount" gorm:"type:decimal(10,2)"`               // 賠付金額
	CompensationReason   string          `json:"compensationReason" gorm:"type:varchar(500)"`                // 賠付原因
	CancelTime           *time.Time      `swaggertype:"string" json:"cancelTime" gorm:"type:datetime(0)"`    // 取消時間
	CancelUserId         uint64          `json:"cancelUserId" gorm:"index:cancel_user_idx"`                  // 取消操作用戶Id

	xmodel.Model
}

func (JobShiftCancellation) TableName() string {
	return "job_shift_cancellation"
}

func (JobShiftCancellation) SwaggerDescription() string {
	return "工作班次取消記錄"
}
