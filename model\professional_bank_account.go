package model

import "github.com/Norray/xrocket/xmodel"

// 專業人士銀行信息
type ProfessionalBankAccount struct {
	Id                uint64 `json:"id" gorm:"primary_key"`
	UserId            uint64 `json:"userId" gorm:"index:user_idx;not null"`               // 用戶Id
	BankStateBranch   string `json:"bankStateBranch" gorm:"type:varchar(255);not null"`   // 銀行分行(BSB)
	BankAccountNumber string `json:"bankAccountNumber" gorm:"type:varchar(255);not null"` // 銀行帳戶號碼
	BankAccountName   string `json:"bankAccountName" gorm:"type:varchar(255);not null"`   // 銀行帳戶名稱
	xmodel.Model
}

func (ProfessionalBankAccount) TableName() string {
	return "professional_bank_account"
}

func (ProfessionalBankAccount) SwaggerDescription() string {
	return "專業人士銀行信息"
}
