package routers

import (
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtest"
	"github.com/shopspring/decimal"
)

func TestAllowanceCreate(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/allowances/actions/create",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "新增",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.AllowanceCreateReq{
					FacilityId:             1,
					Name:                   "測試津貼",
					Description:            "測試津貼描述",
					Status:                 model.AllowanceStatusEnable,
					AttractsSuperannuation: "Y",
					PerHourAmount:          decimal.NewFromFloat(10.50),
					PerShiftAmount:         decimal.NewFromFloat(50.00),
					PerJobAmount:           decimal.NewFromFloat(100.00),
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestAllowanceList(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/allowances",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.AllowanceListReq{
					FacilityId: 1,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestAllowanceSearch(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/allowances/actions/search",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "搜索",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.AllowanceSearchReq{
					FacilityId: 1,
					Status:     model.AllowanceStatusEnable,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestAllowanceEdit(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/allowances/actions/edit",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "修改",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.AllowanceEditReq{
					AllowanceId:            1,
					Name:                   "修改後的津貼",
					Description:            "修改後的津貼描述",
					Status:                 model.AllowanceStatusEnable,
					AttractsSuperannuation: "N",
					PerHourAmount:          decimal.NewFromFloat(12.00),
					PerShiftAmount:         decimal.NewFromFloat(60.00),
					PerJobAmount:           decimal.NewFromFloat(120.00),
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestAllowanceInquire(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/allowances/actions/inquire",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.AllowanceInquireReq{
					AllowanceId: 1,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestAllowanceDelete(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/allowances/actions/delete",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "刪除",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.AllowanceDeleteReq{
					AllowanceId: 1,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFixInitAllowance(t *testing.T) {
	var err error
	facilityIds := []uint64{}
	db := xgorm.DB
	var facilityArr []model.Facility
	facilityBuilder := db.Model(&model.Facility{})
	if len(facilityIds) > 0 {
		facilityBuilder = facilityBuilder.Where("id in (?)", facilityIds)
	}
	if err = facilityBuilder.Find(&facilityArr).Error; err != nil {
		t.Fatal(err)
	}
	tx := db.Begin()
	// 循環先查詢是否已存在allowance，如果不存在則 init
	for _, facility := range facilityArr {
		var allowanceArr []model.Allowance
		if err = tx.Where("facility_id = ?", facility.Id).Find(&allowanceArr).Error; err != nil {
			tx.Rollback()
			t.Fatal(err)
		}
		if len(allowanceArr) == 0 {
			err = services.AllowanceService.Init(tx, facility.Id)
			if err != nil {
				tx.Rollback()
				t.Fatal(err)
			}
		}
	}
	if err = tx.Commit().Error; err != nil {
		t.Fatal(err)
	}
}
