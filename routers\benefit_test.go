package routers

import (
	"encoding/json"
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtest"
)

var testBenefitId uint64

func TestBenefitCreate(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/benefits/actions/create",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "新增",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.BenefitCreateReq{
					FacilityId: 1,
					Name:       "測試福利",
					Status:     model.BenefitStatusEnable,
				},
				CheckResultHandler: func(resp interface{}) bool {
					response := resp.(xresp.Response)
					var data services.BenefitCreateResp
					dataByte, _ := json.Marshal(response.Data)
					_ = json.Unmarshal(dataByte, &data)
					testBenefitId = data.BenefitId
					return true
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFixInitBenefit(t *testing.T) {
	var err error
	facilityIds := []uint64{1}

	var facilityArr []model.Facility
	if err = xgorm.DB.Where("id in (?)", facilityIds).Find(&facilityArr).Error; err != nil {
		t.Fatal(err)
	}
	// 循環先查詢是否已存在benefit，如果不存在則 init
	for _, facility := range facilityArr {
		var benefitArr []model.Benefit
		if err = xgorm.DB.Where("facility_id = ?", facility.Id).Find(&benefitArr).Error; err != nil {
			t.Fatal(err)
		}
		if len(benefitArr) == 0 {
			err = services.BenefitService.Init(xgorm.DB, facility.Id)
			if err != nil {
				t.Fatal(err)
			}
		}
	}
}

func TestBenefitList(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/benefits",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.BenefitListReq{
					FacilityId: 1,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestBenefitSearch(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/benefits/actions/search",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "搜索",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.BenefitSearchReq{
					FacilityId: 1,
					Status:     model.BenefitStatusEnable,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestBenefitEdit(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/benefits/actions/edit",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "修改",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.BenefitEditReq{
					BenefitId: testBenefitId,
					Name:      "修改後的福利",
					Status:    model.BenefitStatusEnable,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestBenefitInquire(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/benefits/actions/inquire",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.BenefitInquireReq{
					BenefitId: testBenefitId,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestBenefitDelete(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/benefits/actions/delete",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "刪除",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.BenefitDeleteReq{
					BenefitId: testBenefitId,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
