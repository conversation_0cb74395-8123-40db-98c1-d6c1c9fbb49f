package job

import (
	"context"
	"encoding/json"
	"time"

	"github.com/Norray/medic-crew/internal/task"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xamqp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
)

const (
	CronJobSchedulePublishTask = "cron_job_schedule_publish_task"
)

func jobSchedulePublishTask() {
	traceId := uuid.NewV4().String()
	ctx := context.Background()
	ctx = context.WithValue(ctx, "traceId", traceId)

	logger := log.WithField("traceId", traceId).WithField("task", CronJobSchedulePublishTask)

	db := xgorm.DB.WithContext(ctx)
	run, _, err := services.CronSettingService.CheckCronRunning(db, CronJobSchedulePublishTask)
	if err != nil {
		logger.Errorf("[CRON] fail to job schedule publish task: %v", err)
		return
	}
	if !run {
		logger.Warnf("[CRON] <%s> cron job not run ", CronJobSchedulePublishTask)
		return
	}

	currentDate := time.Now().UTC().Format(xtool.DateDayA)
	// 找到需要今天發佈的JobScheduleDate的JobScheduleId
	var jobScheduleIds []uint64
	if err = db.Table("job_schedule_date AS jsd").
		Joins("JOIN job_schedule AS js ON js.id = jsd.job_schedule_id AND js.status = ?", model.JobScheduleStatusEnable).
		Select("jsd.job_schedule_id").
		Where("jsd.status = ?", model.JobScheduleDateStatusPending).
		Where("jsd.date >= ?", currentDate).
		Where("DATE_SUB(jsd.date, INTERVAL js.advance_days DAY) <= ?", currentDate).
		Select("jsd.job_schedule_id").
		Group("jsd.job_schedule_id").
		Find(&jobScheduleIds).Error; err != nil {
		logger.Errorf("[CRON] fail to job schedule publish task: %v", err)
		return
	}

	// 提交到隊列
	for _, jobScheduleId := range jobScheduleIds {
		req := task.JobSchedulePublishReq{
			JobScheduleId: jobScheduleId,
		}
		str, _ := json.Marshal(req)
		err = xamqp.SendTask(task.JobSchedulePublishTask, xamqp.Task{
			MessageId: task.JobSchedulePublishTask,
			TaskId:    task.JobSchedulePublishTask + "_" + uuid.NewV4().String(),
			Data:      string(str),
		})
		if err != nil {
			logger.Errorf("can not send task: %s, JobScheduleId: %d", err, jobScheduleId)
		}
		logger.Infof("send task: %s, JobScheduleId: %d", task.JobSchedulePublishTask, jobScheduleId)
	}
}
