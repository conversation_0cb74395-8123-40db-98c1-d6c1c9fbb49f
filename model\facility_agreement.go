package model

import (
	"time"

	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xmodel/xtype"
)

const (
	FacilityAgreementStatusUnsent    = "UNSENT"    // 未發送
	FacilityAgreementStatusUnsigned  = "UNSIGNED"  // 未簽署
	FacilityAgreementStatusReviewing = "REVIEWING" // 審核中
	FacilityAgreementStatusSigned    = "SIGNED"    // 已簽署

	// 動態狀態 隨著時間變化
	FacilityAgreementStatusPending  = "PENDING"  // 待生效
	FacilityAgreementStatusActive   = "ACTIVE"   // 生效中
	FacilityAgreementStatusExpiring = "EXPIRING" // 即將到期
	FacilityAgreementStatusExpired  = "EXPIRED"  // 已過期

	FacilityAgreementRepresentativeSignedY = "Y" // 機構代表已簽署
	FacilityAgreementRepresentativeSignedN = "N" // 機構代表未簽署
)

// 機構協議
type FacilityAgreement struct {
	Id                       uint64         `json:"id" gorm:"primary_key"`
	FacilityId               uint64         `json:"facilityId" gorm:"index:facility_idx;not null"`                                // 機構ID
	BeginTime                xtype.Date     `swaggertype:"string" json:"beginTime" gorm:"type:date;not null"`                     // 生效時間
	EndTime                  xtype.Date     `swaggertype:"string" json:"endTime" gorm:"type:date;not null"`                       // 失效時間
	PayUpfrontCommissionId   uint64         `json:"payUpfrontCommissionId" gorm:"index:pay_upfront_commission_idx;not null"`      // 預付款佣金ID
	PayInArrearsCommissionId uint64         `json:"payInArrearsCommissionId" gorm:"index:pay_in_arrears_commission_idx;not null"` // 事後支付佣金ID
	FacilityFileId           uint64         `json:"facilityFileId" gorm:"index:facility_file_idx;not null"`                       // 機構簽名文件ID
	Content                  string         `json:"content" gorm:"type:mediumtext;not null"`                                      // 內容
	Template                 string         `json:"template" gorm:"type:varchar(255);not null"`                                   // 模板名稱
	Status                   string         `json:"status" gorm:"type:varchar(32);not null"`                                      // 狀態 UNSENT UNSIGNED SIGNED PENDING ACTIVE EXPIRING EXPIRED
	RepresentativeSigned     string         `json:"representativeSigned" gorm:"type:varchar(1);not null"`                         // 機構代表是否簽署協議 Y/N
	RepresentativeSignedDate xtype.NullDate `swaggertype:"string" json:"representativeSignedDate" gorm:"type:date"`               // 機構代表簽署日期
	UpdateTime               *time.Time     `json:"updateTime" gorm:"type:datetime(0)"`                                           // 更新時間
	xmodel.Model
}

func (FacilityAgreement) TableName() string {
	return "facility_agreement"
}

func (FacilityAgreement) SwaggerDescription() string {
	return "機構協議"
}
