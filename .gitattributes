# === 預設：所有文字檔強制使用 LF 行尾 ===
* text=auto eol=lf

# === 語言檔案明確指定為文字檔，避免識別錯誤 ===
*.go      text eol=lf
*.js      text eol=lf
*.ts      text eol=lf
*.jsx     text eol=lf
*.tsx     text eol=lf
*.html    text eol=lf
*.css     text eol=lf
*.scss    text eol=lf
*.json    text eol=lf
*.yaml    text eol=lf
*.yml     text eol=lf
*.md      text eol=lf
*.txt     text eol=lf
*.sh      text eol=lf
*.env     text eol=lf
*.xml     text eol=lf
*.ini     text eol=lf

# === 排除常見的二進位檔案，不進行行尾轉換 ===
*.jpg     binary
*.jpeg    binary
*.png     binary
*.gif     binary
*.ico     binary
*.pdf     binary
*.zip     binary
*.tar.gz  binary
*.7z      binary
*.exe     binary
*.dll     binary
*.mp3     binary
*.mp4     binary
*.mov     binary
*.ttf     binary
*.woff    binary
*.woff2   binary

# === 排除某些 IDE 產生的檔案 ===
*.iml     text eol=lf
*.log     text eol=lf

# === 編譯產物不處理（可選） ===
*.wasm    binary
*.class   binary
*.o       binary
*.a       binary
