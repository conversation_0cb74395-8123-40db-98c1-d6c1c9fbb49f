package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func systemFacilityAgreementRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/system").Use(handlers...)
	{
		controller := system_api.NewFacilityAgreementController()
		r.GET("/facility-agreements", controller.List)
		r.GET("/facility-agreements/actions/inquire", controller.Inquire)
		r.GET("/facility-agreements/actions/search", controller.Search)
		r.POST("/facility-agreements/actions/create", controller.Create)
		r.POST("/facility-agreements/actions/edit", controller.Edit)
		r.POST("/facility-agreements/actions/send-agreement", controller.SendAgreement)
		r.POST("/facility-agreements/actions/download", controller.Download)
	}
}

func facilityFacilityAgreementRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		controller := facility_api.NewFacilityAgreementController()
		r.GET("/facility-agreements", controller.List)
		r.GET("/facility-agreements/actions/inquire", controller.Inquire)
		r.POST("/facility-agreements/actions/download", controller.Download)
		r.GET("/facility-agreements/actions/search", controller.Search)
		r.POST("/facility-agreements/actions/sign", controller.Sign)
	}
}
