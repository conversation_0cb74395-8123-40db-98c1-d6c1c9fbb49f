package routers

import (
	"fmt"
	"os"
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

func TestUserPermissionInquire(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/user-permissions/actions/inquire",
		UserId:           1, // 系統管理員用戶ID
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢用戶API權限列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.UserPermissionInquireReq{
					UserId: 1,
				},
			},
		},
	}
	err := os.Chdir("..")
	if err != nil {
		fmt.Println(err)
		return
	}
	xtest.RunTests(t, test)
}
