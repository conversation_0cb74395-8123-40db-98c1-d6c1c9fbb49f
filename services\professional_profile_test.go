package services

import (
	"fmt"
	"os"
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
)

func TestFixProfessionalProfileReference(t *testing.T) {
	err := os.Chdir("..")
	if err != nil {
		t.Fatalf("Failed to change directory: %v", err)
		return
	}
	xconfig.Setup("./config/app.ini")
	xgorm.DefaultSetup()
	var allProfessional []model.Professional
	xgorm.DB.Find(&allProfessional)
	for _, professional := range allProfessional {
		profile, err := professional.UnmarshalProfile(professional.ProfileJson)
		if err != nil {
			t.Fatalf("Failed to unmarshal profile: %v", err)
		}
		// 刪除2個以上的數據
		if len(profile.References) >= 2 {
			profile.References = profile.References[:2]
		} else if len(profile.References) == 1 {
			profile.References = append(profile.References, model.ProfessionalReference{})
		} else {
			profile.References = append(profile.References, model.ProfessionalReference{})
			profile.References = append(profile.References, model.ProfessionalReference{})
		}
		err = professional.MarshalProfile(profile)
		if err != nil {
			return
		}
		professional.ReferenceFormStatus = model.ProfessionalReferenceFormStatusFilled
		for _, reference := range profile.References {
			if reference.FormStatus == model.ProfessionalProfileReferencesFormStatusPending || reference.FormStatus == "" {
				professional.ReferenceFormStatus = model.ProfessionalReferenceFormStatusUnfilled
			}
		}
		xgorm.DB.Save(&professional)
	}
}
func TestFixProfessionalInvalidFile(t *testing.T) {
	err := os.Chdir("..")
	if err != nil {
		t.Fatalf("Failed to change directory: %v", err)
		return
	}
	xconfig.Setup("./config/app.ini")
	xgorm.DefaultSetup()
	var allProfessional []model.Professional
	xgorm.DB.Find(&allProfessional)
	// 失效的文件編號
	invalidFileCodeMap := map[string]bool{
		"WORKING_WITH_CHILDREN_CHECK":         true,
		"WORKING_WITH_VULNERABLE_PEOPLE_CARD": true,
	}
	successMap := map[uint64][]string{}
	failMap := map[uint64][]string{}

	defer func() {
		fmt.Println("success count:", len(successMap))
		for professionalId, invalidFileList := range successMap {
			fmt.Printf("success professionalId: %d, invalidFileList: %v\n", professionalId, invalidFileList)
		}
		fmt.Println("--------------------------------")
		fmt.Println("fail count:", len(failMap))
		for professionalId, invalidFileList := range failMap {
			fmt.Printf("fail professionalId: %d, invalidFileList: %v\n", professionalId, invalidFileList)
		}
	}()

	tx := xgorm.DB.Begin()
	for _, professional := range allProfessional {
		invalidFileList := make([]string, 0)
		profile, err := professional.UnmarshalProfile(professional.ProfileJson)
		if err != nil {
			failMap[professional.Id] = invalidFileList
			tx.Rollback()
			t.Fatalf("Failed to unmarshal profile: %v", err)
			return
		}
		hasInvalidFile := false
		// 倒序刪除
		for i := len(profile.Files) - 1; i >= 0; i-- {
			if invalidFileCodeMap[profile.Files[i].FileCode] {
				invalidFileList = append(invalidFileList, profile.Files[i].FileCode)
				profile.Files = append(profile.Files[:i], profile.Files[i+1:]...)
				hasInvalidFile = true
			}
		}
		if hasInvalidFile {
			err = professional.MarshalProfile(profile)
			if err != nil {
				failMap[professional.Id] = invalidFileList
				tx.Rollback()
				t.Fatalf("Failed to marshal profile: %v", err)
				return
			}
			tx.Save(&professional)
			successMap[professional.Id] = invalidFileList
		}
	}
	tx.Commit()
}

func TestFixProfessionalPreferredSpecialities(t *testing.T) {
	err := os.Chdir("..")
	if err != nil {
		t.Fatalf("Failed to change directory: %v", err)
		return
	}
	xconfig.Setup("./config/app.ini")
	xgorm.DefaultSetup()
	var allProfessional []model.Professional
	xgorm.DB.Find(&allProfessional)
	successMap := map[uint64][]string{}
	failMap := map[uint64][]string{}

	defer func() {
		fmt.Println("success count:", len(successMap))
		for professionalId, _ := range successMap {
			fmt.Printf("success professionalId: %d", professionalId)
		}
		fmt.Println("--------------------------------")
		fmt.Println("fail count:", len(failMap))
		for professionalId, _ := range failMap {
			fmt.Printf("fail professionalId: %d", professionalId)
		}
	}()

	tx := xgorm.DB.Begin()
	for _, professional := range allProfessional {
		invalidFileList := make([]string, 0)
		profile, err := professional.UnmarshalProfile(professional.ProfileJson)
		if err != nil {
			failMap[professional.Id] = invalidFileList
			tx.Rollback()
			t.Fatalf("Failed to unmarshal profile: %v", err)
			return
		}
		hasInvalid := false
		if profile.PreferredSpecialities == nil || len(profile.PreferredSpecialities) == 0 {
			hasInvalid = true
			profile.PreferredSpecialities = make([]model.ProfessionalPreferredSpeciality, 0)
		}

		if hasInvalid {
			err = professional.MarshalProfile(profile)
			if err != nil {
				failMap[professional.Id] = invalidFileList
				tx.Rollback()
				t.Fatalf("Failed to marshal profile: %v", err)
				return
			}
			tx.Save(&professional)
			successMap[professional.Id] = invalidFileList
		}
	}
	tx.Commit()
}
