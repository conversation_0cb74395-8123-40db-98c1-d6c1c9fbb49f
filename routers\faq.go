package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/Norray/medic-crew/api/v1/professional_api"
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func faqSystemRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/system").Use(handlers...)
	{
		controller := system_api.NewFaqController()
		r.GET("/faqs", controller.List)
		r.GET("/faqs/actions/search", controller.Search)
		r.GET("/faqs/actions/inquire", controller.Inquire)
		r.POST("/faqs/actions/create", controller.Create)
		r.POST("/faqs/actions/edit", controller.Edit)
		r.POST("/faqs/actions/delete", controller.Delete)
	}
}

func faqProfessionalRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/professional").Use(handlers...)
	{
		controller := professional_api.NewProfessionalFaqController()
		r.GET("/faqs/actions/search", controller.Search)
		r.GET("/faqs/actions/inquire", controller.Inquire)
	}
}

func faqFacilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		controller := facility_api.NewFacilityFaqController()
		r.GET("/faqs/actions/search", controller.Search)
		r.GET("/faqs/actions/inquire", controller.Inquire)
	}
}
