package system_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type LocationController struct {
	v1.CommonController
}

func NewLocationController() LocationController {
	return LocationController{}
}

// @Tags Location
// @Summary 新增行政區域
// @Description
// @Router /v1/system/locations/actions/create [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.LocationCreateReq true "parameter"
// @Success 200 {object} services.LocationCreateResp "Success"
func (con LocationController) Create(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.LocationCreateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				if req.ParentId > 0 {
					return services.LocationService.CheckIdExistInCountry(db, req.Country, &model.Location{}, req.ParentId)
				}
				return true, i18n.Message{}, nil
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		resp, err := services.LocationService.Create(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Location
// @Summary 獲取行政區域列表
// @Description
// @Router /v1/system/locations [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.LocationListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Success 200 {object} []services.LocationListResp "Success"
func (con LocationController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.LocationListReq
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		resp, err := services.LocationService.List(db, req, &pageSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Location
// @Summary 修改行政區域
// @Description
// @Router /v1/system/locations/actions/edit [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.LocationEditReq true "parameter"
// @Success 200 "Success"
func (con LocationController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.LocationEditReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var location model.Location
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.LocationService.CheckIdExistInCountry(db, req.Country, &location, req.LocationId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.LocationService.CheckIdCanBeParent(db, req.Country, req.LocationId, req.ParentId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.LocationService.Edit(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Location
// @Summary 查詢行政區域
// @Description
// @Router /v1/system/locations/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.LocationInquireReq true "parameter"
// @Success 200 {object} services.LocationInquireResp "Success"
func (con LocationController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.LocationInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var location model.Location
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.LocationService.CheckIdExist(db, &location, req.LocationId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		resp, err := services.LocationService.Inquire(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Location
// @Summary 刪除行政區域
// @Description
// @Router /v1/system/locations/actions/delete [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.LocationDeleteReq true "parameter"
// @Success 200 "Success"
func (con LocationController) Delete(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.LocationDeleteReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var location model.Location
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.LocationService.CheckIdExist(db, &location, req.LocationId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.LocationService.CheckCanDelete(db, req.LocationId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.LocationService.Delete(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}
