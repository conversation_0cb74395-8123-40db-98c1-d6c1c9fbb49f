package routers

import (
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

// 機構儀表板匯總統計測試
func TestFacilityDashboardSummary(t *testing.T) {
	// 構建測試用例
	user := getTestUser(16)

	test := xtest.Test{
		Url:              programPath + "/v1/facility/dashboard/summary",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "機構儀表板匯總統計",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常獲取儀表板匯總統計",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityDashboardSummaryReq{
					FacilityId: user.FacilityId,
					BeginDate:  "2025-09-01",
					EndDate:    "2025-09-30",
					TimeZone:   "Australia/Sydney",
				},
			},
			{
				SubName:           "缺少必需參數",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.FacilityDashboardSummaryReq{
					FacilityId: user.FacilityId,
					BeginDate:  "2024-01-01",
					TimeZone:   "Australia/Sydney",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 機構支出統計測試
func TestFacilityDashboardExpense(t *testing.T) {
	// 構建測試用例
	user := getTestUser(16)

	test := xtest.Test{
		Url:              programPath + "/v1/facility/dashboard/expense",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "機構支出統計",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常獲取機構支出統計",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityExpenseReq{
					FacilityId: user.FacilityId,
					BeginDate:  "2025-09-01",
					EndDate:    "2025-09-30",
					TimeZone:   "Australia/Sydney",
				},
			},
			{
				SubName:           "缺少必需參數",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.FacilityExpenseReq{
					FacilityId: user.FacilityId,
					BeginDate:  "2025-09-01",
					EndDate:    "2025-09-30",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 機構儀表板匯總統計測試
func TestFacilityDashboardJobStatistic(t *testing.T) {
	// 構建測試用例
	user := getTestUser(16)

	test := xtest.Test{
		Url:              programPath + "/v1/facility/dashboard/job-statistic",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "機構職業發佈工作量統計",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常獲取儀表板匯總統計",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityJobStatisticReq{
					FacilityId: user.FacilityId,
					BeginDate:  "2025-09-01",
					EndDate:    "2025-09-30",
					TimeZone:   "Australia/Sydney",
				},
			},
			{
				SubName:           "缺少必需參數",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.FacilityJobStatisticReq{
					FacilityId: user.FacilityId,
					BeginDate:  "2024-01-01",
					TimeZone:   "Australia/Sydney",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 機構職業工資統計測試
func TestFacilityDashboardJobSalaryStatistic(t *testing.T) {
	// 構建測試用例
	user := getTestUser(16)

	test := xtest.Test{
		Url:              programPath + "/v1/facility/dashboard/job-salary-statistic",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "機構職業工資統計",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常獲取機構職業工資統計",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityJobSalaryStatisticReq{
					FacilityId: user.FacilityId,
					BeginDate:  "2025-09-01",
					EndDate:    "2025-09-30",
					TimeZone:   "Australia/Sydney",
				},
			},
			{
				SubName:           "缺少機構ID",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.FacilityJobSalaryStatisticReq{
					BeginDate: "2025-09-01",
					TimeZone:  "Australia/Sydney",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
