package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/Norray/medic-crew/api/v1/professional_api"
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func facilitySystemRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/system").Use(handlers...)
	{
		r.GET("/facilities/actions/search", system_api.NewFacilityController().Search)
	}
}

func facilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		r.POST("/facilities/actions/edit", facility_api.NewFacilityController().Edit)
		r.GET("/facilities/actions/inquire", facility_api.NewFacilityController().Inquire)
	}
}

func facilityProfessionalRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/professional").Use(handlers...)
	{
		r.GET("/facilities/actions/search", professional_api.NewProfessionalFacilityController().Search)
	}
}
