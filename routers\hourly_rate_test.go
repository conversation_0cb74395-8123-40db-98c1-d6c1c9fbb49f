package routers

import (
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtest"
	"github.com/shopspring/decimal"
)

func TestHourlyRateList(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/hourly-rates",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.HourlyRateListReq{
					FacilityId: 1,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestHourlyRateUpdate(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/hourly-rates/actions/update",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "批量更新",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.HourlyRateUpdateReq{
					FacilityId: 1,
					Items: []services.HourlyRateItem{
						{
							HourlyRateId: 1,
							Rate:         decimal.NewFromFloat(1.0),
						},
						{
							HourlyRateId: 2,
							Rate:         decimal.NewFromFloat(1.5),
						},
					},
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFixInitHourlyRate(t *testing.T) {
	var err error
	var facilityArr []model.Facility
	if err := xgorm.DB.Find(&facilityArr).Error; err != nil {
		t.Fatal(err)
	}
	// 循環先查詢是否已存在hourly rate，如果不存在則 init
	for _, facility := range facilityArr {
		var hourlyRateArr []model.HourlyRate
		if err = xgorm.DB.Where("facility_id = ?", facility.Id).Find(&hourlyRateArr).Error; err != nil {
			t.Fatal(err)
		}
		if len(hourlyRateArr) == 0 {
			err = services.HourlyRateService.Init(xgorm.DB, facility.Id)
			if err != nil {
				t.Fatal(err)
			}
		}
	}
}
