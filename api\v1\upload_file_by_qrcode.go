package v1

import (
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"mime/multipart"
)

type UploadFileByQrcodeController struct {
	CommonController
}

func NewUploadFileByQrcodeController() UploadFileByQrcodeController {
	return UploadFileByQrcodeController{}
}

// @Tags Upload File By Qrcode
// @Summary 生成上傳文件二維碼
// @Router /v1/app/files/actions/gen-upload-qrcode [POST]
// @Produce  json
// @Param json body services.GenUploadFileQrcodeReq true "parameter"
// @Success 200 {object} services.GenUploadFileQrcodeResp "Success"
func (con UploadFileByQrcodeController) GenUploadQrcode(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.GenUploadFileQrcodeReq
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.ReqUserId = nc.GetJWTUserId()
		resp, err := services.UploadFileByQrcodeService.GenQrcodeByFileCode(db, req)
		if xgorm.IsNotFoundErr(err) {
			nc.BadRequestResponse(err)
			return
		}
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Upload File By Qrcode
// @Summary 通過二維碼獲取上傳的文件id
// @Router /v1/app/files/actions/inquire-by-qrcode [GET]
// @Produce  json
// @Param json query services.InquireFileIdByQrcodeReq true "parameter"
// @Success 200 {object} services.ProfessionalFileUploadFileResp "Success"
// @Success 200 {object} services.FacilityFileUploadResp "Success"
// @Success 200 "Success"
func (con UploadFileByQrcodeController) InquireByQrcode(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.InquireFileIdByQrcodeReq
	if err := c.ShouldBind(&req); err == nil {
		req.ReqUserId = nc.GetJWTUserId()
		resp, err := services.UploadFileByQrcodeService.InquireByQrcode(c, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Upload File By Qrcode
// @Summary 掃描二維碼上傳文件
// @Router /v1/public/files/actions/exchange-by-qrcode [POST]
// @Produce  json
// @Param json body services.ExchangeByQrcodeReq true "parameter"
// @Param file formData file true "文件"
// @Success 200 {object} services.ExchangeByQrcodeResp "Success"
func (con UploadFileByQrcodeController) ExchangeQrcode(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ExchangeByQrcodeReq
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var formFile *multipart.FileHeader
		formFile, err = c.FormFile("file")
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		req.File = formFile

		tx := db.Begin()
		resp, err := services.UploadFileByQrcodeService.ExchangeByQrcode(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()

		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
