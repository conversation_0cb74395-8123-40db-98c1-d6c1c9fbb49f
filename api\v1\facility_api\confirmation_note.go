package facility_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type ConfirmationNoteController struct {
	v1.CommonController
}

func NewConfirmationNoteController() ConfirmationNoteController {
	return ConfirmationNoteController{}
}

// @Tags Confirmation Note
// @Summary 獲取機構確認通知單列表
// @Description
// @Router /v1/facility/confirmation-notes [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FacilityConfirmationNoteListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Param sortingKey query string false "createTime 申請时间, amount 金額"
// @Param sortingType query string false "a=順序,d=倒序"
// @Success 200 {object} []services.FacilityConfirmationNoteListResp "Success"
func (con ConfirmationNoteController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityConfirmationNoteListReq
	var pageSet xresp.PageSet
	var sortSet xresp.SortingSet
	_ = c.ShouldBindQuery(&pageSet)
	_ = c.ShouldBindQuery(&sortSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		facilityId, err := con.GetUserFacilityId(nc, db)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		resp, err := services.ConfirmationNoteService.FacilityList(db, req, facilityId, &pageSet, sortSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Confirmation Note
// @Summary 查詢機構確認通知單詳情
// @Description
// @Router /v1/facility/confirmation-notes/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.ConfirmationNoteInquireReq true "parameter"
// @Success 200 {object} services.ConfirmationNoteInquireResp "Success"
func (con ConfirmationNoteController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ConfirmationNoteInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var document model.Document
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.DocumentService.CheckIdExistByUserId(db, &document, req.DocumentId, nc.GetJWTUserId(), model.UserUserTypeFacilityUser)
			})
		msg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(msg) > 0 {
			nc.BadRequestResponseWithCheckMsg(msg)
			return
		}

		resp, err := services.ConfirmationNoteService.FacilityInquire(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Confirmation Note
// @Summary 審批確認通知單
// @Description
// @Router /v1/facility/confirmation-notes/actions/review [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FacilityConfirmationNoteReviewReq true "parameter"
// @Success 200 "Success"
func (con ConfirmationNoteController) Review(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityConfirmationNoteReviewReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var document model.Document
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.DocumentService.CheckIdExistByUserId(db, &document, req.DocumentId, nc.GetJWTUserId(), model.UserUserTypeFacilityUser)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.ConfirmationNoteService.CheckCanReview(db, req.DocumentId)
			})
		msg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(msg) > 0 {
			nc.BadRequestResponseWithCheckMsg(msg)
			return
		}

		tx := db.Begin()
		err = services.ConfirmationNoteService.FacilityReview(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)

	} else {
		nc.BadRequestResponse(err)
	}
}
