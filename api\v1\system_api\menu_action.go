package system_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xmodel"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type MenuActionController struct {
	v1.CommonController
}

func NewMenuActionController() MenuActionController {
	return MenuActionController{}
}

// @Tags Menu Action
// @Summary 新建菜單
// @Description
// @Router /v1/system/menus/actions/create [POST]
// @Produce  json
// @Security ApiKeyAuth
// @Param json body services.MenuCreateReq true "parameter"
// @Success 200 {object} services.MenuCreateResp "Success"
func (con MenuActionController) CreateMenu(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.MenuCreateReq
	if err := c.ShouldBindJ<PERSON>(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		// 檢查合法性
		var checkMsg []string
		checkMsg, err = xapp.NewCK(c).
			Run(func() (bool, i18n.Message, error) {
				return services.MenuActionService.CheckMenuCodeUnique(db, req.Code)
			}).
			Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		tx := db.Begin()
		resp, err := services.MenuActionService.CreateMenu(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Menu Action
// @Summary 修改菜單
// @Description
// @Router /v1/system/menus/actions/edit [POST]
// @Produce  json
// @Security ApiKeyAuth
// @Param json body services.MenuEditReq true "parameter"
// @Success 200 "Success"
func (con MenuActionController) EditMenu(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.MenuEditReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		// 檢查合法性
		var checkMsg []string
		menu := xmodel.Menu{}
		checkMsg, err = xapp.NewCK(c).
			Run(func() (bool, i18n.Message, error) {
				return services.MenuActionService.CheckMenuIdExist(db, &menu, req.MenuId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.MenuActionService.CheckMenuCodeUnique(db, req.Code, req.MenuId)
			}).
			Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		if err = services.MenuActionService.EditMenu(tx, req); err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()

		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Menu Action
// @Summary 查詢菜單詳情
// @Description
// @Router /v1/system/menus/actions/inquire [GET]
// @Produce  json
// @Security ApiKeyAuth
// @Param menuId query uint64 true "菜單id"
// @Success 200 {object} services.MenuListResp "Success"
func (con MenuActionController) InquireMenu(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.MenuInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.MenuActionService.InquireMenu(db, req)
		if err != nil {
			if xgorm.IsNotFoundErr(err) {
				nc.BadRequestResponse(err)
				return
			}
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Menu Action
// @Summary 刪除菜單
// @Description
// @Router /v1/system/menus/actions/delete [POST]
// @Produce  json
// @Security ApiKeyAuth
// @Param json body services.MenuDeleteReq true "parameter"
// @Success 200 "Success"
func (con MenuActionController) DeleteMenu(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.MenuDeleteReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		// 檢查合法性
		var checkMsg []string
		menu := xmodel.Menu{}
		checkMsg, err = xapp.NewCK(c).
			Run(func() (bool, i18n.Message, error) {
				return services.MenuActionService.CheckMenuIdExist(db, &menu, req.MenuId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.MenuActionService.CheckMenuIdCanBeDelete(db, req.MenuId)
			}).
			Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		if err = services.MenuActionService.DeleteMenu(tx, req); err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Menu Action
// @Summary 修改菜單排序
// @Description
// @Router /v1/system/menus/actions/update-sort [POST]
// @Produce  json
// @Security ApiKeyAuth
// @Param json body services.MenuSortUpdateReq true "parameter"
// @Success 200 "Success"
func (con MenuActionController) UpdateMenuSort(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.MenuSortUpdateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		tx := db.Begin()
		if err = services.MenuActionService.UpdateMenuSort(tx, req); err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Menu Action
// @Summary 新建菜單功能
// @Description
// @Router /v1/system/actions/actions/create [POST]
// @Produce  json
// @Security ApiKeyAuth
// @Param json body services.ActionCreateReq true "parameter"
// @Success 200 {object} services.ActionCreateResp "Success"
func (con MenuActionController) CreateAction(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ActionCreateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		// 檢查合法性
		var checkMsg []string
		checkMsg, err = xapp.NewCK(c).
			Run(func() (bool, i18n.Message, error) {
				return services.MenuActionService.CheckMenuIdExist(db, &xmodel.Menu{}, req.MenuId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.MenuActionService.CheckActionCodeUnique(db, req.Code)
			}).
			Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		result, err := services.MenuActionService.CreateAction(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(result)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Menu Action
// @Summary 修改菜單功能
// @Description
// @Router /v1/system/actions/actions/edit [POST]
// @Produce  json
// @Security ApiKeyAuth
// @Param json body services.ActionEditReq true "parameter"
// @Success 200 "Success"
func (con MenuActionController) EditAction(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ActionEditReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		// 檢查合法性
		var checkMsg []string
		action := xmodel.Action{}
		checkMsg, err = xapp.NewCK(c).
			Run(func() (bool, i18n.Message, error) {
				return services.MenuActionService.CheckActionIdExist(db, &action, req.ActionId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.MenuActionService.CheckActionCodeUnique(db, req.Code, req.ActionId)
			}).
			Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		tx := db.Begin()
		if err = services.MenuActionService.EditAction(tx, req); err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()

		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Menu Action
// @Summary 查詢菜單功能詳情
// @Description
// @Router /v1/system/actions/actions/inquire [GET]
// @Produce  json
// @Security ApiKeyAuth
// @Param actionId query uint64 true "菜單功能id"
// @Success 200 {object} services.ActionInquireResp "Success"
func (con MenuActionController) InquireAction(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ActionInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.MenuActionService.InquireAction(db, req)
		if err != nil {
			if xgorm.IsNotFoundErr(err) {
				nc.BadRequestResponse(err)
				return
			}
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Menu Action
// @Summary 刪除菜單功能
// @Description
// @Router /v1/system/actions/actions/delete [POST]
// @Produce  json
// @Security ApiKeyAuth
// @Param json body services.ActionDeleteReq true "parameter"
// @Success 200 "Success"
func (con MenuActionController) DeleteAction(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ActionDeleteReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		// 檢查合法性
		var checkMsg []string
		action := xmodel.Action{}
		checkMsg, err = xapp.NewCK(c).
			Run(func() (bool, i18n.Message, error) {
				return services.MenuActionService.CheckActionIdExist(db, &action, req.ActionId)
			}).
			Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		tx := db.Begin()
		if err = services.MenuActionService.DeleteAction(tx, req); err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Menu Action
// @Summary 查詢可設定菜單功能詳情
// @Description
// @Router /v1/system/menu-actions/actions/tree [GET]
// @Produce  json
// @Security ApiKeyAuth
// @Param system query string true "系統 SYSTEM FACILITY PROGRAM"
// @Success 200 {object} []services.MenuListResp "Success"
func (con MenuActionController) MenuActionTree(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.MenuListReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		result, err := services.MenuActionService.List(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(result)
	} else {
		nc.BadRequestResponse(err)
	}
}
