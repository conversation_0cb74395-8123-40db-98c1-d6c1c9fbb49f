package services

import (
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xmodel/xtype"
	"gorm.io/gorm"
)

// ProfessionalJobAvailabilityService 專業人士工作可用性服務
type professionalJobAvailabilityService struct{}

var ProfessionalJobAvailabilityService = new(professionalJobAvailabilityService)

// ProfessionalJobAvailabilityUpdateReq 更新專業人士工作可用性請求
type ProfessionalJobAvailabilityUpdateReq struct {
	UserId   uint64                                        `json:"-"`                                // userId
	Settings []ProfessionalJobAvailabilityUpdateSettingReq `json:"settings" binding:"required,dive"` // 設定
}

type ProfessionalJobAvailabilityUpdateSettingReq struct {
	FilterType string `json:"filterType" binding:"required,oneof=AVAILABLE_DATE UNAVAILABLE_DATE AVAILABLE_WEEKDAY"`                                           // 篩選類型
	BeginDate  string `json:"beginDate" binding:"required_if=FilterType AVAILABLE_DATE,required_if=FilterType UNAVAILABLE_DATE,omitempty,datetime=2006-01-02"` // 可用開始日期
	EndDate    string `json:"endDate" binding:"required_if=FilterType AVAILABLE_DATE,required_if=FilterType UNAVAILABLE_DATE,omitempty,datetime=2006-01-02"`   // 可用結束日期
	Value      string `json:"value" binding:"required_if=FilterType AVAILABLE_WEEKDAY,omitempty,splitin=1 2 3 4 5 6 7"`                                        // 可用星期篩選
}

// Update 更新專業人士工作可用性
func (s *professionalJobAvailabilityService) Update(db *gorm.DB, req ProfessionalJobAvailabilityUpdateReq) error {
	var err error

	var allIds []uint64
	for _, settingReq := range req.Settings {
		var setting model.ProfessionalJobAvailability
		if settingReq.FilterType == model.ProfessionalJobAvailabilityFilterTypeAvailableWeekday {
			if err = db.Where("user_id = ?", req.UserId).
				Where("filter_type = ?", settingReq.FilterType).
				Where("value = ?", settingReq.Value).
				First(&setting).Error; xgorm.IsSqlErr(err) {
				return err
			}
		} else {
			if err = db.Where("user_id = ?", req.UserId).
				Where("filter_type = ?", settingReq.FilterType).
				Where("begin_date = ?", settingReq.BeginDate).
				Where("end_date = ?", settingReq.EndDate).
				First(&setting).Error; xgorm.IsSqlErr(err) {
				return err
			}
		}

		if xgorm.IsNotFoundErr(err) {
			setting = model.ProfessionalJobAvailability{
				UserId:     req.UserId,
				FilterType: settingReq.FilterType,
				BeginDate:  xtype.NewNullDate(settingReq.BeginDate),
				EndDate:    xtype.NewNullDate(settingReq.EndDate),
				Value:      settingReq.Value,
			}
			if err = db.Create(&setting).Error; err != nil {
				return err
			}
		}
		allIds = append(allIds, setting.Id)
	}

	if len(allIds) == 0 {
		if err = db.Where("user_id = ?", req.UserId).Delete(&model.ProfessionalJobAvailability{}).Error; err != nil {
			return err
		}
	} else {
		if err = db.Where("user_id = ?", req.UserId).Where("id NOT IN (?)", allIds).Delete(&model.ProfessionalJobAvailability{}).Error; err != nil {
			return err
		}
	}

	return nil
}

// ProfessionalJobAvailabilityInquireReq 查詢專業人士工作可用性請求

// ProfessionalJobAvailabilityInquireResp 查詢專業人士工作可用性響應
type ProfessionalJobAvailabilityInquireResp struct {
	UserId   uint64                                         `json:"userId"`   // userId
	Settings []ProfessionalJobAvailabilityUpdateSettingResp `json:"settings"` // 設定
}

type ProfessionalJobAvailabilityUpdateSettingResp struct {
	FilterType string     `json:"filterType"`                     // 篩選類型
	BeginDate  xtype.Date `swaggertype:"string" json:"beginDate"` // 可用開始日期
	EndDate    xtype.Date `swaggertype:"string" json:"endDate"`   // 可用結束日期
	Value      string     `json:"value"`                          // 可用星期篩選
}

// Inquire 查詢專業人士工作可用性
func (s *professionalJobAvailabilityService) Inquire(db *gorm.DB, userId uint64) (ProfessionalJobAvailabilityInquireResp, error) {
	var resp ProfessionalJobAvailabilityInquireResp
	// 查詢記錄
	var settings []ProfessionalJobAvailabilityUpdateSettingResp
	if err := db.Table("professional_job_availability").Select([]string{
		"filter_type",
		"begin_date",
		"end_date",
		"value",
	}).Where("user_id = ?", userId).Order("filter_type").Order("begin_date").Find(&settings).Error; err != nil {
		return resp, err
	}
	resp.UserId = userId
	resp.Settings = settings
	return resp, nil
}
