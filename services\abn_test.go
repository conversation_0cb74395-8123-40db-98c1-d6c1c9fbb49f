package services

import (
	"fmt"
	"strings"
	"testing"
)

func TestAbnService_GetAbnInfo(t *testing.T) {
	abn := "***********"
	resp, err := AbnService.GetAbnInfo(abn)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("GetAbnInfo failed: %v", err)
		return
	}
	fmt.Printf("resp: %+v\n", resp)
}

func TestAbnService_GetAbnInfo_InvalidABN(t *testing.T) {
	// 測試無效的ABN號碼
	abn := "***********" // 無效的ABN號碼
	resp, err := AbnService.GetAbnInfo(abn)
	if err == nil {
		t.<PERSON><PERSON>rf("Expected error for invalid ABN, but got none")
		return
	}

	// 檢查錯誤信息是否包含異常描述
	expectedError := "ABN query failed"
	if !strings.Contains(err.<PERSON>rror(), expectedError) {
		t.<PERSON><PERSON>("Expected error to contain '%s', but got: %v", expectedError, err)
	}

	fmt.Printf("Invalid ABN error (expected): %v\n", err)
	fmt.Printf("resp: %+v\n", resp)
}
