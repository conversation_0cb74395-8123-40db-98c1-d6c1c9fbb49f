@echo off
echo ---------------------------------------------------
echo Git Hooks Configuration
echo ---------------------------------------------------

set /p CHOICE="Do you want to enable git hooks? (Y/N): "

IF /I "%CHOICE%"=="Y" (
    echo Enabling git hooks...
    git config core.hooksPath .githooks
    git config --global core.autocrlf true
    IF %ERRORLEVEL% EQU 0 (
        echo Git hooks successfully enabled and path set to '.githooks'.
        echo core.autocrlf has been set to true.
    ) ELSE (
        echo Failed to enable git hooks.
    )
) ELSE IF /I "%CHOICE%"=="N" (
    echo Disabling git hooks...
    git config --unset core.hooksPath
    IF %ERRORLEVEL% EQU 0 (
        echo Git hooks successfully disabled.
    ) ELSE (
        echo Failed to disable git hooks.
    )
) ELSE (
    echo Invalid choice. Please enter Y or N.
    exit /b 1
)
echo ---------------------------------------------------