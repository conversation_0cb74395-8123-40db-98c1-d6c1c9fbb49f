package routers

import (
	"encoding/json"
	"testing"

	"github.com/Norray/medic-crew/model"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

var testFaqId uint64

func TestFaqSystemCreate(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/faqs/actions/create",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "新增",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FaqCreateReq{
					Category:   model.FaqCategoryProfessional,
					Question:   "測試問題",
					Answer:     "測試答案[image!66f27227-5af1-4723-ae02-5e0569efa0a4]",
					AnswerText: "測試答案",
					Status:     model.FaqStatusEnable,
				},
				CheckResultHandler: func(resp interface{}) bool {
					response := resp.(xresp.Response)
					var data services.FaqCreateResp
					dataByte, _ := json.Marshal(response.Data)
					_ = json.Unmarshal(dataByte, &data)
					testFaqId = data.FaqId
					return true
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFaqSystemList(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/faqs",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.FaqListReq{},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFaqSystemSearch(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/faqs/actions/search",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "搜索",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.FaqSearchReq{},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFaqSystemEdit(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/faqs/actions/edit",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "修改",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FaqEditReq{
					FaqId:      5,
					Category:   model.FaqCategoryProfessional,
					Question:   "測試答案",
					Answer:     "測試答案[image!66f27227-5af1-4723-ae02-5e0569efa0a4]",
					AnswerText: "測試答案111",
					Status:     model.FaqStatusEnable,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFaqSystemInquire(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/faqs/actions/inquire",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FaqInquireReq{
					FaqId: 3,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFaqSystemDelete(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/faqs/actions/delete",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "刪除",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FaqDeleteReq{
					FaqId: testFaqId,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFaqProfessionalSearchRouter(t *testing.T) {
	// 專業人士 FAQ 搜索測試
	test := xtest.Test{
		Url:              programPath + "/v1/professional/faqs/actions/search",
		UserId:           8,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "專業人士FAQ搜索",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常搜索",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.FaqSearchReqByUser{},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFaqProfessionalInquireRouter(t *testing.T) {
	// 專業人士 FAQ 查詢測試
	test := xtest.Test{
		Url:              programPath + "/v1/professional/faqs/actions/inquire",
		UserId:           8,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "專業人士FAQ查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FaqInquireReq{
					FaqId: 1,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFaqFacilityRouter(t *testing.T) {
	// 機構 FAQ 搜索測試
	test := xtest.Test{
		Url:              programPath + "/v1/facility/faqs/actions/search",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "機構FAQ搜索",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常搜索",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.FaqSearchReqByUser{},
			},
		},
	}
	xtest.RunTests(t, test)

	// 機構 FAQ 查詢測試
	test = xtest.Test{
		Url:              programPath + "/v1/facility/faqs/actions/inquire",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "機構FAQ查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FaqInquireReq{
					FaqId: testFaqId,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
