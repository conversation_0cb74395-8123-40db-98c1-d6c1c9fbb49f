package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/gin-gonic/gin"
)

func allowanceFacilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		r.POST("/allowances/actions/create", facility_api.NewAllowanceController().Create)
		r.GET("/allowances", facility_api.NewAllowanceController().List)
		r.GET("/allowances/actions/search", facility_api.NewAllowanceController().Search)
		r.POST("/allowances/actions/edit", facility_api.NewAllowanceController().Edit)
		r.GET("/allowances/actions/inquire", facility_api.NewAllowanceController().Inquire)
		r.POST("/allowances/actions/delete", facility_api.NewAllowanceController().Delete)
	}
}
