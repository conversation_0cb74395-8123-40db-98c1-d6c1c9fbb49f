package job

import (
	"context"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
)

const CronCleanProfessionalJobAvailability = "cron_clean_professional_job_availability"

// 清理專業職務可提供時間
func cleanProfessionalJobAvailability() {
	traceId := uuid.NewV4().String()
	ctx := context.Background()
	ctx = context.WithValue(ctx, "traceId", traceId)

	db := xgorm.DB.WithContext(ctx)
	run, _, err := services.CronSettingService.CheckCronRunning(db, CronCleanProfessionalJobAvailability)
	if err != nil {
		log.WithField("traceId", traceId).Errorf("[CRON] fail to clean professional job availability: %v", err)
		return
	}
	if !run {
		log.WithField("traceId", traceId).Warnf("[CRON] <%s> cron job not run ", CronCleanProfessionalJobAvailability)
		return
	}

	currentDate := xtool.NowFormat(xtool.DateDayA)
	if err = db.Where("filter_type = ? OR filter_type = ?", model.ProfessionalJobAvailabilityFilterTypeAvailableDate, model.ProfessionalJobAvailabilityFilterTypeUnavailableDate).
		Where("end_date < ?", currentDate).Delete(&model.ProfessionalJobAvailability{}).Error; err != nil {
		log.WithField("traceId", traceId).Errorf("[CRON] fail to clean professional job availability: %v", err)
		return
	}
}
