package routers

import (
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
	"testing"
)

// region ------------------------------------------ 專業人員儀表板 ------------------------------------------

func TestProfessionalDashboardIncome(t *testing.T) {
	// 構建測試用例
	user := getTestUser(15) // 使用測試用戶
	test := xtest.Test{
		Url:              programPath + "/v1/professional/dashboard/income",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "專業人員收入統計",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ProfessionalIncomeRequest{
					UserId:    user.UserId,
					BeginDate: "2025-09-01",
					EndDate:   "2025-09-30",
				},
			},
			{
				SubName:           "缺少開始日期",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.ProfessionalIncomeRequest{
					UserId:  user.UserId,
					EndDate: "2025-09-30",
				},
			},
			{
				SubName:           "缺少結束日期",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.ProfessionalIncomeRequest{
					UserId:    user.UserId,
					BeginDate: "2025-09-01",
				},
			},
			{
				SubName:           "日期格式錯誤",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.ProfessionalIncomeRequest{
					UserId:    user.UserId,
					BeginDate: "2025/09/01",
					EndDate:   "2025/09/30",
				},
			},
		},
	}
	xtest.RunTests(t, test)

}

func TestProfessionalDashboardWorkingHours(t *testing.T) {
	// 構建測試用例
	user := getTestUser(15) // 使用測試用戶
	test := xtest.Test{
		Url:              programPath + "/v1/professional/dashboard/working-hours",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "專業人員工作時間統計",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.WorkingHoursRequest{
					UserId:    user.UserId,
					BeginDate: "2025-09-01",
					EndDate:   "2025-09-30",
					TimeZone:  "Asia/Taipei",
				},
			},
			{
				SubName:           "缺少開始日期",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.WorkingHoursRequest{
					UserId:   user.UserId,
					EndDate:  "2025-09-30",
					TimeZone: "Asia/Taipei",
				},
			},
			{
				SubName:           "缺少結束日期",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.WorkingHoursRequest{
					UserId:    user.UserId,
					BeginDate: "2025-09-01",
					TimeZone:  "Asia/Taipei",
				},
			},
			{
				SubName:           "缺少時區",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.WorkingHoursRequest{
					UserId:    user.UserId,
					BeginDate: "2025-09-01",
					EndDate:   "2025-09-30",
				},
			},
			{
				SubName:           "日期格式錯誤",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.WorkingHoursRequest{
					UserId:    user.UserId,
					BeginDate: "2025/09/01",
					EndDate:   "2025/09/30",
					TimeZone:  "Asia/Taipei",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalDashboardSummary(t *testing.T) {
	// 構建測試用例
	user := getTestUser(15) // 使用測試用戶
	test := xtest.Test{
		Url:              programPath + "/v1/professional/dashboard/summary",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "專業人員儀表板匯總統計",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.DashboardSummaryRequest{
					UserId:    user.UserId,
					BeginDate: "2025-09-01",
					EndDate:   "2025-09-30",
					TimeZone:  "Asia/Taipei",
				},
			},
			{
				SubName:           "缺少開始日期",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.DashboardSummaryRequest{
					UserId:   user.UserId,
					EndDate:  "2025-09-30",
					TimeZone: "Asia/Taipei",
				},
			},
			{
				SubName:           "缺少結束日期",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.DashboardSummaryRequest{
					UserId:    user.UserId,
					BeginDate: "2025-09-01",
					TimeZone:  "Asia/Taipei",
				},
			},
			{
				SubName:           "缺少時區",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.DashboardSummaryRequest{
					UserId:    user.UserId,
					BeginDate: "2025-09-01",
					EndDate:   "2025-09-30",
				},
			},
			{
				SubName:           "日期格式錯誤",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.DashboardSummaryRequest{
					UserId:    user.UserId,
					BeginDate: "2025/09/01",
					EndDate:   "2025/09/30",
					TimeZone:  "Asia/Taipei",
				},
			},
			{
				SubName:           "無效時區",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.DashboardSummaryRequest{
					UserId:    user.UserId,
					BeginDate: "2025-09-01",
					EndDate:   "2025-09-30",
					TimeZone:  "Invalid/Timezone",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// endregion
