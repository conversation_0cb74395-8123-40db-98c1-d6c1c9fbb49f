package job

import (
	"context"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

const (
	CronCheckFacilityInvitationTimeout                    = "cron_check_facility_invitation_timeout" // 檢查機構的超時邀請
	CheckFacilityInvitationTimeoutMaxProcessRecordsPerRun = 100                                      // 每次處理的最大記錄數
	CheckFacilityInvitationTimeoutLockTimeoutSeconds      = 50                                       // 鎖定超時時間（秒）
)

type FacilityInvitationTimeout struct {
	WsMessageId      uint64
	JobApplicationId uint64
}

func jobCheckFacilityInvitationTimeout() {
	traceId := uuid.NewV4().String()
	ctx := context.Background()
	ctx = context.WithValue(ctx, "traceId", traceId)

	logger := log.WithField("traceId", traceId).With<PERSON><PERSON>("task", CronCheckFacilityInvitationTimeout)

	db := xgorm.DB.WithContext(ctx)
	run, _, err := services.CronSettingService.CheckCronRunning(db, CronCheckFacilityInvitationTimeout)
	if err != nil {
		logger.Errorf("[CRON] fail to job schedule publish task: %v", err)
		return
	}
	if !run {
		logger.Warnf("[CRON] <%s> cron job not run ", CronCheckFacilityInvitationTimeout)
		return
	}

	nowTime := time.Now().UTC().Truncate(time.Second)

	var facilityInvitationTimeouts []FacilityInvitationTimeout
	builder := db.Table("ws_message AS wm").
		Select([]string{
			"wm.id AS ws_message_id",
			"wm.job_application_id",
		}).
		Joins("JOIN job_application AS ja ON ja.id = wm.job_application_id AND ja.status != 'ACCEPT'").
		Where("wm.message_type = ?", model.WsMessageTypeJobInvitation).
		Where("wm.processed = ?", model.WsMessageProcessedN).
		Where("wm.process_result = ?", "").
		Where("wm.create_time < ?", nowTime.Add(-12*time.Hour)).
		Limit(CheckFacilityInvitationTimeoutMaxProcessRecordsPerRun) // 限制每次處理的記錄數
	if err = builder.Group("wm.id, wm.job_application_id").Scan(&facilityInvitationTimeouts).Error; err != nil {
		logger.Errorf("[CRON] fail to get facility invitation timeout: %v", err)
		return
	}
	if len(facilityInvitationTimeouts) == 0 {
		logger.Info("[CRON] no facility invitation timeout")
		return
	}
	wmIds := make([]uint64, 0, len(facilityInvitationTimeouts))
	jobApplicationIds := make([]uint64, 0, len(facilityInvitationTimeouts))
	for _, facilityInvitationTimeout := range facilityInvitationTimeouts {
		wmIds = append(wmIds, facilityInvitationTimeout.WsMessageId)
		jobApplicationIds = append(jobApplicationIds, facilityInvitationTimeout.JobApplicationId)
	}
	wmIds = xtool.Uint64ArrayDeduplication(wmIds)
	jobApplicationIds = xtool.Uint64ArrayDeduplication(jobApplicationIds)

	logger = logger.WithField("wm_ids", wmIds).WithField("job_application_ids", jobApplicationIds)

	// 設置事務超時
	ctx, cancel := context.WithTimeout(ctx, CheckFacilityInvitationTimeoutLockTimeoutSeconds*time.Second)
	defer cancel()

	err = db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		updateMap := map[string]interface{}{
			"processed":      model.WsMessageProcessedY,
			"process_result": model.WsMessageProcessResultInvitationTimeout,
		}
		if err = tx.Model(&model.WsMessage{}).Where("id IN (?)", wmIds).Updates(updateMap).Error; err != nil {
			logger.Errorf("[CRON] fail to update ws message: %v", err)
			return err
		}
		if err = tx.Model(&model.JobApplication{}).
			Where("id IN (?)", jobApplicationIds).
			Where("status = ?", model.JobApplicationStatusInvite).
			Update("status", model.JobApplicationStatusChatting).Error; err != nil {
			logger.Errorf("[CRON] fail to update job application: %v", err)
			return err
		}
		return nil
	})
	if err != nil {
		logger.Errorf("[CRON] fail to update facility invitation timeout: %v", err)
		return
	}
	logger.Debugf("[CRON] successfully processed %d facility invitation timeouts", len(facilityInvitationTimeouts))
}
