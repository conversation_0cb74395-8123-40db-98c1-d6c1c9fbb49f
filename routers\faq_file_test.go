package routers

import (
	"os"
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

func TestFaqFileUpload(t *testing.T) {
	// 讀取圖片
	file, err := os.Open("1.png")
	if err != nil {
		t.Error(err)
		return
	}
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/faq-files/actions/upload",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Form,
		Name:             "上傳常見問題文件",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常上傳",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FaqFileUploadReq{
					FileCode: model.FaqFileCodeImage,
				},
				File:          file,
				FileFieldName: "file",
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFaqFilePreview(t *testing.T) {
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/faq-files/actions/preview",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "預覽常見問題文件",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FaqFilePreviewUrlReq{
					FaqFileUuid: "",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
