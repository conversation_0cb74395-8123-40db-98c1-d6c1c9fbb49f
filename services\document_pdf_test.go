package services

import (
	"io"
	"os"
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
)

func TestDocumentService_DocumentPDF(t *testing.T) {
	err := os.Chdir("..")
	if err != nil {
		t.Fatalf("Failed to change directory: %v", err)
		return
	}
	xconfig.Setup("./config/app.ini")
	xgorm.DefaultSetup()
	db := xgorm.DB
	_ = xgorm.DB.AutoMigrate(&model.Document{})
	reader, err := DocumentService.DocumentPDF(db, DocumentPDFReq{
		DocumentId: 131,
	})
	if err != nil {
		t.Errorf("DocumentPDF failed: %v", err)
		return
	}
	// 將 *bytes.Reader 轉換為 []byte
	data, err := io.ReadAll(reader)
	if err != nil {
		t.Errorf("Failed to read PDF data: %v", err)
		return
	}
	// 保存到本地
	err = os.WriteFile("test.pdf", data, 0644)
	if err != nil {
		t.Errorf("Failed to write PDF file: %v", err)
		return
	}
}
