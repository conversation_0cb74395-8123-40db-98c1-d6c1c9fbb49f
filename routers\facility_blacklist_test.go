package routers

import (
	"encoding/json"
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

var testFacilityBlacklistId uint64

func TestFacilityBlacklistList(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/blacklists",
		UserId:           11,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityBlacklistListReq{
					FacilityId: 3,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityBlacklistCreate(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/blacklists/actions/create",
		UserId:           11,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "新增",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityBlacklistCreateReq{
					FacilityId:     3,
					ProfessionalId: 39,
					Reason:         "測試",
				},
				CheckResultHandler: func(resp interface{}) bool {
					response := resp.(xresp.Response)
					var data services.FacilityBlacklistCreateResp
					dataByte, _ := json.Marshal(response.Data)
					_ = json.Unmarshal(dataByte, &data)
					testFacilityBlacklistId = data.FacilityBlacklistId
					return true
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityBlacklistEdit(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/blacklists/actions/edit",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "修改",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityBlacklistEditReq{
					FacilityBlacklistId: testFacilityBlacklistId,
					FacilityId:          3,
					Reason:              "測試修改",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityBlacklistDelete(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/blacklists/actions/delete",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "刪除",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityBlacklistDeleteReq{
					FacilityId:          3,
					FacilityBlacklistId: 1,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
