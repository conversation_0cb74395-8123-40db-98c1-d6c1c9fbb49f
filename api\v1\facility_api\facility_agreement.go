package facility_api

import (
	"fmt"
	"net/http"

	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type FacilityAgreementController struct {
	v1.CommonController
}

func NewFacilityAgreementController() FacilityAgreementController {
	return FacilityAgreementController{}
}

// @Tags Facility Agreement
// @Summary 獲取機構協議列表
// @Description
// @Router /v1/facility/facility-agreements [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FacilityAgreementListReqByFacility true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Success 200 {object} []services.FacilityAgreementListResp "Success"
func (con FacilityAgreementController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityAgreementListReqByFacility
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.FacilityAgreementService.ListByFacility(db, req, &pageSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Agreement
// @Summary 查询機構協議
// @Description
// @Router /v1/facility/facility-agreements/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.AgreementInquireByFacilityProfileReq true "parameter"
// @Success 200 {object} services.AgreementInquireByFacilityProfileResp "Success"
func (con FacilityAgreementController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.AgreementInquireByFacilityProfileReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var facilityAgreement model.FacilityAgreement
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityAgreementService.CheckIdExist(db, &facilityAgreement, req.FacilityAgreementId, true)
			})
		if facilityAgreement.Id > 0 && !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: facilityAgreement.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}
		checkMsg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		resp, err := services.AgreementService.GetFacilityAgreement(db, req)
		if xgorm.IsNotFoundErr(err) {
			nc.BadRequestResponse(err)
			return
		}
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)

	} else {
		nc.ErrorResponse(req, err)
	}
}

// @Tags Agreement
// @Summary 下載機構協議
// @Description
// @Router /v1/facility/facility-agreements/actions/download [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FacilityAgreementDownloadReq true "parameter"
// @Success 200 "Success"
func (con FacilityAgreementController) Download(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityAgreementDownloadReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var facilityAgreement model.FacilityAgreement
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityAgreementService.CheckIdExist(db, &facilityAgreement, req.FacilityAgreementId, true)
			})
		if facilityAgreement.Id > 0 && !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: facilityAgreement.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}
		checkMsg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		filename, fileBytes, err := services.FacilityAgreementService.Download(db, services.FacilityAgreementDownloadReq{
			FacilityAgreementId: facilityAgreement.Id,
		})
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		c.Writer.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename*=utf-8''%s", xtool.ReplacePlus(filename)))
		c.Data(http.StatusOK, services.FacilityFileService.GetFileMimeType(filename), fileBytes)
	} else {
		nc.ErrorResponse(req, err)
	}
}

// @Tags Agreement
// @Summary 機構協議的列表
// @Description
// @Router /v1/facility/facility-agreements/actions/search [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FacilityAgreementSearchByFacilityProfileReq true "parameter"
// @Success 200 {object} []services.FacilityAgreementSearchByFacilityProfileResp "Success"
func (con FacilityAgreementController) Search(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityAgreementSearchByFacilityProfileReq
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.FacilityAgreementService.SearchByFacilityProfile(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.ErrorResponse(req, err)
	}
}

// @Tags Facility Agreement
// @Summary 簽署機構協議
// @Description 簽署機構協議
// @Router /v1/facility/facility-agreements/actions/sign [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FacilityAgreementSignReq true "parameter"
// @Success 200 "Success"
func (con FacilityAgreementController) Sign(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityAgreementSignReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		checker := xapp.NewCK(c)

		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityAgreementService.CheckIdExist(db, &model.FacilityAgreement{}, req.FacilityAgreementId)
			})
		checkMsg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityAgreementService.CheckCanSignAgreement(db, req.FacilityAgreementId)
			})
		alertMsg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(alertMsg) > 0 {
			nc.AlertResponse(alertMsg)
			return
		}
		err = services.FacilityAgreementService.Sign(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}
