package routers

import (
	"github.com/Norray/medic-crew/model"
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

// region ---------------------------------------------------- 專業人士 ----------------------------------------------------
func TestProfessionalInvoiceList(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/professional/invoices",
		UserId:           42,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "專業人士發票列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.ProfessionalInvoiceListReq{},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalInvoiceInquire(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/professional/invoices/actions/inquire",
		UserId:           9,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "專業人士發票詳情",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.InvoiceInquireReq{
					DocumentId: 1,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalInvoicePaymentReceived(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/professional/invoices/actions/payment-received",
		UserId:           9,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "專業人士發票標記已收款",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.InvoicePaymentReceivedReq{
					DocumentId: 7,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// endregion ---------------------------------------------------- 專業人士 ----------------------------------------------------

// region ---------------------------------------------------- 機構 ----------------------------------------------------
func TestFacilityInvoiceList(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/facility/invoices",
		UserId:           16,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "機構發票列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.FacilityInvoiceListReq{},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityInvoiceInquire(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/facility/invoices/actions/inquire",
		UserId:           16,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "機構發票詳情",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.InvoiceInquireReq{
					DocumentId: 1,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// endregion ---------------------------------------------------- 機構 ----------------------------------------------------

// region ---------------------------------------------------- 管理端 ----------------------------------------------------

func TestSystemInvoiceList(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/invoices",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "管理端發票列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "AR列表-應收未收",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.SystemInvoiceListReq{
					DocumentNo: "",
					StartDate:  "",
					EndDate:    "",
					ToName:     "",
					FromName:   "",
					Progress:   "",
					Particular: "",
					Paid:       model.DocumentPaidN,
					DataType:   model.DocumentDataTypeSystemToFacility,
				},
			},
			{
				SubName:           "AR列表-應收已收",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.SystemInvoiceListReq{
					DocumentNo: "",
					StartDate:  "",
					EndDate:    "",
					ToName:     "",
					FromName:   "",
					Progress:   "",
					Particular: "",
					Paid:       model.DocumentPaidN,
					DataType:   model.DocumentDataTypeSystemToFacility,
				},
			},
			{
				SubName:           "AP列表-應付未付",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.SystemInvoiceListReq{
					DocumentNo: "",
					StartDate:  "",
					EndDate:    "",
					ToName:     "",
					FromName:   "",
					Progress:   "",
					Particular: "",
					Paid:       "",
					DataType:   model.DocumentDataTypeProfessionalToSystem,
				},
			},
			{
				SubName:           "AP列表-應付已付",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.SystemInvoiceListReq{
					DocumentNo: "",
					StartDate:  "",
					EndDate:    "",
					ToName:     "",
					FromName:   "",
					Progress:   "",
					Particular: "",
					Paid:       "",
					DataType:   model.DocumentDataTypeProfessionalToSystem,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSystemInvoiceInquire(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/invoices/actions/inquire",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "管理端發票詳情",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.InvoiceInquireReq{
					DocumentId: 1,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// endregion ---------------------------------------------------- 管理端 ----------------------------------------------------
