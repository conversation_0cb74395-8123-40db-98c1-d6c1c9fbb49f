package model

import (
	"time"

	"github.com/Norray/xrocket/xmodel"
)

const (
	WsMessageTypeSystem  = "SYSTEM"  // 系統消息
	WsMessageTypeAck     = "ACK"     // 確認消息
	WsMessageTypeError   = "ERROR"   // 錯誤消息/發送失敗
	WsMessageTypeOnline  = "ONLINE"  // 在線提醒
	WsMessageTypeOffline = "OFFLINE" // 離線提醒

	WsMessageTypeText                 = "TEXT"                   // 文本消息
	WsMessageTypeJobInvitation        = "JOB_INVITATION"         // 工作邀請(機構)
	WsMessageTypeJobInvitationRevoked = "JOB_INVITATION_REVOKED" // 取消工作邀請(機構)
	WsMessageTypeJobAccept            = "JOB_ACCEPT"             // 接收邀請(專業人士)
	WsMessageTypeJobDecline           = "JOB_DECLINE"            // 拒絕邀請(專業人士)
	WsMessageTypeRead                 = "READ"                   // 消息已讀
	WsMessageTypeUnreadCount          = "UNREAD_COUNT"           // 未讀消息數

	WsMessageProcessResultInvitationTimeout   = "INVITATION_TIMEOUT"   // 邀請超時
	WsMessageProcessResultFacilityRevoke      = "FACILITY_REVOKE"      // 機構撤回工作邀請
	WsMessageProcessResultProfessionalDecline = "PROFESSIONAL_DECLINE" // 專業人士拒絕工作邀請
	WsMessageProcessResultProfessionalAccept  = "PROFESSIONAL_ACCEPT"  // 專業人士接受工作邀請

	WsMessageStatusUnread  = "UNREAD"  // 未讀
	WsMessageStatusRead    = "READ"    // 已讀
	WsMessageStatusDeleted = "DELETED" // 已刪除

	WsMessageProcessedY = "Y" // 消息已處理
	WsMessageProcessedN = "N" // 消息未處理

	WsMessageSenderTypeProfessional = "PROFESSIONAL" // 專業人士發送
	WsMessageSenderTypeFacility     = "FACILITY"     // 機構發送
	WsMessageSenderTypeSystem       = "SYSTEM"       // 系統發送

	WsMessageReceiverTypeProfessional = "PROFESSIONAL" // 專業人士接收
	WsMessageReceiverTypeFacility     = "FACILITY"     // 機構接收
	WsMessageReceiverTypeSystem       = "SYSTEM"       // 系統接收
)

// WebSocket消息
type WsMessage struct {
	Id                 uint64     `json:"id" gorm:"primary_key"`
	MessageUuid        string     `json:"messageUuid" gorm:"type:varchar(64);unique;not null"`                       // 消息UUID
	SessionId          uint64     `json:"sessionId" gorm:"index:session_id_idx;not null"`                            // 聊天會話ID
	SenderId           uint64     `json:"senderId" gorm:"index:sender_idx;not null"`                                 // 發送者ID(Professional User Id / Facility Id)
	SenderType         string     `json:"senderType" gorm:"type:varchar(20);not null"`                               // 發送者類型(PROFESSIONAL / FACILITY / SYSTEM)
	ReceiverId         uint64     `json:"receiverId" gorm:"index:receiver_idx;not null"`                             // 接收者ID(Professional User Id / Facility Id)
	RelatedMessageId   uint64     `json:"relatedMessageId" gorm:"index:related_message_id_idx"`                      // 相關消息ID(如果是回覆消息，則指向原消息ID)
	RelatedMessageUuid string     `json:"relatedMessageUuid" gorm:"type:varchar(64);index:related_message_uuid_idx"` // 相關消息UUID(如果是回覆消息，則指向原消息UUID)
	JobId              uint64     `json:"jobId" gorm:"index:job_id_idx"`                                             // 相關工作ID
	JobApplicationId   uint64     `json:"jobApplicationId" gorm:"index:job_application_id_idx"`                      // 相關工作申請ID
	ReceiverType       string     `json:"receiverType" gorm:"type:varchar(20);not null"`                             // 接收者類型(PROFESSIONAL / FACILITY / SYSTEM)
	MessageType        string     `json:"messageType" gorm:"type:varchar(64);not null"`                              // 消息類型
	Content            string     `json:"content" gorm:"type:text"`                                                  // 消息內容
	Status             string     `json:"status" gorm:"type:varchar(20);not null"`                                   // 消息狀態
	Processed          string     `json:"processed" gorm:"type:varchar(1);not null"`                                 // 是否已處理 Y N
	ProcessResult      string     `json:"processResult" gorm:"type:varchar(255)"`                                    // 用戶處理結果
	ReadTime           *time.Time `json:"readTime" gorm:"type:datetime(0)"`                                          // 已讀時間
	CreateTime         time.Time  `json:"createTime" gorm:"type:datetime(0);index:create_time_idx;not null"`         // 創建時間
	xmodel.Model
}

func (m *WsMessage) TableName() string {
	return "ws_message"
}

func (m *WsMessage) SwaggerDescription() string {
	return "WebSocket消息"
}
