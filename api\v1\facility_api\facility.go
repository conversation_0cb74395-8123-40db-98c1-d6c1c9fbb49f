package facility_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type FacilityController struct {
	v1.CommonController
}

func NewFacilityController() FacilityController {
	return FacilityController{}
}

// @Tags Facility
// @Summary 修改機構
// @Description
// @Router /v1/facility/facilities/actions/edit [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FacilityEditReq true "parameter"
// @Success 200 "Success"
func (con FacilityController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityEditReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityService.CheckIdExist(db, &model.Facility{}, req.FacilityId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		tx := db.Begin()
		err = services.FacilityService.Edit(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility
// @Summary 查询機構
// @Description
// @Router /v1/facility//facilities/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FacilityInquireReq true "parameter"
// @Success 200 {object} services.FacilityInquireResp "Success"
func (con FacilityController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.FacilityService.Inquire(db, req)
		if xgorm.IsNotFoundErr(err) {
			nc.BadRequestResponse(err)
			return
		}
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
