package routers

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/gin-gonic/gin"
)

func registerRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1").Use(handlers...)
	{
		controller := v1.NewRegisterController()
		r.POST("/register/facility", controller.RegisterFacility)
		r.POST("/register/facility-verify-email", controller.RegisterFacilityVerifyEmail)
		r.POST("/register/professional", controller.RegisterProfessional)
		r.POST("/register/professional-verify-email", controller.RegisterProfessionalVerifyEmail)
		r.POST("/register/resend-verification-email", controller.ResendVerificationEmail)
	}
}
