package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/gin-gonic/gin"
)

func serviceLocationFacilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		r.POST("/service-locations/actions/create", facility_api.NewServiceLocationController().Create)
		r.GET("/service-locations", facility_api.NewServiceLocationController().List)
		r.GET("/service-locations/actions/search", facility_api.NewServiceLocationController().Search)
		r.POST("/service-locations/actions/edit", facility_api.NewServiceLocationController().Edit)
		r.GET("/service-locations/actions/inquire", facility_api.NewServiceLocationController().Inquire)
		r.POST("/service-locations/actions/delete", facility_api.NewServiceLocationController().Delete)
	}
}
