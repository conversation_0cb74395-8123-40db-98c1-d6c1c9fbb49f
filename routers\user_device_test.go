package routers

import (
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xtest"
	"github.com/Norray/xrocket/xtool"
)

var frontendPassword123 = xtool.FrontendEncodeStringWithSalt("123", "medic-crew")

func TestUserDeviceApply(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:        programPath + "/v1/user-devices/actions/apply",
		Method:     xtest.Post,
		ParamsType: xtest.Body,
		Name:       "用戶設備申請",
		Cases: []xtest.TestCase{
			{
				SubName:           "專業人士登錄",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.UserDevicesApplyReq{
					Username: "<EMAIL>",
					Password: frontendPassword123,
					Device:   "WEB",
					System:   "USER",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestUserDeviceVerify(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:        programPath + "/v1/user-devices/actions/verify",
		Method:     xtest.Post,
		ParamsType: xtest.Body,
		Name:       "用戶設備驗證",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.UserDevicesVerifyReq{
					Username: "<EMAIL>",
					Key:      "876a55d3-22eb-4d6b-ac3c-330bd163cfe6",
					Code:     "992410",
					Device:   "WEB",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestUserDeviceRevoke(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/user-devices/actions/revoke",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "用戶設備登出",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSystemUserDevicesList(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/user-devices",
		UserId:           1, // 系統管理員用戶ID
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "系統管理員查看用戶設備列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.UserDevicesReq{
					PageIndex: 1,
					PageSize:  10,
				},
			},
			{
				SubName:           "按用戶名搜索",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.UserDevicesReq{
					UserName:  "test",
					PageIndex: 1,
					PageSize:  10,
				},
			},
			{
				SubName:           "按設備狀態搜索",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.UserDevicesReq{
					Status:    "ENABLE",
					PageIndex: 1,
					PageSize:  10,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestChangeDeviceStatus(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/user-devices/actions/change-status",
		UserId:           1, // 系統管理員用戶ID
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "用戶設備更改狀態",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ChangeDeviceStatusReq{
					DeviceId: 4,
					Status:   xmodel.UserDeviceStatusEnable,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
