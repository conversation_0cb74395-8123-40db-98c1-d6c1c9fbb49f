package routers

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func locationAppRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/app").Use(handlers...)
	{
		r.GET("/locations/actions/search", v1.NewLocationController().Search)
		r.GET("/locations/actions/search-country", v1.NewLocationController().SearchCountry)
	}
}

func locationSystemRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/system").Use(handlers...)
	{
		r.POST("/locations/actions/create", system_api.NewLocationController().Create)
		r.GET("/locations", system_api.NewLocationController().List)
		r.POST("/locations/actions/edit", system_api.NewLocationController().Edit)
		r.GET("/locations/actions/inquire", system_api.NewLocationController().Inquire)
		r.POST("/locations/actions/delete", system_api.NewLocationController().Delete)
	}
}
