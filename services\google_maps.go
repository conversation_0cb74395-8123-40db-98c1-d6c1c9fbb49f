package services

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"github.com/Norray/xrocket/xconfig"
	"github.com/shopspring/decimal"
)

var GoogleMapsService = new(googleMapsService)

type googleMapsService struct{}

type TimezoneRequest struct {
	Latitude  decimal.Decimal `form:"latitude" binding:"required" json:"latitude"`   // 緯度
	Longitude decimal.Decimal `form:"longitude" binding:"required" json:"longitude"` // 經度
}

type TimezoneResponse struct {
	Timezone string `json:"timezone"` // 時區
}

// GoogleTimezoneResponse Google時區API響應
type GoogleTimezoneResponse struct {
	DstOffset    int    `json:"dstOffset"`
	RawOffset    int    `json:"rawOffset"`
	Status       string `json:"status"`
	TimeZoneId   string `json:"timeZoneId"`
	TimeZoneName string `json:"timeZoneName"`
}

// GetTimezone 根據經緯度獲取時區
func (s *googleMapsService) GetTimezone(ctx context.Context, req TimezoneRequest) (TimezoneResponse, error) {
	// 獲取當前時間戳
	timestamp := time.Now().Unix()

	// 構建Google Timezone API請求URL
	url := fmt.Sprintf(
		"https://maps.googleapis.com/maps/api/timezone/json?location=%s,%s&timestamp=%d&key=%s",
		req.Latitude.String(),
		req.Longitude.String(),
		timestamp,
		xconfig.GoogleServiceConf.ApiKey,
	)

	// 發送HTTP請求
	httpReq, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return TimezoneResponse{}, err
	}

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return TimezoneResponse{}, err
	}
	defer resp.Body.Close()

	// 讀取響應內容
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return TimezoneResponse{}, err
	}

	// 解析JSON響應
	var googleResp GoogleTimezoneResponse
	if err := json.Unmarshal(body, &googleResp); err != nil {
		return TimezoneResponse{}, err
	}

	// 檢查API響應狀態
	if googleResp.Status != "OK" {
		return TimezoneResponse{}, fmt.Errorf("google_maps.timezone.error, status: %s", googleResp.Status)
	}

	// 返回時區信息
	return TimezoneResponse{
		Timezone: googleResp.TimeZoneId,
	}, nil
}
