package system_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/gin-gonic/gin"
)

type UserPermissionController struct {
	v1.CommonController
}

func NewUserPermissionController() *UserPermissionController {
	return &UserPermissionController{}
}

// @Tags User Permission
// @Summary 獲取用户api權限
// @Description
// @Router /v1/system/user-permissions/actions/inquire [GET]
// @Produce  json
// @Security ApiKeyAuth
// @Param json query services.UserPermissionInquireReq true "parameter"
// @Param pageIndex query uint32 false "頁數"
// @Param pageSize query uint32 false "每頁條目數"
// @Success 200 {object} services.UserApiPermissionInquireResp "Success"
func (con UserPermissionController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.UserPermissionInquireReq
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		resp, err := services.UserPermissionService.Inquire(c, req, &pageSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}
