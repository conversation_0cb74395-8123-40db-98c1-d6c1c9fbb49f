package routers

import (
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

func TestServiceFacilitySearch(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/app/service-facilities/actions/search",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "搜索",
		Cases: []xtest.TestCase{
			{
				SubName:           "按名稱搜索",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ServiceFacilitySearchReq{
					Name: "hospital",
				},
			},
			{
				SubName:           "限制結果數量",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ServiceFacilitySearchReq{
					Limit: 5,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
