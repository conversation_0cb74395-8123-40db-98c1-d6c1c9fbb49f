package model

import (
	"time"

	"github.com/Norray/xrocket/xmodel"
)

const (
	ProfessionalSuperannuationTypeExistingSuperFund = "EXISTING_SUPER_FUND" // 现有养老金账户
	ProfessionalSuperannuationTypeDefaultSuperFund  = "DEFAULT_SUPER_FUND"  // 默认养老金账户
	ProfessionalSuperannuationTypePrivateSMSF       = "PRIVATE_SMSF"        // 自管理养老金账户

	ProfessionalSuperannuationDataTypeDraft     = "DRAFT"     // 草稿
	ProfessionalSuperannuationDataTypeSubmitted = "SUBMITTED" // 已提交
)

// 养老金信息
type ProfessionalSuperannuation struct {
	Id                           uint64     `json:"id" gorm:"primary_key"`                                                             // 主键ID
	UserId                       uint64     `json:"userId" gorm:"type:bigint(20);not null;index:user_idx"`                             // 用户ID
	DataType                     string     `json:"dataType" gorm:"type:varchar(32);not null;index:data_type_idx"`                     // 数据类型，可选值：DRAFT、SUBMITTED
	SuperannuationType           string     `json:"superannuationType" gorm:"type:varchar(32);not null;index:superannuation_type_idx"` // 養老金类型，可选值：EXISTING_SUPER_FUND、DEFAULT_SUPER_FUND、PRIVATE_SMSF
	FullName                     string     `json:"fullName" gorm:"type:varchar(255);not null"`                                        // 个人全名
	TaxFileNumber                string     `json:"taxFileNumber" gorm:"type:varchar(255);not null"`                                   // 税务文件号码
	SuperFundName                string     `json:"superFundName" gorm:"type:varchar(255);not null"`                                   // 养老金基金名称
	SuperFundAbn                 string     `json:"superFundAbn" gorm:"type:varchar(255);not null"`                                    // 养老金基金ABN号码(Australian Business Number)
	SuperFundUsi                 string     `json:"superFundUsi" gorm:"type:varchar(255);not null"`                                    // 养老金基金USI号码(Unique Superannuation Identifier)
	SuperFundMemberAccountNumber string     `json:"superFundMemberAccountNumber" gorm:"type:varchar(255);not null"`                    // 会员账户号码
	SuperFundAccountName         string     `json:"superFundAccountName" gorm:"type:varchar(255);not null"`                            // 账户名称
	SmsfName                     string     `json:"smsfName" gorm:"type:varchar(255);not null"`                                        // 自管理养老金名称(Self-Managed Super Fund)
	SmsfAbn                      string     `json:"smsfAbn" gorm:"type:varchar(255);not null"`                                         // 自管理养老金ABN号码
	SmsfElectronicServiceAddress string     `json:"smsfElectronicServiceAddress" gorm:"type:varchar(255);not null"`                    // 自管理养老金电子服务地址
	SmsfAccountName              string     `json:"smsfAccountName" gorm:"type:varchar(255);not null"`                                 // 自管理养老金账户名称
	SmsfBankAccountName          string     `json:"smsfBankAccountName" gorm:"type:varchar(255);not null"`                             // 自管理养老金银行账户名称
	BsbCode                      string     `json:"bsbCode" gorm:"type:varchar(255);not null"`                                         // 银行BSB代码(Bank State Branch)
	SmsfBankAccountNumber        string     `json:"smsfBankAccountNumber" gorm:"type:varchar(255);not null"`                           // 自管理养老金银行账户号码
	DeclarationConfirmed         string     `json:"declarationConfirmed" gorm:"type:varchar(1);not null"`                              // 声明 Y/N
	SubmittedTime                *time.Time `json:"submittedTime" gorm:"type:datetime(0)"`                                             // 提交时间
	xmodel.Model
}

func (ProfessionalSuperannuation) TableName() string {
	return "professional_superannuation"
}

func (ProfessionalSuperannuation) SwaggerDescription() string {
	return "养老金信息"
}
