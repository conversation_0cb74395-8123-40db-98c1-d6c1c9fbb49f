package model

import (
	"github.com/Norray/xrocket/xmodel"
)

// JobBenefit 工作福利
type JobBenefit struct {
	Id         uint64 `json:"id" gorm:"primary_key"`                         // Id
	FacilityId uint64 `json:"facilityId" gorm:"index:facility_idx;not null"` // 機構Id
	JobId      uint64 `json:"jobId" gorm:"index:job_idx;not null"`           // 工作Id
	BenefitId  uint64 `json:"benefitId" gorm:"index:benefit_idx;not null"`   // 福利Id
	xmodel.Model
}

func (JobBenefit) TableName() string {
	return "job_benefit"
}

func (JobBenefit) SwaggerDescription() string {
	return "工作福利"
}
