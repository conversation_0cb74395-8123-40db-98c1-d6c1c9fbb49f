package routers

import (
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

func TestFacilitySpecialisationInquire(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/facility-specialisations/actions/inquire",
		UserId:           11,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢機構專業細分樹狀結構",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilitySpecialisationInquireReq{
					FacilityId:   3,
					SelectedOnly: "N",
				},
			},
			{
				SubName:           "只查詢已選中的專業",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilitySpecialisationInquireReq{
					FacilityId:   3,
					SelectedOnly: "Y",
				},
			},
			{
				SubName:           "無效機構Id",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.FacilitySpecialisationInquireReq{
					FacilityId: 0,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilitySpecialisationEdit(t *testing.T) {
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/facility-specialisations/actions/edit",
		UserId:           11,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "更新機構專業細分",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常更新護士專業細分",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilitySpecialisationEditReq{
					FacilityId:               3,
					MedicalPractitionerCodes: []string{"PSMP_M_ALLERGY"},
					EnrolledNurseCodes:       []string{"PSN_AGED_CARE"},
					RegisteredNurseCodes:     []string{"PSN_MENTAL_HEALTH"},
					PersonalCareWorkerCodes:  []string{},
				},
			},
			//{
			//	SubName:           "正常更新個人護理員專業細分",
			//	ExpectErrRespCode: xresp.StatusOK,
			//	Params: services.FacilitySpecialisationEditReq{
			//		FacilityId:               3,
			//		MedicalPractitionerCodes: []string{},
			//		EnrolledNurseCodes:       []string{},
			//		RegisteredNurseCodes:     []string{},
			//		PersonalCareWorkerCodes:  []string{"PSPCW_AGED_CARE"},
			//	},
			//},
			//{
			//	SubName:           "清空所有專業細分",
			//	ExpectErrRespCode: xresp.StatusOK,
			//	Params: services.FacilitySpecialisationEditReq{
			//		FacilityId:               3,
			//		MedicalPractitionerCodes: []string{},
			//		EnrolledNurseCodes:       []string{},
			//		RegisteredNurseCodes:     []string{},
			//		PersonalCareWorkerCodes:  []string{},
			//	},
			//},
			//{
			//	SubName:           "無效機構Id",
			//	ExpectErrRespCode: xresp.StatusBadRequest,
			//	Params: services.FacilitySpecialisationEditReq{
			//		FacilityId:               0,
			//		MedicalPractitionerCodes: []string{},
			//		EnrolledNurseCodes:       []string{"PSN_AGED_CARE"},
			//		RegisteredNurseCodes:     []string{},
			//		PersonalCareWorkerCodes:  []string{},
			//	},
			//},
			//{
			//	SubName:           "無效護士專業細分代碼",
			//	ExpectErrRespCode: xresp.StatusBadRequest,
			//	Params: services.FacilitySpecialisationEditReq{
			//		FacilityId:               3,
			//		MedicalPractitionerCodes: []string{},
			//		EnrolledNurseCodes:       []string{"INVALID_CODE"},
			//		RegisteredNurseCodes:     []string{},
			//		PersonalCareWorkerCodes:  []string{},
			//	},
			//},
		},
	}
	xtest.RunTests(t, test)
}
