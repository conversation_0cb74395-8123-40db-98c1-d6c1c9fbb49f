package services

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"github.com/Norray/medic-crew/model"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"

	"github.com/Norray/xrocket/xconfig"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	log "github.com/sirupsen/logrus"
)

var ReCaptchaService = new(reCaptchaService)

type reCaptchaService struct{}

// ReCaptchaVerifyResp 驗證響應
type ReCaptchaVerifyResp struct {
	Success     bool     `json:"success"`      // 驗證是否成功
	Score       float64  `json:"score"`        // 分數 (0.0 - 1.0)
	Action      string   `json:"action"`       // 動作名稱
	ChallengeTs string   `json:"challenge_ts"` // 挑戰時間戳
	Hostname    string   `json:"hostname"`     // 主機名
	ErrorCodes  []string `json:"error-codes"`  // 錯誤代碼
}

func (s *reCaptchaService) NewHttpClient() *http.Client {
	// 創建帶代理的 HTTP 客戶端
	client := &http.Client{}
	transport := &http.Transport{}

	// 設置代理
	transport.Proxy = func(req *http.Request) (*url.URL, error) {
		var proxyURL string
		if req.URL.Scheme == "https" {
			proxyURL = xconfig.ServerConf.HttpsProxy
		} else {
			proxyURL = xconfig.ServerConf.HttpProxy
		}

		if proxyURL == "" {
			return nil, nil
		}

		parsedURL, err := url.Parse(proxyURL)
		if err != nil {
			return nil, fmt.Errorf("failed to parse proxy URL: %v", err)
		}
		return parsedURL, nil
	}

	client.Transport = transport

	return client
}

// Verify 驗證 Google reCAPTCHA token
func (s *reCaptchaService) Verify(token string, clientIp string) (ReCaptchaVerifyResp, error) {
	var resp ReCaptchaVerifyResp

	// 構建請求參數
	form := url.Values{}
	form.Add("secret", xconfig.ReCaptchaConf.SecretKey)
	form.Add("response", token)
	if clientIp != "" {
		form.Add("remoteip", clientIp)
	}

	client := s.NewHttpClient()

	// 發送請求到 Google reCAPTCHA API
	httpResp, err := client.Post(
		"https://www.google.com/recaptcha/api/siteverify",
		"application/x-www-form-urlencoded",
		strings.NewReader(form.Encode()),
	)
	if err != nil {
		return resp, fmt.Errorf("request reCAPTCHA verify failed: %v", err)
	}
	defer httpResp.Body.Close()

	// 讀取響應內容
	body, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return resp, fmt.Errorf("failed to read response: %v", err)
	}

	// 解析 JSON 響應
	if err = json.Unmarshal(body, &resp); err != nil {
		return resp, fmt.Errorf("failed to parse response: %v", err)
	}

	return resp, nil
}

// 檢查 reCAPTCHA 驗證是否有效
func (s *reCaptchaService) CheckReCaptcha(db *gorm.DB, token string, clientIp string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.recaptcha.check_failed",
		Other: "reCAPTCHA verification failed",
	}
	scoreThreshold, err := CommonSettingService.GetSettingValueByCodeAsDecimal(db, model.CommonSettingCodeReCaptchaScoreThreshold)
	if err != nil {
		return false, i18n.Message{}, err
	}
	if scoreThreshold.IsZero() {
		log.Warn("reCAPTCHA score threshold is zero, skipping score check")
		return true, msg, nil
	}
	resp, err := s.Verify(token, clientIp)
	if err != nil {
		log.Warnf("reCAPTCHA verify failed: %v", err)
		return false, msg, err
	}
	// 檢查是否成功
	if !resp.Success {
		log.Warnf("reCAPTCHA verification failed: %v", resp.ErrorCodes)
		return false, msg, nil
	}
	// 檢查分數是否低於得分閾值
	if scoreThreshold.GreaterThan(decimal.NewFromFloat(resp.Score)) {
		log.Warnf("reCAPTCHA score is below threshold: %v", resp.Score)
		return false, msg, nil
	}
	return true, i18n.Message{}, nil
}
