package model

import "github.com/Norray/xrocket/xmodel"

// 機構黑名單
type FacilityBlacklist struct {
	Id             uint64 `json:"id" gorm:"primary_key"`
	FacilityId     uint64 `json:"facilityId" gorm:"index:facility_idx;not null"`         // 機構Id
	UserId         uint64 `json:"userId" gorm:"index:user_idx;not null"`                 // 專業人員的用戶Id
	ProfessionalId uint64 `json:"professionalId" gorm:"index:professional_idx;not null"` // 專業人員Id
	Reason         string `json:"reason" gorm:"type:varchar(255);not null"`              // 原因
	xmodel.Model
}

func (FacilityBlacklist) TableName() string {
	return "facility_blacklist"
}

func (FacilityBlacklist) SwaggerDescription() string {
	return "機構黑名單"
}
