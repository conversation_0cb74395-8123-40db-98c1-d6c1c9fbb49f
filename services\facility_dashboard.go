package services

import (
	"fmt"
	"time"

	"github.com/Norray/xrocket/xmodel/xtype"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xtool"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

var FacilityDashboardService = new(facilityDashboardService)

type facilityDashboardService struct{}

// region ------------------------------------------ 儀表板匯總統計 ------------------------------------------

type FacilityDashboardSummaryReq struct {
	FacilityId uint64 `form:"facilityId" binding:"required"`                    // 機構ID
	BeginDate  string `form:"beginDate" binding:"required,datetime=2006-01-02"` // 開始日期
	EndDate    string `form:"endDate" binding:"required,datetime=2006-01-02"`   // 結束日期
	TimeZone   string `form:"timeZone" binding:"required,timezone"`             // 時區
}

type FacilityDashboardSummaryResp struct {
	TotalExpenseAmount     decimal.Decimal `json:"totalExpenseAmount"`     // 總支出
	CompletedJobsCount     int             `json:"completedJobsCount"`     // 已完成工作數
	HiredProfessionalCount int             `json:"hiredProfessionalCount"` // 受聘專業人員總數
}

func (s *facilityDashboardService) DashboardSummary(db *gorm.DB, req FacilityDashboardSummaryReq) (FacilityDashboardSummaryResp, error) {
	var resp FacilityDashboardSummaryResp

	// 解析時區
	tz, err := time.LoadLocation(req.TimeZone)
	if err != nil {
		return resp, err
	}

	// 解析開始和結束日期
	beginDate, err := time.ParseInLocation(xtool.DateDayA, req.BeginDate, tz)
	if err != nil {
		return resp, err
	}
	endDate, err := time.ParseInLocation(xtool.DateDayA, req.EndDate, tz)
	if err != nil {
		return resp, err
	}
	// 調整結束日期到當天的23:59:59
	endDate = time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 23, 59, 59, 0, tz)
	beginDateUTC := beginDate.UTC()
	endDateUTC := endDate.UTC()

	// 1. 計算總支出 - 所有金額的合計
	var totalExpenseResult struct {
		TotalAmount decimal.Decimal `json:"totalAmount"`
	}

	expenseQuery := db.Table("document AS d").
		Where("d.category = ?", model.DocumentCategoryInvoice).
		Where("d.facility_id = ?", req.FacilityId).
		Where("d.data_type IN (?)", []string{model.DocumentDataTypeSystemToFacility, model.DocumentDataTypeProfessionalToFacility}).
		Where("d.document_date >= ?", req.BeginDate).
		Where("d.document_date <= ?", req.EndDate).
		Where("d.progress = ?", model.DocumentProgressConfirm).
		Select("COALESCE(SUM(d.grand_total), 0) as total_amount")
	if err = expenseQuery.Scan(&totalExpenseResult).Error; err != nil {
		return resp, err
	}
	resp.TotalExpenseAmount = totalExpenseResult.TotalAmount

	// 2. 計算已完成工作數
	var completedJobsCount int64
	jobCountQuery := db.Table("job AS j").
		Where("j.facility_id = ?", req.FacilityId).
		Where("j.status IN (?)", []string{model.JobStatusComplete, model.JobStatusPublish}).
		Where("j.end_time >= ? AND j.end_time <= ?", beginDateUTC, endDateUTC)

	if err = jobCountQuery.Count(&completedJobsCount).Error; err != nil {
		return resp, err
	}
	resp.CompletedJobsCount = int(completedJobsCount)

	// 3. 計算已完成工作數裡面的專業人士數量
	var hiredProfessionalCount int64
	professionalCountQuery := db.Table("job_application AS ja").
		Joins("JOIN job AS j ON j.id = ja.job_id").
		Where("j.facility_id = ?", req.FacilityId).
		Where("j.status IN (?)", []string{model.JobStatusComplete, model.JobStatusPublish}).
		Where("j.end_time >= ? AND j.end_time <= ?", beginDateUTC, endDateUTC).
		Select("COUNT(DISTINCT ja.professional_id) as total_count")
	if err = professionalCountQuery.Count(&hiredProfessionalCount).Error; err != nil {
		return resp, err
	}
	resp.HiredProfessionalCount = int(hiredProfessionalCount)

	return resp, nil
}

// endregion ---------------------------------------------------- 儀表板匯總統計 ----------------------------------------------------

// region ---------------------------------------------------- 機構支出統計 ----------------------------------------------------

type FacilityExpenseReq struct {
	FacilityId uint64 `form:"facilityId" binding:"required"`                    // 機構ID
	BeginDate  string `form:"beginDate" binding:"required,datetime=2006-01-02"` // 開始日期
	EndDate    string `form:"endDate" binding:"required,datetime=2006-01-02"`   // 結束日期
	TimeZone   string `form:"timeZone" binding:"required,timezone"`             // 時區
}

type PeriodExpenseItem struct {
	Date             xtype.Date      `swaggertype:"string" json:"date"` // 日期
	TotalAmount      decimal.Decimal `json:"totalAmount"`               // 總金額
	SalaryAmount     decimal.Decimal `json:"salaryAmount"`              // 薪資金額
	CommissionAmount decimal.Decimal `json:"commissionAmount"`          // 佣金金額
	OtherAmount      decimal.Decimal `json:"otherAmount"`               // 其他金額
}

var SalaryAmountItemTypes = []interface{}{model.DocumentItemTypeWages, model.DocumentItemTypeAdditionalWages, model.DocumentItemTypeAllowance, model.DocumentItemTypeCancellationFee, model.DocumentItemTypeSuperAmount}
var OtherAmountItemTypes = []interface{}{model.DocumentItemTypeTravelReimbursement, model.DocumentItemTypeMeals, model.DocumentItemTypeAccommodation}

func (s *facilityDashboardService) FacilityExpense(db *gorm.DB, req FacilityExpenseReq) ([]PeriodExpenseItem, error) {
	var resp []PeriodExpenseItem

	builder := db.Table("document_item AS di").
		Joins("JOIN document AS d ON d.id = di.document_id").
		Where("d.category = ?", model.DocumentCategoryInvoice).
		Where("d.facility_id = ?", req.FacilityId).
		Where("d.data_type IN (?)", []string{model.DocumentDataTypeSystemToFacility, model.DocumentDataTypeProfessionalToFacility}).
		Where("d.document_date >= ?", req.BeginDate).
		Where("d.document_date <= ?", req.EndDate).
		Where("d.progress = ?", model.DocumentProgressConfirm).
		Select([]string{
			"d.document_date as date",
			"SUM(di.total_amount) as total_amount",
			fmt.Sprintf("SUM(CASE WHEN di.item_type IN ('%s', '%s', '%s', '%s', '%s') THEN di.total_amount ELSE 0 END) as salary_amount", SalaryAmountItemTypes...),
			fmt.Sprintf("SUM(CASE WHEN di.item_type = '%s' THEN di.total_amount ELSE 0 END) as commission_amount", model.DocumentItemTypeCommissionAmount),
			fmt.Sprintf("SUM(CASE WHEN di.item_type IN ('%s', '%s', '%s', '%s') THEN di.total_amount ELSE 0 END) as other_amount", OtherAmountItemTypes...),
		})
	var result []PeriodExpenseItem
	if err := builder.
		Group("d.document_date").
		Order("d.document_date ASC").
		Scan(&result).Error; err != nil {
		return resp, err
	}

	// 創建數據映射
	dataMap := make(map[string]PeriodExpenseItem)
	for _, item := range result {
		dataMap[item.Date.String()] = item
	}

	dateRange, err := GenerateRange(req.BeginDate, req.EndDate)
	if err != nil {
		return resp, err
	}

	for _, dateStr := range dateRange {
		if item, exists := dataMap[dateStr]; exists {
			resp = append(resp, item)
		} else {
			resp = append(resp, PeriodExpenseItem{
				Date:             xtype.NewDate(dateStr),
				TotalAmount:      decimal.Zero,
				SalaryAmount:     decimal.Zero,
				CommissionAmount: decimal.Zero,
				OtherAmount:      decimal.Zero,
			})
		}
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 機構費用統計 ----------------------------------------------------

// region ---------------------------------------------------- 每種職業發佈工作量統計 ----------------------------------------------------

type FacilityJobStatisticReq struct {
	FacilityId uint64 `form:"facilityId" binding:"required"`                    // 機構ID
	BeginDate  string `form:"beginDate" binding:"required,datetime=2006-01-02"` // 開始日期
	EndDate    string `form:"endDate" binding:"required,datetime=2006-01-02"`   // 結束日期
	TimeZone   string `form:"timeZone" binding:"required,timezone"`             // 時區
}

type PeriodJobStatisticItem struct {
	Profession     string `json:"profession"`              // 職業
	ProfessionName string `json:"professionName" gorm:"-"` // 職業名稱
	JobCount       int    `json:"jobCount"`                // 工作數量
}

func (s *facilityDashboardService) FacilityJobStatistic(db *gorm.DB, req FacilityJobStatisticReq) ([]PeriodJobStatisticItem, error) {
	var resp []PeriodJobStatisticItem
	// 解析時區
	tz, err := time.LoadLocation(req.TimeZone)
	if err != nil {
		return resp, err
	}

	// 解析開始和結束日期
	beginDate, err := time.ParseInLocation(xtool.DateDayA, req.BeginDate, tz)
	if err != nil {
		return resp, err
	}
	endDate, err := time.ParseInLocation(xtool.DateDayA, req.EndDate, tz)
	if err != nil {
		return resp, err
	}
	// 調整結束日期到當天的23:59:59
	endDate = time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 23, 59, 59, 0, tz)
	beginDateUTC := beginDate.UTC()
	endDateUTC := endDate.UTC()

	builder := db.Table("job AS j").
		Where("j.facility_id = ?", req.FacilityId).
		Where("j.status = ?", model.JobStatusPublish).
		Where("j.publish_time < ?", endDateUTC).
		Where("j.begin_time >= ?", beginDateUTC).
		Select([]string{
			"j.position_profession as profession",
			"COUNT(j.id) as job_count",
		})
	if err := builder.
		Group("j.position_profession").
		Order("FIELD(j.position_profession, 'MEDICAL_PRACTITIONER', 'ENROLLED_NURSE', 'REGISTERED_NURSE', 'PERSONAL_CARE_WORKER')").
		Scan(&resp).Error; err != nil {
		return resp, err
	}
	var professionNameMap map[string]string
	professionNameMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
	if err != nil {
		return resp, err
	}
	for i, item := range resp {
		resp[i].ProfessionName = professionNameMap[item.Profession]
	}
	return resp, nil
}

// endregion ---------------------------------------------------- 機構費用統計 ----------------------------------------------------

// region ---------------------------------------------------- 每種職業工資統計統計 ----------------------------------------------------

type FacilityJobSalaryStatisticReq struct {
	FacilityId uint64 `form:"facilityId" binding:"required"`                    // 機構ID
	BeginDate  string `form:"beginDate" binding:"required,datetime=2006-01-02"` // 開始日期
	EndDate    string `form:"endDate" binding:"required,datetime=2006-01-02"`   // 結束日期
	TimeZone   string `form:"timeZone" binding:"required,timezone"`             // 時區
}

type PeriodJobSalaryStatisticItem struct {
	Profession     string          `json:"profession"`              // 職業
	ProfessionName string          `json:"professionName" gorm:"-"` // 職業名稱
	SalaryAmount   decimal.Decimal `json:"salaryAmount"`            // 薪資金額
}

func (s *facilityDashboardService) FacilityJobSalaryStatistic(db *gorm.DB, req FacilityJobSalaryStatisticReq) ([]PeriodJobSalaryStatisticItem, error) {
	var err error
	var resp []PeriodJobSalaryStatisticItem

	// 查詢每種職業的薪資統計
	builder := db.Table("document_item AS di").
		Joins("JOIN document AS d ON d.id = di.document_id").
		Joins("JOIN job_application AS ja ON ja.id = d.job_application_id").
		Joins("JOIN job AS j ON j.id = ja.job_id").
		Where("d.category = ?", model.DocumentCategoryInvoice).
		Where("d.facility_id = ?", req.FacilityId).
		Where("d.data_type IN (?)", []string{model.DocumentDataTypeSystemToFacility, model.DocumentDataTypeProfessionalToFacility}).
		Where("d.document_date >= ?", req.BeginDate).
		Where("d.document_date <= ?", req.EndDate).
		Where("di.item_type IN (?)", SalaryAmountItemTypes).
		Where("d.progress = ?", model.DocumentProgressConfirm).
		Select([]string{
			"j.position_profession as profession",
			"SUM(di.total_amount) as salary_amount",
		}).
		Group("j.position_profession").
		Order("FIELD(j.position_profession, 'MEDICAL_PRACTITIONER', 'ENROLLED_NURSE', 'REGISTERED_NURSE', 'PERSONAL_CARE_WORKER')")

	if err := builder.Scan(&resp).Error; err != nil {
		return resp, err
	}

	var professionNameMap map[string]string
	professionNameMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
	if err != nil {
		return resp, err
	}
	for i, item := range resp {
		resp[i].ProfessionName = professionNameMap[item.Profession]
	}
	return resp, nil
}

// endregion ---------------------------------------------------- 機構費用統計 ----------------------------------------------------
