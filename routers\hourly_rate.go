package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/gin-gonic/gin"
)

func hourlyRateFacilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		r.GET("/hourly-rates", facility_api.NewHourlyRateController().List)
		r.POST("/hourly-rates/actions/update", facility_api.NewHourlyRateController().Update)
	}
}
