package model

import (
	"encoding/json"

	"github.com/Norray/xrocket/xmodel"
)

const (
	TrainingModuleStatusActive   = "ACTIVE"   // 啟用
	TrainingModuleStatusInactive = "INACTIVE" // 停用

	TrainingQuestionTypeSingleChoice = "SINGLE_CHOICE" // 單選

	TrainingAnswerIsCorrectY = "Y" // 正確
	TrainingAnswerIsCorrectN = "N" // 錯誤
)

// 培訓模塊
type TrainingModule struct {
	Id          uint64 `json:"id" gorm:"primary_key"`
	Title       string `json:"title" gorm:"type:varchar(255);not null"`                  // 模塊標題
	Description string `json:"description" gorm:"type:text;not null"`                    // 模塊描述
	Seq         int32  `json:"seq" gorm:"index:seq_idx;not null"`                        // 排序順序
	Status      string `json:"status" gorm:"type:varchar(32);index:status_idx;not null"` // 狀態
	DetailJson  string `json:"detailJson" gorm:"type:mediumtext;not null"`               // 模塊詳情JSON
	xmodel.Model
}

func (TrainingModule) TableName() string {
	return "training_module"
}

func (TrainingModule) SwaggerDescription() string {
	return "培訓模塊"
}

func (p *TrainingModule) Unmarshal(jsonStr string) (ProfessionalProfile, error) {
	var profile ProfessionalProfile
	err := json.Unmarshal([]byte(jsonStr), &profile)
	if err != nil {
		return profile, err
	}
	return profile, nil
}

func (p *TrainingModule) Marshal(detail TrainingModuleDetail) error {
	detailJson, err := json.Marshal(detail)
	if err != nil {
		return err
	}
	p.DetailJson = string(detailJson)
	return nil
}

type TrainingModuleDetail struct {
	Video     TrainingModuleVideo `json:"video"`
	Questions []TrainingQuestion  `json:"questions"`
}

// 培訓視頻
type TrainingModuleVideo struct {
	Title        string `json:"title"`        // 模塊標題
	Description  string `json:"description"`  // 模塊描述
	Url          string `json:"url"`          // 模塊URL
	Duration     int32  `json:"duration"`     // 視頻時長(秒)
	ThumbnailUrl string `json:"thumbnailUrl"` // 縮略圖URL
}

// 培訓問題
type TrainingQuestion struct {
	QuestionText string                   `json:"questionText" ` // 問題內容
	QuestionType string                   `json:"questionType" ` // 問題類型
	Options      []TrainingQuestionOption `json:"options"`       // 選項
	Seq          int32                    `json:"seq"`           // 排序順序
}

// 培訓答案
type TrainingQuestionOption struct {
	OptionText string `json:"optionText"` // 選項內容
	IsCorrect  string `json:"isCorrect"`  // 是否正確答案 Y/N
	Seq        int32  `json:"seq"`        // 排序順序 (A,B,C,D對應1,2,3,4)
}
