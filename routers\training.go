package routers

import (
	"github.com/Norray/medic-crew/api/v1/professional_api"
	"github.com/gin-gonic/gin"
)

func trainingProfessionalRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/professional").Use(handlers...)
	{
		controller := professional_api.NewProfessionalTrainingController()
		r.GET("/training-modules", controller.ModuleList)                                   // 獲取培訓模塊列表
		r.GET("/training-modules/actions/inquire", controller.ModuleInquire)                // 獲取培訓模塊詳情
		r.GET("/training-modules/actions/questions", controller.ModuleQuestions)            // 獲取培訓模塊問題
		r.GET("/training-modules/actions/progress", controller.ModuleProgress)              // 獲取用戶培訓進度
		r.POST("/training-modules/actions/answer", controller.Answer)                       // 回答培訓問題
		r.POST("/training-modules/actions/mark-video-watched", controller.MarkVideoWatched) // 標記視頻觀看完成
	}
}
