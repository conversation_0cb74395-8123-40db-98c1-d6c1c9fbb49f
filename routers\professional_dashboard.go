package routers

import (
	"github.com/Norray/medic-crew/api/v1/professional_api"
	"github.com/gin-gonic/gin"
)

func professionalDashboardRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/professional").Use(handlers...)
	{
		controller := professional_api.NewProfessionalDashboardController()
		r.GET("/dashboard/income", controller.Income)              // 專業人員收入統計
		r.GET("/dashboard/working-hours", controller.WorkingHours) // 專業人員工作時間統計
		r.GET("/dashboard/summary", controller.Summary)            // 儀表板匯總統計
	}
}
