package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func userSystemRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/system/").Use(handlers...)
	{
		controller := system_api.NewSystemUserController()
		r.GET("/users", controller.List)
		r.GET("/users/actions/search", controller.Search)
		r.GET("/users/actions/inquire", controller.Inquire)
		r.POST("/users/actions/create", controller.Create)
		r.POST("/users/actions/edit", controller.Edit)
		r.POST("/users/actions/delete", controller.Delete)
		r.GET("/users/actions/search-all", controller.SearchAll)
	}
}

func userFacilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility/").Use(handlers...)
	{
		controller := facility_api.NewFacilityUserController()
		r.GET("/users", controller.List)
		r.GET("/users/actions/search", controller.Search)
		r.GET("/users/actions/inquire", controller.Inquire)
		r.POST("/users/actions/create", controller.Create)
		r.POST("/users/actions/edit", controller.Edit)
		r.POST("/users/actions/delete", controller.Delete)
	}
}
