package v1

import (
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
)

type SystemNotificationController struct {
	CommonController
}

func NewSystemNotificationController() SystemNotificationController {
	return SystemNotificationController{}
}

// @Tags SystemNotification
// @Summary 獲取用戶通知列表
// @Description 獲取當前用戶的通知列表，支援分頁和已讀狀態篩選
// @Router /v1/app/system-notifications [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param read query string false "已讀狀態篩選: Y=已讀, N=未讀"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Success 200 {object} []services.UserNotificationItem "Success"
func (con SystemNotificationController) GetList(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.GetUserNotificationListReq
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		// 嚴格設定用戶ID，防止越權存取
		req.UserId = nc.GetJWTUserId()
		resp, err := services.SystemNotificationService.GetUserNotificationList(db, req, &pageSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags SystemNotification
// @Summary 標記通知讀取狀態
// @Description 將指定的通知標記為已讀或未讀狀態，支援切換功能
// @Router /v1/app/system-notifications/actions/mark-read [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.MarkNotificationAsReadReq true "通知ID列表和讀取狀態"
// @Success 200 {object} xresp.Response "Success"
func (con SystemNotificationController) MarkAsRead(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.MarkNotificationAsReadReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		// 嚴格設定用戶ID，防止越權存取
		req.UserId = nc.GetJWTUserId()
		err := services.SystemNotificationService.MarkNotificationAsRead(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags SystemNotification
// @Summary 標記所有通知為已讀
// @Description 將當前用戶的所有未讀通知標記為已讀狀態
// @Router /v1/app/system-notifications/actions/mark-all-read [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 "Success"
func (con SystemNotificationController) MarkAllAsRead(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.MarkAllNotificationsAsReadReq
	db := xgorm.DB.WithContext(c)
	// 嚴格設定用戶ID，防止越權存取
	req.UserId = nc.GetJWTUserId()
	err := services.SystemNotificationService.MarkAllNotificationsAsRead(db, req)
	if err != nil {
		nc.ErrorResponse(req, err)
		return
	}
	nc.OKResponse(nil)
}

// @Tags SystemNotification
// @Summary 刪除通知
// @Description 軟刪除指定的通知
// @Router /v1/app/system-notifications/actions/delete [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.DeleteNotificationReq true "通知ID列表"
// @Success 200 {object} xresp.Response "Success"
func (con SystemNotificationController) Delete(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.DeleteNotificationReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		// 嚴格設定用戶ID，防止越權存取
		req.UserId = nc.GetJWTUserId()
		err := services.SystemNotificationService.DeleteNotification(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags SystemNotification
// @Summary 獲取未讀通知數量
// @Description 獲取當前用戶的未讀通知數量
// @Router /v1/app/system-notifications/unread-count [GET]
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} services.GetUnreadCountResp "Success"
func (con SystemNotificationController) GetUnreadCount(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.GetUnreadCountReq
	db := xgorm.DB.WithContext(c)
	// 嚴格設定用戶ID，防止越權存取
	req.UserId = nc.GetJWTUserId()
	resp, err := services.SystemNotificationService.GetUnreadCount(db, req)
	if err != nil {
		nc.ErrorResponse(req, err)
		return
	}
	nc.OKResponse(resp)
}
