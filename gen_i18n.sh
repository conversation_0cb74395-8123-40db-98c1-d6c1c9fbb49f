#!/bin/bash
# 复制此文件到根目录
set -e  # Exit on any error

goi18n extract -outdir=resource/i18n
cd ./resource/i18n || exit 1

goi18n merge active.en.toml active.zh-CN.toml active.zh-HK.toml
if [ -f "translate.zh-CN.toml" ]; then
    goi18n merge active.en.toml active.zh-CN.toml active.zh-HK.toml translate.zh-CN.toml
fi
if [ -f "translate.zh-HK.toml" ]; then
    goi18n merge active.en.toml active.zh-CN.toml active.zh-HK.toml translate.zh-HK.toml
fi

# 檢查是否生成了新的翻譯文件
if [ -f "translate.zh-CN.toml" ] || [ -f "translate.zh-HK.toml" ]; then
    echo "New translation content detected, please update translations before committing."
    
    # 清理生成的文件
    if [ -f "translate.zh-CN.toml" ]; then
        rm translate.zh-CN.toml
    fi
    if [ -f "translate.zh-HK.toml" ]; then
        rm translate.zh-HK.toml
    fi
    
    exit 1
fi