package model

import (
	"time"

	"github.com/Norray/xrocket/xmodel"
)

const (
	AgreementStatusEnable  = "ENABLE"  // 啟用 (一個模板最多只有一個)
	AgreementStatusDisable = "DISABLE" // 禁用
)

// 協議模板
type Agreement struct {
	Id          uint64    `json:"id" gorm:"primary_key"`
	Title       string    `json:"title" gorm:"type:varchar(255);not null"`       // 標題
	Content     string    `json:"content" gorm:"type:mediumtext;not null"`       // 內容
	ContentText string    `json:"contentText" gorm:"type:varchar(255);not null"` // 內容純文字
	Status      string    `json:"status" gorm:"type:varchar(32);not null"`       // 狀態 ENABLE DISABLE
	UpdateTime  time.Time `json:"updateTime" gorm:"type:datetime(0);not null"`   // 更新時間
	xmodel.Model
}

func (Agreement) TableName() string {
	return "agreement"
}

func (Agreement) SwaggerDescription() string {
	return "協議模板"
}
