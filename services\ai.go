package services

import (
	"errors"

	"github.com/Norray/xrocket/xgemini"
	"github.com/Norray/xrocket/xmodel"
	"gorm.io/gorm"
)

var AiService = new(aiService)

type aiService struct{}

type AiResp struct {
	Model       string `json:"model"`
	Output      string `json:"output"`
	InputToken  int32  `json:"inputToken"`
	OutputToken int32  `json:"outputToken"`
}

func (s *aiService) AiExpireDate(db *gorm.DB, filename string, data []byte) (AiResp, error) {
	prompt, err := s.GetExpireDatePrompt(db)
	if err != nil {
		return AiResp{}, err
	}
	if prompt == "" {
		return AiResp{}, errors.New("prompt is not set")
	}
	modelName := "gemini-2.0-flash-001"
	result, err := xgemini.GeminiChatCompletions(&xgemini.GeminiChatCompletionsReq{
		Prompt:          prompt,
		ModelName:       modelName,
		Temperature:     0.7,
		TopK:            1,
		TopP:            0,
		MaxOutputTokens: xgemini.GeminiDefaultMaxOutputTokens,
		File: []*xgemini.GeminiFile{
			{
				FileName: filename,
				MimeType: xgemini.GetFileMimeType(filename),
				Content:  data,
			},
		},
	})
	if err != nil {
		return AiResp{}, err
	}
	return AiResp{
		Model:       modelName,
		Output:      xgemini.RemoveJSONMarkdown(result.Output),
		InputToken:  result.InputToken,
		OutputToken: result.OutputToken,
	}, nil
}

func (s *aiService) GetExpireDatePrompt(db *gorm.DB) (string, error) {
	var setting xmodel.CommonSetting
	if err := db.Where("code = ?", "EXPIRE_DATE_PROMPT").First(&setting).Error; err != nil {
		return "", err
	}
	return setting.SettingValue, nil
}
