# Job Allowance API 文檔

## 概述

JobAllowance 採用 **Job 級別設計**，津貼配置在 Job 層面統一管理，支持動態計算每個 JobShift 的津貼金額。

## 數據結構

### JobAllowanceReq（津貼配置）
```json
{
  "allowanceId": 1,
  "allowanceName": "小時津貼",
  "allowanceType": "HOURLY",  // HOURLY | SHIFT | JOB
  "baseAmount": 5.00,
  "attractsSuperannuation": "N"  // 從數據庫獲取，前端不傳
}
```

### AllowanceDetail（計算結果）
```json
{
  "allowanceId": 1,
  "allowanceName": "小時津貼",
  "allowanceType": "HOURLY",
  "baseAmount": 5.00,
  "amount": 40.00,  // 動態計算結果
  "attractsSuperannuation": "N"
}
```

## API 接口

### 1. 創建 Job
```http
POST /api/v1/facility/job
```

**請求體**：
```json
{
  "jobShiftItems": [
    {
      "beginTime": "2025-01-01T08:00:00Z",
      "endTime": "2025-01-01T16:00:00Z",
      "payHours": 8.0,
      "hourlyRate": 30.0
    },
    {
      "beginTime": "2025-01-02T08:00:00Z", 
      "endTime": "2025-01-02T12:00:00Z",
      "payHours": 4.0,
      "hourlyRate": 30.0
    }
  ],
  "allowances": [
    {
      "allowanceId": 1,
      "allowanceName": "小時津貼",
      "allowanceType": "HOURLY",
      "baseAmount": 5.0
    },
    {
      "allowanceId": 2,
      "allowanceName": "班次津貼", 
      "allowanceType": "SHIFT",
      "baseAmount": 20.0
    },
    {
      "allowanceId": 3,
      "allowanceName": "職位津貼",
      "allowanceType": "JOB", 
      "baseAmount": 100.0
    }
  ]
}
```

### 2. 查詢 Job 列表
```http
GET /api/v1/facility/job/list
```

**響應**：
```json
{
  "data": [
    {
      "jobId": 123,
      "jobShiftItems": [
        {
          "jobShiftId": 456,
          "payHours": 8.0,
          "hourlyRate": 30.0,
          "allowanceAmount": 116.0  // 動態計算的總津貼
        },
        {
          "jobShiftId": 457,
          "payHours": 4.0,
          "hourlyRate": 30.0,
          "allowanceAmount": 96.0   // 動態計算的總津貼
        }
      ],
      "allowances": [
        {
          "allowanceId": 1,
          "allowanceName": "小時津貼",
          "allowanceType": "HOURLY",
          "baseAmount": 5.0
        },
        {
          "allowanceId": 2,
          "allowanceName": "班次津貼",
          "allowanceType": "SHIFT", 
          "baseAmount": 20.0
        },
        {
          "allowanceId": 3,
          "allowanceName": "職位津貼",
          "allowanceType": "JOB",
          "baseAmount": 100.0
        }
      ]
    }
  ]
}
```

### 3. 計算 JobShift 津貼明細
```http
GET /api/v1/facility/job-shift/{jobShiftId}/allowance-details
```

**響應**：
```json
{
  "totalAmount": 116.0,
  "details": [
    {
      "allowanceId": 1,
      "allowanceName": "小時津貼",
      "allowanceType": "HOURLY",
      "baseAmount": 5.0,
      "amount": 40.0,  // 5.0 × 8小時
      "attractsSuperannuation": "N"
    },
    {
      "allowanceId": 2,
      "allowanceName": "班次津貼",
      "allowanceType": "SHIFT",
      "baseAmount": 20.0,
      "amount": 20.0,  // 固定金額
      "attractsSuperannuation": "N"
    },
    {
      "allowanceId": 3,
      "allowanceName": "職位津貼",
      "allowanceType": "JOB",
      "baseAmount": 100.0,
      "amount": 56.0,  // (100.0 ÷ 2) × 1.12（含退休金）
      "attractsSuperannuation": "Y"
    }
  ]
}
```

## 計算邏輯

### HOURLY（按小時）
```
amount = baseAmount × payHours
```

### SHIFT（按班次）
```
amount = baseAmount
```

### JOB（按職位）
```
amount = baseAmount ÷ totalJobShifts
誤差分配給最後一個 JobShift
```

### 退休金處理
```
if attractsSuperannuation == "Y":
    amount = amount × 1.12
```

## 前端適配

### 主要變更
1. **津貼配置提升**：從 `jobShiftItems[].allowances` 提升到與 `jobShiftItems` 同層
2. **動態計算**：`allowanceAmount` 由後端動態計算，前端無需計算
3. **配置統一**：所有 JobShift 共享相同的津貼配置

### 遷移指南
```javascript
// 舊格式
{
  jobShiftItems: [
    {
      allowances: [...]  // ❌ 移除
    }
  ]
}

// 新格式  
{
  jobShiftItems: [...],
  allowances: [...]     // ✅ 提升到 Job 級別
}
```

## 優勢

1. **數據簡化**：減少 80% 津貼存儲記錄
2. **一致性保證**：Job 發佈後配置不可變
3. **計算準確**：統一的誤差處理邏輯
4. **維護便利**：津貼規則集中管理
