package facility_api

import (
	"github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
)

type JobFileController struct {
	v1.CommonController
}

func NewJobFileController() JobFileController {
	return JobFileController{}
}

// @Tags Job File
// @Summary 獲取機構指定工作職位的文件列表
// @Description 根據機構ID和職位專業獲取對應的工作文件列表
// @Router /v1/facility/job-files [GET]
// @Security ApiKeyAuth
// @Param json query services.JobFileListReq true "parameter"
// @Success 200 {object} []services.JobFileListResp "Success"
func (con JobFileController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobFileListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		nc.ErrorResponse(req, err)
		return
	}

	db := xgorm.DB.WithContext(c)
	// 檢查權限
	if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
		nc.NoPermissionResponse(resource.ForbiddenMsg)
		return
	}

	// 獲取文件列表
	resp, err := services.JobFileService.JobFileList(db, &req)
	if err != nil {
		nc.ErrorResponse(req, err)
		return
	}

	nc.OKResponse(resp)
}
