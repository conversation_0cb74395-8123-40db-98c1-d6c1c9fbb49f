package model

import (
	"github.com/Norray/xrocket/xmodel"
	"github.com/shopspring/decimal"
)

const (
	PayForBreakTimeY = "Y"
	PayForBreakTimeN = "N"
)

// 機構的時薪管理
type HourlyRate struct {
	Id              uint64          `json:"id" gorm:"primary_key"`
	FacilityId      uint64          `json:"facilityId" gorm:"index:facility_idx;not null"`   // 機構ID
	Name            string          `json:"name" gorm:"type:varchar(255);not null"`          // 時薪名稱
	Code            string          `json:"code" gorm:"type:varchar(255);not null"`          // 時薪代碼
	PayForBreakTime string          `json:"payForBreakTime" gorm:"type:varchar(1);not null"` // 休息時間薪資
	Rate            decimal.Decimal `json:"rate" gorm:"type:decimal(10,2);not null"`         // 時薪倍率
	xmodel.Model
}

func (HourlyRate) TableName() string {
	return "hourly_rate"
}

func (HourlyRate) SwaggerDescription() string {
	return "機構的時薪管理"
}
