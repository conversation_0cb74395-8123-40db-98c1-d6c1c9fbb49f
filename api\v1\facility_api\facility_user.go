package facility_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type FacilityUserController struct {
	v1.CommonController
}

func NewFacilityUserController() FacilityUserController {
	return FacilityUserController{}
}

// @Tags User
// @Summary 機構用戶列表
// @Router /v1/facility/users [GET]
// @Produce  json
// @Param form query services.UserListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Success 200 {object} services.UserListResp "Success"
func (con FacilityUserController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.UserListReq
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.UserType = model.UserUserTypeFacilityUser
		req.FacilityId, err = con.GetUserFacilityId(nc, db)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		result, err := services.UserService.List(db, req, &pageSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(result, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags User
// @Summary 機構用戶搜索
// @Router /v1/facility/users/actions/search [GET]
// @Produce  json
// @Param form query services.UserSearchReq true "parameter"
// @Success 200 {object} services.UserSearchResp "Success"
func (con FacilityUserController) Search(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.UserSearchReq
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.UserType = model.UserUserTypeFacilityUser
		req.FacilityId, err = con.GetUserFacilityId(nc, db)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		result, err := services.UserService.Search(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(result)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags User
// @Summary 查询用戶
// @Description
// @Router /v1/facility/users/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.UserInquireReq true "parameter"
// @Success 200 {object} services.UserInquireResp "Success"
func (con FacilityUserController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.UserInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.UserType = model.UserUserTypeFacilityUser
		req.FacilityId, err = con.GetUserFacilityId(nc, db)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		resp, err := services.UserService.Inquire(db, req)
		if xgorm.IsNotFoundErr(err) {
			nc.BadRequestResponse(err)
			return
		}
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags User
// @Summary 機構用戶創建
// @Router /v1/facility/users/actions/create [POST]
// @Produce  json
// @Param json body services.UserCreateReq true "parameter"
// @Success 200 {object} services.UserCreateResp "Success"
func (con FacilityUserController) Create(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.UserCreateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.UserType = model.UserUserTypeFacilityUser
		req.FacilityId, err = con.GetUserFacilityId(nc, db)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.UserService.CheckUserEmailUnique(db, req.Email)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.RoleService.CheckRoleExists(db, model.UserUserTypeFacilityUser, req.RoleId, req.FacilityId)
			})
		if nc.BadRequestResponseIfCheckerFailed(checker, req) {
			return
		}
		tx := db.Begin()
		resp, err := services.UserService.Create(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags User
// @Summary 機構用戶編輯
// @Router /v1/facility/users/actions/edit [POST]
// @Produce  json
// @Param json body services.UserEditReq true "parameter"
// @Success 200 "Success"
func (con FacilityUserController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	db := xgorm.DB.WithContext(c)
	var req services.UserEditReq
	if err := c.ShouldBindJSON(&req); err == nil {
		req.UserType = model.UserUserTypeFacilityUser
		req.FacilityId, err = con.GetUserFacilityId(nc, db)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.UserService.CheckUserExists(db, model.UserUserTypeFacilityUser, req.UserId, req.FacilityId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.UserService.CheckUserEmailUnique(db, req.Email, req.UserId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.RoleService.CheckRoleExists(db, model.UserUserTypeFacilityUser, req.RoleId, req.FacilityId)
			})
		if nc.BadRequestResponseIfCheckerFailed(checker, req) {
			return
		}
		tx := db.Begin()
		err := services.UserService.Edit(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags User
// @Summary 機構用戶刪除
// @Router /v1/facility/users/actions/delete [POST]
// @Produce  json
// @Param json body services.UserDeleteReq true "parameter"
// @Success 200 "Success"
func (con FacilityUserController) Delete(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	db := xgorm.DB.WithContext(c)
	var req services.UserDeleteReq
	if err := c.ShouldBindJSON(&req); err == nil {
		req.UserType = model.UserUserTypeFacilityUser
		req.FacilityId, err = con.GetUserFacilityId(nc, db)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.UserService.CheckUserExists(db, model.UserUserTypeFacilityUser, req.UserId, req.FacilityId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.UserService.CheckUserCanDelete(db, req.UserId)
			})
		if nc.BadRequestResponseIfCheckerFailed(checker, req) {
			return
		}
		tx := db.Begin()
		err := services.UserService.Delete(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}
