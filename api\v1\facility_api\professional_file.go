package facility_api

import (
	"fmt"
	"net/http"

	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type ProfessionalFileController struct {
	v1.CommonController
}

func NewProfessionalFileController() ProfessionalFileController {
	return ProfessionalFileController{}
}

// @Tags Professional File
// @Summary 獲取專業人士文件圖片
// @Description 直接獲取專業人士文件的圖片內容，可以在瀏覽器中直接顯示
// @Router /v1/facility/professional-files/actions/preview [GET]
// @Security ApiKeyAuth
// @Param json query services.ProfessionalFileGetPreviewByFacilityReq true "parameter"
// @Success 200 "Success"
func (con ProfessionalFileController) Preview(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalFileGetPreviewByFacilityReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		// 檢查工作職位是否存在,並且是否可以機構訪問
		checker := xapp.NewCK(c, true)
		checker.Run(func() (bool, i18n.Message, error) {
			return services.ProfessionalFileService.FacilityCheckFileExist(db, req.FacilityId, req.ProfessionalId, req.ProfessionalFileId)
		})

		checkMsg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		// 獲取圖片數據
		resp, err := services.ProfessionalFileService.Preview(db, services.ProfessionalFileGetPreviewReq{
			ProfessionalFileId: req.ProfessionalFileId,
		})
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		c.Writer.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename*=utf-8''%s", xtool.ReplacePlus(resp.UuidName)))
		c.Data(http.StatusOK, services.FacilityFileService.GetFileMimeType(resp.UuidName), resp.FileBytes)

	} else {
		nc.BadRequestResponse(err)
	}
}
