package professional_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
)

type ProfessionalJobAvailabilityController struct {
	v1.CommonController
}

func NewProfessionalJobAvailabilityController() ProfessionalJobAvailabilityController {
	return ProfessionalJobAvailabilityController{}
}

// @Tags Professional Job Availability
// @Summary 更新專業人士工作可用性
// @Description 更新專業人士的工作可用時間和不可用時間設置
// @Router /v1/professional/professional-job-availabilities/actions/update [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalJobAvailabilityUpdateReq true "parameter"
// @Success 200 "Success"
func (con ProfessionalJobAvailabilityController) Update(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalJobAvailabilityUpdateReq
	var err error
	if err = c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		req.UserId = nc.GetJWTUserId()
		// 更新工作可用性設置
		tx := db.Begin()
		err = services.ProfessionalJobAvailabilityService.Update(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Job Availability
// @Summary 查詢專業人士工作可用性
// @Description 查詢專業人士的工作可用時間和不可用時間設置
// @Router /v1/professional/professional-job-availabilities/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} services.ProfessionalJobAvailabilityInquireResp "Success"
func (con ProfessionalJobAvailabilityController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}

	db := xgorm.DB.WithContext(c)

	var err error
	// 查詢工作可用性設置
	resp, err := services.ProfessionalJobAvailabilityService.Inquire(db, nc.GetJWTUserId())
	if err != nil {
		nc.ErrorResponse(nil, err)
		return
	}

	nc.OKResponse(resp)
}
