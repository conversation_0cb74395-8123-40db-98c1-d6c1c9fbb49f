package model

import (
	"github.com/Norray/xrocket/xmodel"
	"github.com/shopspring/decimal"
)

const (
	AllowanceStatusEnable  = "ENABLE"  // 啟用
	AllowanceStatusDisable = "DISABLE" // 停用

	AllowanceAttractsSuperannuationY = "Y" // 納入退休金
	AllowanceAttractsSuperannuationN = "N" // 不納入退休金
)

// 津貼
type Allowance struct {
	Id                     uint64          `json:"id" gorm:"primary_key"`
	FacilityId             uint64          `json:"facilityId" gorm:"index:facilityId_idx;not null"`          // 機構ID
	Name                   string          `json:"name" gorm:"type:varchar(255);index:name_idx;not null"`    // 津貼名稱
	Description            string          `json:"description" gorm:"type:varchar(255);not null"`            // 描述
	Status                 string          `json:"status" gorm:"type:varchar(32);index:status_idx;not null"` // 狀態 ENABLE DISABLE
	AttractsSuperannuation string          `json:"attractsSuperannuation" gorm:"type:varchar(1);not null"`   // 納入退休金 Y N
	PerHourAmount          decimal.Decimal `json:"perHourAmount" gorm:"type:decimal(9,2);not null"`          // 小時
	PerShiftAmount         decimal.Decimal `json:"perShiftAmount" gorm:"type:decimal(9,2);not null"`         // 班次
	PerJobAmount           decimal.Decimal `json:"perJobAmount" gorm:"type:decimal(9,2);not null"`           // 職位
	xmodel.Model
}

func (Allowance) TableName() string {
	return "allowance"
}

func (Allowance) SwaggerDescription() string {
	return "津貼"
}
