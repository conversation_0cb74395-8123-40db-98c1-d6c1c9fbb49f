package routers

import (
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

func TestSystemRoleActionList(t *testing.T) {
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/roles/actions/action-list",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "搜索",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢-超級管理員",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.RoleActionListReq{
					RoleId: 1,
				},
			},
			{
				SubName:           "正常查詢-管理員",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.RoleActionListReq{
					RoleId: 2,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityRoleActionList(t *testing.T) {
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/roles/actions/action-list",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "搜索",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢-系統",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.RoleActionListReq{
					RoleId: 3,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSelectRoleActions(t *testing.T) {
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/roles/actions/select-action",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "選擇角色功能",
		Cases: []xtest.TestCase{
			{
				SubName:           "選擇權限-系統",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.RoleActionSelectReq{
					RoleId:  1,
					System:  model.MenuSystemSystem,
					Actions: []uint64{3, 4, 5, 6},
				},
			},
			{
				SubName:           "選擇權限-系統",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.RoleActionSelectReq{
					RoleId:  2,
					System:  model.MenuSystemSystem,
					Actions: []uint64{3, 4, 5, 6},
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
