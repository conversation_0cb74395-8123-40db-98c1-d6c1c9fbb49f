package facility_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type AllowanceController struct {
	v1.CommonController
}

func NewAllowanceController() AllowanceController {
	return AllowanceController{}
}

// @Tags Allowance
// @Summary 新增津貼
// @Description
// @Router /v1/facility/allowances/actions/create [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.AllowanceCreateReq true "parameter"
// @Success 200 {object} services.AllowanceCreateResp "Success"
func (con AllowanceController) Create(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.AllowanceCreateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		tx := db.Begin()
		resp, err := services.AllowanceService.Create(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Allowance
// @Summary 獲取津貼列表
// @Description
// @Router /v1/facility/allowances [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.AllowanceListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Success 200 {object} []services.AllowanceListResp "Success"
func (con AllowanceController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.AllowanceListReq
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.AllowanceService.List(db, req, &pageSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Allowance
// @Summary 搜索津貼
// @Description
// @Router /v1/facility/allowances/actions/search [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.AllowanceSearchReq true "parameter"
// @Success 200 {object} []services.AllowanceSearchResp "Success"
func (con AllowanceController) Search(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.AllowanceSearchReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.AllowanceService.Search(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Allowance
// @Summary 修改津貼
// @Description
// @Router /v1/facility/allowances/actions/edit [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.AllowanceEditReq true "parameter"
// @Success 200 "Success"
func (con AllowanceController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.AllowanceEditReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var allowance model.Allowance
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.AllowanceService.CheckIdExist(db, &allowance, req.AllowanceId, req.FacilityId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.AllowanceService.Edit(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Allowance
// @Summary 查詢津貼
// @Description
// @Router /v1/facility/allowances/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.AllowanceInquireReq true "parameter"
// @Success 200 {object} services.AllowanceInquireResp "Success"
func (con AllowanceController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.AllowanceInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}
		var allowance model.Allowance
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.AllowanceService.CheckIdExist(db, &allowance, req.AllowanceId, req.FacilityId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		resp, err := services.AllowanceService.Inquire(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Allowance
// @Summary 刪除津貼
// @Description
// @Router /v1/facility/allowances/actions/delete [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.AllowanceDeleteReq true "parameter"
// @Success 200 "Success"
func (con AllowanceController) Delete(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.AllowanceDeleteReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var checkMsg []string
		var allowance model.Allowance
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.AllowanceService.CheckIdExist(db, &allowance, req.AllowanceId, req.FacilityId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.AllowanceService.Delete(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}
