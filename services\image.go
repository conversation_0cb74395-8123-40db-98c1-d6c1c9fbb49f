package services

import (
	"bytes"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strings"
	// uuid "github.com/satori/go.uuid"
	// "gopkg.in/gographics/imagick.v3/imagick"
)

type imageService struct{}

var ImageService = new(imageService)

type ConvertToThumbnailResp struct {
	Size              uint32
	ThumbnailBody     *bytes.Reader
	ThumbnailUuidName string
}

func (s *imageService) ConvertToThumbnail(reader multipart.File, fileName string, uuidStr string) (ConvertToThumbnailResp, error) {
	var err error
	var resp ConvertToThumbnailResp
	ext := strings.ToLower(path.Ext(fileName))

	thumbnailUuidName := uuidStr + "_thumb.jpg"
	thumbnailSavePath := s.GetLocalFilePath("file-cache/" + thumbnailUuidName)

	// 讀取文件
	fileData, err := io.ReadAll(reader)
	if err != nil {
		return resp, err
	}
	mimeType := http.DetectContentType(fileData)
	_, err = reader.Seek(0, io.SeekStart)
	if err != nil {
		return resp, err
	}

	if ext == ".pdf" {
		// 先將第一頁 pdf 轉換成圖片
		// mw := imagick.NewMagickWand()
		// defer s.clearImagickWand(mw)
		// // Must be *before* ReadImageFile
		// // Make sure our image is high quality
		// if err = mw.SetResolution(200, 200); err != nil {
		// 	return resp, err
		// }
		// // Load the image file into imagick
		// if err = mw.ReadImageBlob(fileData); err != nil {
		// 	return resp, err
		// }
		// // Set any compression (100 = max quality)
		// if err = mw.SetCompressionQuality(100); err != nil {
		// 	return resp, err
		// }
		// var firstPageFilePath string
		// // 只處理第一頁
		// for i := uint(0); i < 1; i++ {
		// 	mw.SetIteratorIndex(int(i))
		// 	// remove alpha channel, to prevent alpha turning black in jpg
		// 	if err = mw.SetImageAlphaChannel(imagick.ALPHA_CHANNEL_REMOVE); err != nil {
		// 		return resp, err
		// 	}
		// 	// Convert into JPG
		// 	if err = mw.SetFormat("jpg"); err != nil {
		// 		return resp, err
		// 	}
		// 	fileExt := ".jpg"
		// 	cacheUuidStr := uuid.NewV4().String()
		// 	// 使用 os.MkdirAll 創建文件夾，0775 是目錄的許可權
		// 	dirPath := "file-cache"
		// 	err = os.MkdirAll(s.GetLocalFilePath(dirPath), 0775)
		// 	if err != nil {
		// 		return resp, err
		// 	}
		// 	firstPageFilePath = s.GetLocalFilePath(path.Join(dirPath, cacheUuidStr+fileExt))
		// 	imgFile, err := os.Create(firstPageFilePath)
		// 	if err != nil {
		// 		return resp, err
		// 	}
		// 	if err = mw.WriteImageFile(imgFile); err != nil {
		// 		return resp, err
		// 	}
		// }
		// defer func(name string) {
		// 	_ = os.Remove(name)
		// }(firstPageFilePath)
		// // 構建縮略圖，打開第一頁圖片
		// readerFirstPage, err := os.OpenFile(firstPageFilePath, os.O_RDONLY, 0)
		// if err != nil {
		// 	return resp, err
		// }
		// // 構建縮略圖
		// err = MakeThumbnail(readerFirstPage, thumbnailSavePath, mimeType)
		// defer func(name string) {
		// 	_ = os.Remove(name)
		// }(thumbnailSavePath)
		// if err != nil {
		// 	return resp, err
		// }
		return resp, nil
	} else {
		// 構建縮略圖
		err = MakeThumbnail(reader.(io.Reader), thumbnailSavePath, mimeType)
		defer func(name string) {
			_ = os.Remove(name)
		}(thumbnailSavePath)
		if err != nil {
			return resp, err
		}
	}
	// 讀取縮略圖文件
	readerThumbnail, err := os.OpenFile(thumbnailSavePath, os.O_RDONLY, 0)
	defer func(readerThumbnail *os.File) {
		_ = readerThumbnail.Close()
	}(readerThumbnail)
	if err != nil {
		return resp, err
	}
	thumbnailFile, err := readerThumbnail.Stat()
	if err != nil {
		return resp, err
	}
	thumbnailBytes, err := io.ReadAll(readerThumbnail)
	if err != nil {
		return resp, err
	}

	resp.ThumbnailBody = bytes.NewReader(thumbnailBytes)
	resp.Size = uint32(thumbnailFile.Size())
	resp.ThumbnailUuidName = thumbnailUuidName
	return resp, nil
}

// func (s *imageService) clearImagickWand(mw *imagick.MagickWand) {
// 	_ = mw.RemoveImage()
// 	mw.Clear()
// 	mw.Destroy()
// }

func (s *imageService) GetLocalFilePath(path string) string {
	// HACK: 這裡是為了解決在 routers 目錄下執行 go test 時找不到文件的問題
	if s.IsRouterTest() {
		path = "../" + path
	}
	if s.IsTaskTest() {
		path = "../../" + path
	}
	return path
}

func (s *imageService) IsRouterTest() bool {
	dir, _ := os.Getwd()
	return filepath.Base(dir) == "routers"
}

func (s *imageService) IsTaskTest() bool {
	dir, _ := os.Getwd()
	return filepath.Base(dir) == "task"
}
