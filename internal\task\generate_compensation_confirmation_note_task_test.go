package task

import (
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xredis"
	"testing"
)

func TestGenerateCompensationConfirmationNote(t *testing.T) {
	xconfig.Setup("../../config/app.ini")
	xredis.DefaultSetup()
	xgorm.DefaultSetup()
	_, err := GenerateCompensationConfirmationNote("{\"jobShiftId\": 0,\"jobApplicationId\": 0,\"jobShiftId\": 2}")
	if err != nil {
		t.Error(err)
		return
	}
}
