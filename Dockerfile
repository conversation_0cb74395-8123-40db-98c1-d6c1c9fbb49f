FROM norrayjm/xgolang-1.23:0.0.1-dev AS build-env

ARG GITHUB_USER
ARG ACCESS_TOKEN
RUN go env -w GOPRIVATE=github.com/Norray
RUN git config --global url."https://${GITHUB_USER}:${ACCESS_TOKEN}@github.com".insteadOf "https://github.com"

# 設置時區為澳洲悉尼
ENV TZ=Australia/Sydney
RUN ln -sf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

WORKDIR $GOPATH/src/github.com/Norray/medic-crew
COPY . $GOPATH/src/github.com/Norray/medic-crew
RUN go install github.com/swaggo/swag/cmd/swag@v1.7.8
RUN swag init --parseDependency --parseDepth 2
RUN go build .

FROM norrayjm/xgolang-1.23:0.0.1-dev

# 設置時區為澳洲悉尼
ENV TZ=Australia/Sydney
RUN ln -sf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

RUN addgroup --system appgroup && adduser --system --ingroup appgroup appuser
WORKDIR /app

COPY --from=build-env /go/src/github.com/Norray/medic-crew/medic-crew /app/
COPY --from=build-env /go/src/github.com/Norray/medic-crew/docs/swagger.json /app/docs/
COPY --from=build-env /go/src/github.com/Norray/medic-crew/docs/swagger.yaml /app/docs/
COPY --from=build-env /go/src/github.com/Norray/medic-crew/config/rbac_model.conf /app/config/
COPY --from=build-env /go/src/github.com/Norray/medic-crew/resource/geoip/GeoLite2-City.mmdb /app/resource/geoip/
COPY --from=build-env /go/src/github.com/Norray/medic-crew/resource/i18n/active.en.toml /app/resource/i18n/
COPY --from=build-env /go/src/github.com/Norray/medic-crew/resource/i18n/active.zh-CN.toml /app/resource/i18n/
COPY --from=build-env /go/src/github.com/Norray/medic-crew/resource/i18n/active.zh-HK.toml /app/resource/i18n/
COPY --from=build-env /go/src/github.com/Norray/medic-crew/resource/pdf/quill.core.css /app/resource/pdf/
COPY --from=build-env /go/src/github.com/Norray/medic-crew/resource/pdf/platform_signature.png /app/resource/pdf/
COPY --from=build-env /go/src/github.com/Norray/medic-crew/resource/pdf/logo.jpg /app/resource/pdf/
COPY --from=build-env /go/src/github.com/Norray/medic-crew/resource/font/Montserrat-Bold.ttf /app/resource/font/
COPY --from=build-env /go/src/github.com/Norray/medic-crew/resource/font/Montserrat-Regular.ttf /app/resource/font/
RUN echo -n $(date +"%Y-%m-%d %H:%M:%S") > /app/resource/compile_time.txt
RUN chown -R appuser:appgroup /app
# 切换到非 root 用户
USER appuser
ENTRYPOINT ["./medic-crew"]
