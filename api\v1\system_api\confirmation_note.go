package system_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type ConfirmationNoteController struct {
	v1.CommonController
}

func NewConfirmationNoteController() ConfirmationNoteController {
	return ConfirmationNoteController{}
}

// @Tags Confirmation Note
// @Summary 查詢機構確認通知單詳情
// @Description
// @Router /v1/system/confirmation-notes/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.ConfirmationNoteInquireReq true "parameter"
// @Success 200 {object} services.ConfirmationNoteInquireResp "Success"
func (con ConfirmationNoteController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ConfirmationNoteInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var document model.Document
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.DocumentService.CheckIdExist(db, &document, req.DocumentId)
			})
		msg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(msg) > 0 {
			nc.BadRequestResponseWithCheckMsg(msg)
			return
		}

		resp, err := services.ConfirmationNoteService.SystemInquire(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
