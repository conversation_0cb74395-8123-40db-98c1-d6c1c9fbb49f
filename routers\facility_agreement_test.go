package routers

import (
	"encoding/json"
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

var testFacilityAgreementId uint64

func TestFacilityAgreementList(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/facility-agreements",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.FacilityAgreementListReq{},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityAgreementInquire(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/facility-agreements/actions/inquire",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityAgreementInquireReq{
					FacilityAgreementId: testFacilityAgreementId,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityAgreementCreate(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/facility-agreements/actions/create",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "新增",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.FacilityAgreementCreateReq{},
				CheckResultHandler: func(resp interface{}) bool {
					response := resp.(xresp.Response)
					var data services.FacilityAgreementCreateResp
					dataByte, _ := json.Marshal(response.Data)
					_ = json.Unmarshal(dataByte, &data)
					testFacilityAgreementId = data.FacilityAgreementId
					return true
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityAgreementEdit(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/facility-agreements/actions/edit",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "修改",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityAgreementEditReq{
					FacilityAgreementId: testFacilityAgreementId,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityAgreementSendAgreement(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/facility-agreements/actions/send-agreement",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "發送協議",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.SendAgreementReq{
					FacilityAgreementId: testFacilityAgreementId,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityAgreementDownload(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/facility-agreements/actions/download",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "下載",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityAgreementDownloadReq{
					FacilityAgreementId: 2,
				},
				CheckResultFileHandler: xtest.SaveFileWithHandler("agreement.pdf", true),
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityAgreementDownloadByFacilityProfile(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/facility-agreements/actions/download",
		UserId:           16,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "下載",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityAgreementDownloadByFacilityProfileReq{
					FacilityProfileId: 2,
				},
				CheckResultFileHandler: xtest.SaveFileWithHandler("agreement.pdf", true),
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityAgreementSearch(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/facility-agreements/actions/search",
		UserId:           16,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityAgreementSearchByFacilityProfileReq{
					FacilityId: 7,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityAgreementListByFacility(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/facility-agreements",
		UserId:           16,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityAgreementListReqByFacility{
					FacilityId: 7,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
