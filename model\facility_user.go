package model

import "github.com/Norray/xrocket/xmodel"

const (
	FacilityUserPrimaryUserY = "Y" // 主賬號
	FacilityUserPrimaryUserN = "N" // 子賬號
)

// 機構用戶
type FacilityUser struct {
	Id          uint64 `json:"id" gorm:"primary_key"`
	FacilityId  uint64 `json:"facilityId" gorm:"index:facility_idx;not null"` // 機構ID
	UserId      uint64 `json:"userId" gorm:"unique;not null"`                 // 用戶ID,一個用戶只能有一個機構
	PrimaryUser string `json:"primaryUser" gorm:"type:varchar(1);not null"`   // 主賬號 Y/N
	xmodel.Model
}

func (FacilityUser) TableName() string {
	return "facility_user"
}

func (FacilityUser) SwaggerDescription() string {
	return "機構用戶"
}
