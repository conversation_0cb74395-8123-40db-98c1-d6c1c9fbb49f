package model

import (
	"github.com/Norray/xrocket/xmodel"
)

const (
	FacilityDeactivatedY = "Y" // 停用
	FacilityDeactivatedN = "N" // 啟用
)

// 機構
type Facility struct {
	Id                      uint64 `json:"id" gorm:"primary_key"`
	BreakTimeDurationMinute int32  `json:"breakTimeDurationMinute" gorm:"not null"` // 休息時長分鐘
	BreakTimeEveryHour      int32  `json:"breakTimeEveryHour" gorm:"not null"`      // 每隔多少小時有休息時長
	Deactivated             string `json:"deactivated" gorm:"type:varchar(1)"`      // 停用 Y = 已停用 N = 未停用
	xmodel.Model
}

func (Facility) TableName() string {
	return "facility"
}

func (Facility) SwaggerDescription() string {
	return "機構"
}
