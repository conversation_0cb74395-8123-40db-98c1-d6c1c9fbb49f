package model

import (
	"encoding/json"
	"time"

	"github.com/Norray/xrocket/xmodel"
)

const (
	TrainingProgressStatusWatching  = "WATCHING"  // 觀看中
	TrainingProgressStatusAnswering = "ANSWERING" // 答題中
	TrainingProgressStatusCompleted = "COMPLETED" // 已完成
)

// 用戶培訓答案結構
type ProfessionalTrainingAnswer struct {
	QuestionSeq    int32  `json:"questionSeq"`    // 對應 TrainingModule.DetailJson.questions[i].Seq
	SelectedOption int32  `json:"selectedOption"` // 對應 options[i].Seq
	IsCorrect      string `json:"isCorrect"`      // Y/N
	AnswerTime     string `json:"answerTime"`     // 答題時間
}

// 用戶培訓進度
type ProfessionalTrainingProgress struct {
	Id                 uint64     `json:"id" gorm:"primary_key"`
	UserId             uint64     `json:"userId" gorm:"uniqueIndex:uniq_user_module;not null"`                               // 用戶ID
	TrainingModuleId   uint64     `json:"trainingModuleId" gorm:"uniqueIndex:uniq_user_module;not null"`                     // 培訓模塊ID
	VideoWatchComplete string     `json:"videoWatchComplete" gorm:"index:video_watch_complete_idx;type:varchar(1);not null"` // 視頻是否觀看完成 Y/N
	QuestionComplete   string     `json:"questionComplete" gorm:"index:question_complete_idx;type:varchar(1);not null"`      // 問題是否全部回答正確 Y/N
	AnswersJson        string     `json:"answersJson" gorm:"type:mediumtext;not null"`                                       // 答案記錄JSON (存儲ProfessionalTrainingAnswer數組)
	Status             string     `json:"status" gorm:"index:status_idx;type:varchar(32);not null"`                          // 進度狀態 WATCHING ANSWERING COMPLETED
	StartTime          *time.Time `json:"startTime" gorm:"type:datetime(0);not null"`                                        // 開始時間
	CompleteTime       *time.Time `json:"completeTime" gorm:"type:datetime(0);not null"`                                     // 完成時間
	xmodel.Model
}

func (ProfessionalTrainingProgress) TableName() string {
	return "professional_training_progress"
}

func (ProfessionalTrainingProgress) SwaggerDescription() string {
	return "用戶培訓進度"
}

// UnmarshalAnswers 解析答案JSON
func (p *ProfessionalTrainingProgress) UnmarshalAnswers() ([]ProfessionalTrainingAnswer, error) {
	var answers []ProfessionalTrainingAnswer
	err := json.Unmarshal([]byte(p.AnswersJson), &answers)
	return answers, err
}

// MarshalAnswers 序列化答案為JSON
func (p *ProfessionalTrainingProgress) MarshalAnswers(answers []ProfessionalTrainingAnswer) error {
	answersJson, err := json.Marshal(answers)
	if err != nil {
		return err
	}
	p.AnswersJson = string(answersJson)
	return nil
}
