package routers

import (
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

func TestProfessionalWsMessageSessions(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/professional/ws/sessions",
		UserId:           15, // 假設這是一個專業人士用戶ID
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "專業人士獲取聊天會話列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.WsSessionListReq{},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalWsMessageMessageList(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/professional/ws/messages",
		UserId:           15, // 假設這是一個專業人士用戶ID
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "專業人士獲取聊天會話列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.WsMessageListReq{
					SessionUuid: "6b4563f7-44c3-44ba-98e1-7046c397c566",
					Limit:       10,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalWsMessageUnreadCount(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/professional/ws/actions/unread-count",
		UserId:           15, // 假設這是一個專業人士用戶ID
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "專業人士獲取未讀消息數量",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.UnreadCountReq{},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityWsMessageSessions(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/facility/ws/sessions",
		UserId:           16, // 假設這是一個機構用戶ID
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "機構獲取聊天會話列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.WsSessionListReq{},
				PageSet: xresp.PageSet{
					PageIndex: 1,
					PageSize:  10,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityWsMessageUnreadCount(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/facility/ws/actions/unread-count",
		UserId:           16, // 假設這是一個機構用戶ID
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "機構獲取未讀消息數量",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
			},
		},
	}
	xtest.RunTests(t, test)
}
