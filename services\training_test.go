package services

import (
	"encoding/json"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
)

// TestCreateInitialTrainingModules 創建初始培訓模塊數據
func TestCreateInitialTrainingModules(t *testing.T) {
	err := os.Chdir("..")
	if err != nil {
		t.Fatalf("Failed to change directory: %v", err)
		return
	}
	xconfig.Setup("./config/app.ini")
	xgorm.DefaultSetup()

	// 檢查是否已經存在培訓模塊
	var existingCount int64
	xgorm.DB.Model(&model.TrainingModule{}).Count(&existingCount)
	if existingCount > 0 {
		fmt.Printf("已存在 %d 個培訓模塊，跳過創建\n", existingCount)
		return
	}

	// 準備培訓模塊數據（基於 training.MD 文件）
	modules := []struct {
		Title       string
		Description string
		Seq         int32
		Video       model.TrainingModuleVideo
		Questions   []model.TrainingQuestion
	}{
		{
			Title:       "Module 1",
			Description: "Chain of Infection",
			Seq:         1,
			Video: model.TrainingModuleVideo{
				Title:        "",
				Description:  "",
				Url:          "",
				Duration:     0,
				ThumbnailUrl: "",
			},
			Questions: []model.TrainingQuestion{
				{
					QuestionText: "What is the main goal of breaking the chain of infection?",
					QuestionType: model.TrainingQuestionTypeSingleChoice,
					Seq:          1,
					Options: []model.TrainingQuestionOption{
						{OptionText: "To delay symptoms of illness", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 1},
						{OptionText: "To prevent germs from entering the body", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 2},
						{OptionText: "To stop the spread of infection from one person to another", IsCorrect: model.TrainingAnswerIsCorrectY, Seq: 3},
						{OptionText: "To avoid having to wear protective equipment", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 4},
					},
				},
				{
					QuestionText: "Which of the following is NOT one of the recommended everyday actions to help prevent infection?",
					QuestionType: model.TrainingQuestionTypeSingleChoice,
					Seq:          2,
					Options: []model.TrainingQuestionOption{
						{OptionText: "Staying home when sick", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 1},
						{OptionText: "Covering coughs and sneezes", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 2},
						{OptionText: "Taking antibiotics regularly", IsCorrect: model.TrainingAnswerIsCorrectY, Seq: 3},
						{OptionText: "Practising good hand hygiene", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 4},
					},
				},
				{
					QuestionText: "In which situation might standard hygiene practices not be enough to prevent infection?",
					QuestionType: model.TrainingQuestionTypeSingleChoice,
					Seq:          3,
					Options: []model.TrainingQuestionOption{
						{OptionText: "When working in aged care during an outbreak", IsCorrect: model.TrainingAnswerIsCorrectY, Seq: 1},
						{OptionText: "After using a tissue", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 2},
						{OptionText: "While washing your hands before a meal", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 3},
						{OptionText: "After covering a cough", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 4},
					},
				},
			},
		},
		{
			Title:       "Module 2",
			Description: "Hand Hygiene",
			Seq:         2,
			Video: model.TrainingModuleVideo{
				Title:        "",
				Description:  "",
				Url:          "",
				Duration:     0,
				ThumbnailUrl: "",
			},
			Questions: []model.TrainingQuestion{
				{
					QuestionText: "When is it best to wash your hands with soap and water instead of using hand rub?",
					QuestionType: model.TrainingQuestionTypeSingleChoice,
					Seq:          1,
					Options: []model.TrainingQuestionOption{
						{OptionText: "When you're preparing food", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 1},
						{OptionText: "When your hands are visibly dirty or after using the toilet", IsCorrect: model.TrainingAnswerIsCorrectY, Seq: 2},
						{OptionText: "After shaking someone's hand", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 3},
						{OptionText: "After using a mobile phone", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 4},
					},
				},
				{
					QuestionText: "What is the recommended alcohol content for effective hand sanitiser?",
					QuestionType: model.TrainingQuestionTypeSingleChoice,
					Seq:          2,
					Options: []model.TrainingQuestionOption{
						{OptionText: "Less than 50%", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 1},
						{OptionText: "Exactly 90%", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 2},
						{OptionText: "Between 60% and 80%", IsCorrect: model.TrainingAnswerIsCorrectY, Seq: 3},
						{OptionText: "More than 85%", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 4},
					},
				},
				{
					QuestionText: "Which of the following is NOT recommended when practising hand hygiene?",
					QuestionType: model.TrainingQuestionTypeSingleChoice,
					Seq:          3,
					Options: []model.TrainingQuestionOption{
						{OptionText: "Wearing artificial nails", IsCorrect: model.TrainingAnswerIsCorrectY, Seq: 1},
						{OptionText: "Using hand sanitiser with 70% alcohol", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 2},
						{OptionText: "Moisturising your hands", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 3},
						{OptionText: "Removing jewellery before hand hygiene", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 4},
					},
				},
			},
		},
		{
			Title:       "Module 3",
			Description: "Personal Protective Equipment",
			Seq:         3,
			Video: model.TrainingModuleVideo{
				Title:        "",
				Description:  "",
				Url:          "",
				Duration:     0,
				ThumbnailUrl: "",
			},
			Questions: []model.TrainingQuestion{
				{
					QuestionText: "What is the most important reason to perform a fit check each time you wear an N95 or P2 respirator?",
					QuestionType: model.TrainingQuestionTypeSingleChoice,
					Seq:          1,
					Options: []model.TrainingQuestionOption{
						{OptionText: "To make sure the straps are comfortable", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 1},
						{OptionText: "To adjust the nose wire correctly", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 2},
						{OptionText: "To ensure the seal is tight against your face for proper protection", IsCorrect: model.TrainingAnswerIsCorrectY, Seq: 3},
						{OptionText: "To reuse the same mask multiple times", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 4},
					},
				},
				{
					QuestionText: "When should you change your surgical mask?",
					QuestionType: model.TrainingQuestionTypeSingleChoice,
					Seq:          2,
					Options: []model.TrainingQuestionOption{
						{OptionText: "Only when you finish your shift", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 1},
						{OptionText: "Every four hours or when it becomes wet or dirty", IsCorrect: model.TrainingAnswerIsCorrectY, Seq: 2},
						{OptionText: "Once a week", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 3},
						{OptionText: "Only after eating or drinking", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 4},
					},
				},
				{
					QuestionText: "Which of the following actions is NOT recommended when using PPE?",
					QuestionType: model.TrainingQuestionTypeSingleChoice,
					Seq:          3,
					Options: []model.TrainingQuestionOption{
						{OptionText: "Performing hand hygiene before and after removing PPE", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 1},
						{OptionText: "Wearing gloves when handling contaminated waste", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 2},
						{OptionText: "Wearing a mask on your chin while on break", IsCorrect: model.TrainingAnswerIsCorrectY, Seq: 3},
						{OptionText: "Changing gloves between each patient", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 4},
					},
				},
			},
		},
		{
			Title:       "Module 4",
			Description: "Cleaning",
			Seq:         4,
			Video: model.TrainingModuleVideo{
				Title:        "",
				Description:  "",
				Url:          "",
				Duration:     0,
				ThumbnailUrl: "",
			},
			Questions: []model.TrainingQuestion{
				{
					QuestionText: "What is the primary purpose of cleaning in infection prevention?",
					QuestionType: model.TrainingQuestionTypeSingleChoice,
					Seq:          1,
					Options: []model.TrainingQuestionOption{
						{OptionText: "To make surfaces smell better", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 1},
						{OptionText: "To remove visible stains and make surfaces look clean", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 2},
						{OptionText: "To physically remove dirt and germs, reducing the risk of infection", IsCorrect: model.TrainingAnswerIsCorrectY, Seq: 3},
						{OptionText: "To avoid having to use disinfectants", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 4},
					},
				},
				{
					QuestionText: "Why must a surface be cleaned before applying disinfectant?",
					QuestionType: model.TrainingQuestionTypeSingleChoice,
					Seq:          2,
					Options: []model.TrainingQuestionOption{
						{OptionText: "So the surface will dry faster", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 1},
						{OptionText: "To make it easier to see where the disinfectant is applied", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 2},
						{OptionText: "Because dirt can stop the disinfectant from working effectively", IsCorrect: model.TrainingAnswerIsCorrectY, Seq: 3},
						{OptionText: "So the disinfectant lasts longer on the surface", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 4},
					},
				},
				{
					QuestionText: "When cleaning, what is the correct direction to wipe surfaces to prevent spreading germs?",
					QuestionType: model.TrainingQuestionTypeSingleChoice,
					Seq:          3,
					Options: []model.TrainingQuestionOption{
						{OptionText: "In circles", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 1},
						{OptionText: "In a zigzag pattern", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 2},
						{OptionText: "Up and down or side to side in one direction", IsCorrect: model.TrainingAnswerIsCorrectY, Seq: 3},
						{OptionText: "Randomly, depending on the surface", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 4},
					},
				},
			},
		},
		{
			Title:       "Module 5",
			Description: "Waste Management",
			Seq:         5,
			Video: model.TrainingModuleVideo{
				Title:        "",
				Description:  "",
				Url:          "",
				Duration:     0,
				ThumbnailUrl: "",
			},
			Questions: []model.TrainingQuestion{
				{
					QuestionText: "Why is it important to handle clinical waste carefully and follow correct procedures?",
					QuestionType: model.TrainingQuestionTypeSingleChoice,
					Seq:          1,
					Options: []model.TrainingQuestionOption{
						{OptionText: "To avoid mixing recyclable materials with general waste", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 1},
						{OptionText: "To keep the workplace looking clean", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 2},
						{OptionText: "To prevent the spread of disease and reduce the risk of injury", IsCorrect: model.TrainingAnswerIsCorrectY, Seq: 3},
						{OptionText: "To reduce the cost of waste disposal", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 4},
					},
				},
				{
					QuestionText: "How full should a sharps or clinical waste container be before it is replaced?",
					QuestionType: model.TrainingQuestionTypeSingleChoice,
					Seq:          2,
					Options: []model.TrainingQuestionOption{
						{OptionText: "Completely full, to reduce the number of bins used", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 1},
						{OptionText: "About half full, so it's easier to carry", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 2},
						{OptionText: "Up to the top, then tied tightly", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 3},
						{OptionText: "No more than three-quarters full", IsCorrect: model.TrainingAnswerIsCorrectY, Seq: 4},
					},
				},
				{
					QuestionText: "What should you do immediately after disposing of clinical waste?",
					QuestionType: model.TrainingQuestionTypeSingleChoice,
					Seq:          3,
					Options: []model.TrainingQuestionOption{
						{OptionText: "Take off your PPE and continue working", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 1},
						{OptionText: "Wash your hands and then remove PPE", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 2},
						{OptionText: "Remove your PPE carefully and perform hand hygiene", IsCorrect: model.TrainingAnswerIsCorrectY, Seq: 3},
						{OptionText: "Leave the waste bag near the door for collection", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 4},
					},
				},
			},
		},
		{
			Title:       "Module 6",
			Description: "Managing Outbreaks",
			Seq:         6,
			Video: model.TrainingModuleVideo{
				Title:        "",
				Description:  "",
				Url:          "",
				Duration:     0,
				ThumbnailUrl: "",
			},
			Questions: []model.TrainingQuestion{
				{
					QuestionText: "What is the first step taken when an outbreak is suspected in a workplace?",
					QuestionType: model.TrainingQuestionTypeSingleChoice,
					Seq:          1,
					Options: []model.TrainingQuestionOption{
						{OptionText: "Hand out PPE to all staff", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 1},
						{OptionText: "Immediately close the facility", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 2},
						{OptionText: "Confirm the outbreak and identify affected and at-risk individuals", IsCorrect: model.TrainingAnswerIsCorrectY, Seq: 3},
						{OptionText: "Call an engineer to improve ventilation", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 4},
					},
				},
				{
					QuestionText: "Which of the following is considered the most effective control in the hierarchy of controls?",
					QuestionType: model.TrainingQuestionTypeSingleChoice,
					Seq:          2,
					Options: []model.TrainingQuestionOption{
						{OptionText: "Administrative changes like training and cleaning schedules", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 1},
						{OptionText: "Using personal protective equipment", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 2},
						{OptionText: "Substitution of high-risk equipment", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 3},
						{OptionText: "Elimination, such as staying home when sick", IsCorrect: model.TrainingAnswerIsCorrectY, Seq: 4},
					},
				},
				{
					QuestionText: "What is cohorting in the context of outbreak management?",
					QuestionType: model.TrainingQuestionTypeSingleChoice,
					Seq:          3,
					Options: []model.TrainingQuestionOption{
						{OptionText: "Asking all staff to wear the same PPE", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 1},
						{OptionText: "Placing infected individuals together and keeping staff in designated teams", IsCorrect: model.TrainingAnswerIsCorrectY, Seq: 2},
						{OptionText: "Sending all patients home", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 3},
						{OptionText: "Reducing ventilation to stop the spread of airborne illness", IsCorrect: model.TrainingAnswerIsCorrectN, Seq: 4},
					},
				},
			},
		},
	}

	// 開始數據庫事務
	tx := xgorm.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			t.Fatalf("Transaction failed: %v", r)
		}
	}()

	successCount := 0
	failCount := 0

	// 創建培訓模塊
	for i, moduleData := range modules {
		fmt.Printf("正在創建模塊 %d: %s\n", i+1, moduleData.Title)

		// 創建模塊詳情
		detail := model.TrainingModuleDetail{
			Video:     moduleData.Video,
			Questions: moduleData.Questions,
		}

		// 創建培訓模塊
		module := model.TrainingModule{
			Title:       moduleData.Title,
			Description: moduleData.Description,
			Seq:         moduleData.Seq,
			Status:      model.TrainingModuleStatusActive,
		}

		// 序列化詳情為JSON
		err := module.Marshal(detail)
		if err != nil {
			fmt.Printf("序列化模塊詳情失敗: %v\n", err)
			failCount++
			continue
		}

		// 保存到數據庫
		if err := tx.Create(&module).Error; err != nil {
			fmt.Printf("保存模塊失敗: %v\n", err)
			failCount++
			continue
		}

		fmt.Printf("成功創建模塊: %s (ID: %d)\n", module.Title, module.Id)
		successCount++
	}

	// 提交事務
	if err := tx.Commit().Error; err != nil {
		t.Fatalf("提交事務失敗: %v", err)
		return
	}

	// 輸出結果
	fmt.Printf("\n=== 培訓模塊創建結果 ===\n")
	fmt.Printf("成功創建: %d 個模塊\n", successCount)
	fmt.Printf("創建失敗: %d 個模塊\n", failCount)
	fmt.Printf("總計: %d 個模塊\n", len(modules))

	if failCount > 0 {
		t.Errorf("有 %d 個模塊創建失敗", failCount)
	} else {
		fmt.Println("所有培訓模塊創建成功！")
	}
}

// TestCleanTrainingModules 清理培訓模塊數據（測試用）
func TestCleanTrainingModules(t *testing.T) {
	err := os.Chdir("..")
	if err != nil {
		t.Fatalf("Failed to change directory: %v", err)
		return
	}
	xconfig.Setup("./config/app.ini")
	xgorm.DefaultSetup()

	// 刪除所有培訓進度記錄
	var progressCount int64
	if err := xgorm.DB.Model(&model.ProfessionalTrainingProgress{}).Count(&progressCount).Error; err == nil && progressCount > 0 {
		if err := xgorm.DB.Exec("DELETE FROM professional_training_progress").Error; err != nil {
			t.Errorf("刪除培訓進度記錄失敗: %v", err)
		} else {
			fmt.Printf("已刪除 %d 條培訓進度記錄\n", progressCount)
		}
	}

	// 刪除所有培訓模塊
	var moduleCount int64
	if err := xgorm.DB.Model(&model.TrainingModule{}).Count(&moduleCount).Error; err == nil && moduleCount > 0 {
		if err := xgorm.DB.Exec("DELETE FROM training_module").Error; err != nil {
			t.Errorf("刪除培訓模塊失敗: %v", err)
		} else {
			fmt.Printf("已刪除 %d 個培訓模塊\n", moduleCount)
		}
	}

	fmt.Println("培訓數據清理完成")
}

// TestVerifyTrainingModules 驗證培訓模塊數據完整性
func TestVerifyTrainingModules(t *testing.T) {
	err := os.Chdir("..")
	if err != nil {
		t.Fatalf("Failed to change directory: %v", err)
		return
	}
	xconfig.Setup("./config/app.ini")
	xgorm.DefaultSetup()

	var modules []model.TrainingModule
	if err := xgorm.DB.Where("status = ?", model.TrainingModuleStatusActive).
		Order("seq ASC").Find(&modules).Error; err != nil {
		t.Fatalf("查詢培訓模塊失敗: %v", err)
		return
	}

	fmt.Printf("=== 培訓模塊驗證報告 ===\n")
	fmt.Printf("找到 %d 個有效的培訓模塊\n\n", len(modules))

	for i, module := range modules {
		fmt.Printf("模塊 %d:\n", i+1)
		fmt.Printf("  ID: %d\n", module.Id)
		fmt.Printf("  標題: %s\n", module.Title)
		fmt.Printf("  描述: %s\n", module.Description)
		fmt.Printf("  序號: %d\n", module.Seq)
		fmt.Printf("  狀態: %s\n", module.Status)
		fmt.Printf("  創建時間: %s\n", time.Unix(module.CreatedAt, 0).Format(xtool.DateTimeSecA1))

		// 解析模塊詳情
		var detail model.TrainingModuleDetail
		if err := unmarshalModuleDetail(&module, &detail); err != nil {
			fmt.Printf("  ❌ 解析模塊詳情失敗: %v\n", err)
			continue
		}

		fmt.Printf("  視頻標題: %s\n", detail.Video.Title)
		fmt.Printf("  視頻時長: %d 秒\n", detail.Video.Duration)
		fmt.Printf("  問題數量: %d\n", len(detail.Questions))

		// 驗證問題
		for j, question := range detail.Questions {
			fmt.Printf("    問題 %d: %s\n", j+1, question.QuestionText)
			fmt.Printf("    選項數量: %d\n", len(question.Options))

			// 檢查是否有正確答案
			hasCorrect := false
			for _, option := range question.Options {
				if option.IsCorrect == "Y" {
					hasCorrect = true
					break
				}
			}
			if !hasCorrect {
				fmt.Printf("    ⚠️  警告: 問題沒有正確答案\n")
			}
		}
		fmt.Println()
	}

	fmt.Println("驗證完成")
}

// unmarshalModuleDetail 解析模塊詳情的輔助函數
func unmarshalModuleDetail(module *model.TrainingModule, detail *model.TrainingModuleDetail) error {
	return json.Unmarshal([]byte(module.DetailJson), detail)
}
