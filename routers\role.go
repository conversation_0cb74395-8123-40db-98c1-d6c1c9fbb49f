package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func roleSystemRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/system/").Use(handlers...)
	{
		controller := system_api.NewSystemRoleController()
		r.GET("/roles", controller.List)
		r.GET("/roles/actions/search", controller.Search)
		r.GET("/roles/actions/inquire", controller.Inquire)
		r.POST("/roles/actions/create", controller.Create)
		r.POST("/roles/actions/edit", controller.Edit)
		r.POST("/roles/actions/delete", controller.Delete)
		r.POST("/roles/actions/select-action", controller.RoleSelectActions)
		r.GET("/roles/actions/action-list", controller.RoleActionList)
	}
}

func roleFacilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility/").Use(handlers...)
	{
		controller := facility_api.NewFacilityRoleController()
		r.GET("/roles", controller.List)
		r.GET("/roles/actions/search", controller.Search)
		r.GET("/roles/actions/inquire", controller.Inquire)
		r.POST("/roles/actions/create", controller.Create)
		r.POST("/roles/actions/edit", controller.Edit)
		r.POST("/roles/actions/delete", controller.Delete)
		r.POST("/roles/actions/select-action", controller.RoleSelectActions)
		r.GET("/roles/actions/action-list", controller.RoleActionList)
	}
}
