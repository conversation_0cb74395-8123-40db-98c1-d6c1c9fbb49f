# Git Hooks 配置說明

## 啟用 Git Hooks

在使用任何 Git Hooks 功能之前，需要先啟用它們：

### Windows 環境
運行項目根目錄下的 setup-hooks 批處理文件：
```bash
.githooks\setup-hooks.bat
```

### Linux/MacOS 環境
運行項目根目錄下的 setup-hooks 腳本：
```bash
chmod +x .githooks/setup-hooks.sh  # 確保腳本有執行權限
.githooks/setup-hooks.sh
```

## Go 路徑配置

### 自定義 Go 路徑
在不同的項目中，可能需要使用不同版本的 Go 編譯器。本項目支持通過本地配置文件來自定義 Go 路徑，而不影響系統環境變量。

### 配置步驟

1. 在 `.githooks` 目錄下創建 `.go-path.local` 文件：
   ```bash
   touch .githooks/.go-path.local
   ```

2. 在 `.go-path.local` 文件中寫入 Go 可執行文件的完整路徑。

   Windows 環境（注意需要使用雙反斜線）：
   ```
   C:\\Program Files\\Go\\bin\\go.exe
   # 或者
   C:\\Users\\<USER>\\sdk\\go1.19\\bin\\go.exe
   ```

   Linux/MacOS 環境：
   ```
   /usr/local/go/bin/go
   # 或者
   /home/<USER>/sdk/go1.19/bin/go
   ```

### 注意事項

- `.go-path.local` 文件不會被提交到代碼倉庫
- 如果沒有配置此文件，將無法通過校驗
- Windows 環境下路徑中的反斜線需要寫成雙反斜線
- 每次提交代碼時，pre-commit hook 會自動使用配置的 Go 路徑執行相關檢查
