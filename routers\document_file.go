package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/Norray/medic-crew/api/v1/professional_api"
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func professionalDocumentFileRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/professional").Use(handlers...)
	{
		r.POST("/document-files/actions/upload", professional_api.NewProfessionalDocumentFileController().Upload)
		r.GET("/document-files/actions/preview", professional_api.NewProfessionalDocumentFileController().Preview)
	}
}

func facilityDocumentFileRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		r.GET("/document-files/actions/preview", facility_api.NewDocumentFileController().Preview)
	}
}

func systemDocumentFileRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/system").Use(handlers...)
	{
		r.GET("/document-files/actions/preview", system_api.NewDocumentFileController().Preview)
	}
}
