package routers

import (
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func casbinRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1").Use(handlers...)
	{
		r.POST("/system/casbin/actions/reload", system_api.NewCasbinController().Reload)
		r.POST("/system/casbin/actions/create", system_api.NewCasbinController().Create)
		r.GET("/system/casbin", system_api.NewCasbinController().List)
		r.POST("/system/casbin/actions/edit", system_api.NewCasbinController().Edit)
		r.GET("/system/casbin/actions/inquire", system_api.NewCasbinController().Inquire)
		r.POST("/system/casbin/actions/delete", system_api.NewCasbinController().Delete)
	}
}
