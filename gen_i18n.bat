@echo off
chcp 65001 >nul
REM Copy this file to root directory

echo Extracting i18n strings...
goi18n extract -outdir=resource/i18n
cd resource\i18n || exit /b 1

goi18n merge active.en.toml active.zh-CN.toml active.zh-HK.toml
if exist "translate.zh-CN.toml" (
    goi18n merge active.en.toml active.zh-CN.toml active.zh-HK.toml translate.zh-CN.toml
)
if exist "translate.zh-HK.toml" (
    goi18n merge active.en.toml active.zh-CN.toml active.zh-HK.toml translate.zh-HK.toml
)

REM Check if new translation files are generated
if exist "translate.zh-CN.toml" goto cleanup
if exist "translate.zh-HK.toml" goto cleanup
goto end

:cleanup
echo New translation content detected, please update translations before committing.

REM Clean up generated files
if exist "translate.zh-CN.toml" del translate.zh-CN.toml
if exist "translate.zh-HK.toml" del translate.zh-HK.toml
    
exit /b 1

:end