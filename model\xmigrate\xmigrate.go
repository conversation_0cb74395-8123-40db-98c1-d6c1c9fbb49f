package xmigrate

import (
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xmodel/xmigrate"
)

func MigrateAll() error {
	xmigrate.MigrateAll()
	err := xgorm.DB.AutoMigrate(
		&model.Agreement{},
		&model.Allowance{},
		&model.Benefit{},
		&model.Commission{},
		&model.Document{},
		&model.DocumentFile{},
		&model.DocumentFileRelation{},
		&model.DocumentItem{},
		&model.Facility{},
		&model.FacilityAgreement{},
		&model.FacilityBlacklist{},
		&model.FacilityFile{},
		&model.FacilityFileRelation{},
		&model.FacilityProfile{},
		&model.FacilityRole{},
		&model.FacilityUser{},
		&model.FacilitySpecialisation{},
		&model.Faq{},
		&model.FaqFile{},
		&model.GoogleUser{},
		&model.HourlyRate{},
		&model.Job{},
		&model.JobAllowance{},
		&model.JobApplication{},
		&model.JobBenefit{},
		&model.JobFeedback{},
		&model.JobFile{},
		&model.JobSchedule{},
		&model.JobScheduleDate{},
		&model.JobShift{},
		&model.JobShiftCancellation{},
		&model.Location{},
		&model.Professional{},
		&model.ProfessionalBankAccount{},
		&model.ProfessionalFile{},
		&model.ProfessionalFileRelation{},
		&model.ProfessionalJobAvailability{},
		&model.ProfessionalSuperannuation{},
		&model.ProfessionalTrainingProgress{},
		&model.ProfessionalGST{},
		&model.Selection{},
		&model.ServiceFacility{},
		&model.ServiceLocation{},
		&model.TrainingModule{},
		&model.WsMessage{},
		&model.WsSessionView{},
		&model.WsSession{},
	)
	return err
}
