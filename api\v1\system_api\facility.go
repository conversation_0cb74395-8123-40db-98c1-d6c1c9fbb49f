package system_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
)

type FacilityController struct {
	v1.CommonController
}

func NewFacilityController() FacilityController {
	return FacilityController{}
}

// @Tags Facility
// @Summary 搜索機構
// @Description
// @Router /v1/system/facilities/actions/search [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FacilitySearchReq true "parameter"
// @Success 200 {object} []services.FacilitySearchResp "Success"
func (con FacilityController) Search(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilitySearchReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.FacilityService.Search(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
