package system_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type JobController struct {
	v1.CommonController
}

func NewJobController() JobController {
	return JobController{}
}

// @Tags Job
// @Summary 查詢工作職位列表
// @Description 查詢工作職位列表，支持分頁和條件過濾
// @Router /v1/system/jobs [GET]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.JobListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Param sortingKey query string false "hourlyRate 時薪,createTime 建立時間,beginTime 開始時間"
// @Param sortingType query string false "a=順序,d=倒序"
// @Success 200 {object} []services.JobListResp "Success"
func (con JobController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobListReq
	var pageSet xresp.PageSet
	var sortSet xresp.SortingSet
	_ = c.ShouldBindQuery(&pageSet)
	_ = c.ShouldBindQuery(&sortSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		resp, err := services.JobService.List(db, req, &pageSet, sortSet, "SYSTEM")
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Job
// @Summary 搜索工作職位
// @Description 搜索工作職位，支持條件過濾和優先排序
// @Router /v1/system/jobs/actions/search [GET]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.JobSearchReq true "parameter"
// @Success 200 {object} []services.JobSearchResp "Success"
func (con JobController) Search(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobSearchReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		resp, err := services.JobService.Search(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Job
// @Summary 查詢工作職位詳情
// @Description 查詢工作職位詳情
// @Router /v1/system/jobs/actions/inquire [GET]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.JobInquireReq true "parameter"
// @Success 200 {object} services.JobInquireResp "Success"
func (con JobController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobInquireReqBySystem
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var checkMsg []string
		var job model.Job
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.JobService.CheckIdExist(db, &job, req.JobId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		resp, err := services.JobService.Inquire(db, services.JobInquireReq{
			JobId:      req.JobId,
			FacilityId: job.FacilityId,
		})
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
