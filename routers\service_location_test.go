package routers

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtest"
)

var testServiceLocationId uint64

func TestServiceLocationCreate(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/service-locations/actions/create",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "新增",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ServiceLocationCreateReq{
					FacilityId:    1,
					Address:       "測試地址",
					LocationState: "測試州",
					LocationCity:  "測試城市",
					Status:        model.ServiceLocationStatusEnable,
				},
				CheckResultHandler: func(resp interface{}) bool {
					response := resp.(xresp.Response)
					var data services.ServiceLocationCreateResp
					dataByte, _ := json.Marshal(response.Data)
					_ = json.Unmarshal(dataByte, &data)
					testServiceLocationId = data.ServiceLocationId
					return true
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestServiceLocationList(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/service-locations",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.ServiceLocationListReq{},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestServiceLocationSearch(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/service-locations/actions/search",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "搜索",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ServiceLocationSearchReq{
					FacilityId: 1,
					Status:     model.ServiceLocationStatusEnable,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestServiceLocationEdit(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/service-locations/actions/edit",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "修改",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ServiceLocationEditReq{
					ServiceLocationId: testServiceLocationId,
					Address:           "修改後的地址",
					LocationState:     "修改後的州",
					LocationCity:      "修改後的城市",
					Status:            model.ServiceLocationStatusEnable,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestServiceLocationInquire(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/service-locations/actions/inquire",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ServiceLocationInquireReq{
					ServiceLocationId: testServiceLocationId,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestServiceLocationDelete(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/service-locations/actions/delete",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "刪除",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ServiceLocationDeleteReq{
					ServiceLocationId: testServiceLocationId,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFixAllGoogleTimezone(t *testing.T) {
	fixServiceLocationIds := []uint64{} // 如需指定特定service_location_id，則填入，否則留空

	db := xgorm.DB
	var serviceLocationArr []model.ServiceLocation
	builder := db.Model(&model.ServiceLocation{})
	if len(fixServiceLocationIds) > 0 {
		builder = builder.Where("id IN (?)", fixServiceLocationIds)
	}
	if err := builder.Find(&serviceLocationArr).Error; err != nil {
		t.Fatal(err)
	}
	ctx := db.Statement.Context
	for _, serviceLocation := range serviceLocationArr {
		timezone, err := services.GoogleMapsService.GetTimezone(ctx, services.TimezoneRequest{
			Latitude:  serviceLocation.LocationLat,
			Longitude: serviceLocation.LocationLng,
		})
		if err != nil {
			t.Logf("[ERROR] facility_id: %d, service_location_id: %d, error: %v", serviceLocation.FacilityId, serviceLocation.Id, err)
		}
		t.Logf("[INFO] facility_id: %d, service_location_id: %d, timezone: %s", serviceLocation.FacilityId, serviceLocation.Id, timezone.Timezone)
		time.Sleep(1 * time.Second)
		tx := db.Begin()
		err = services.ServiceLocationService.Edit(tx, services.ServiceLocationEditReq{
			ServiceLocationId: serviceLocation.Id,
			Address:           serviceLocation.Address,
			AddressExtra:      serviceLocation.AddressExtra,
			LocationState:     serviceLocation.LocationState,
			LocationCity:      serviceLocation.LocationCity,
			LocationRoute:     serviceLocation.LocationRoute,
			LocationLat:       serviceLocation.LocationLat,
			LocationLng:       serviceLocation.LocationLng,
			Status:            serviceLocation.Status,
			Timezone:          timezone.Timezone,
		})
		if err != nil {
			tx.Rollback()
			t.Logf("[ERROR] facility_id: %d, service_location_id: %d, error: %v", serviceLocation.FacilityId, serviceLocation.Id, err)
		}
		if err := tx.Commit().Error; err != nil {
			t.Logf("[ERROR] facility_id: %d, service_location_id: %d, error: %v", serviceLocation.FacilityId, serviceLocation.Id, err)
		}
		t.Logf("[INFO] facility_id: %d, service_location_id: %d, timezone: %s", serviceLocation.FacilityId, serviceLocation.Id, timezone.Timezone)
	}
}
