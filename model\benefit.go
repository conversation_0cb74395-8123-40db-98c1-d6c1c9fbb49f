package model

import (
	"github.com/Norray/xrocket/xmodel"
)

const (
	BenefitStatusEnable  = "ENABLE"  // 啟用
	BenefitStatusDisable = "DISABLE" // 停用
)

// 福利管理
type Benefit struct {
	Id         uint64 `json:"id" gorm:"primary_key"`
	FacilityId uint64 `json:"facilityId" gorm:"index:facility_idx;not null"`            // 機構ID // 機構聯絡電郵
	Name       string `json:"name" gorm:"type:varchar(255);not null"`                   // 福利名稱
	Status     string `json:"status" gorm:"type:varchar(32);index:status_idx;not null"` // 狀態
	xmodel.Model
}

func (Benefit) TableName() string {
	return "benefit"
}

func (Benefit) SwaggerDescription() string {
	return "機構的福利管理"
}
