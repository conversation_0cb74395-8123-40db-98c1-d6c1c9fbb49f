package system_api

import (
	"fmt"
	"github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"net/http"
)

type FacilityFileController struct {
	v1.CommonController
}

func NewFacilityFileController() FacilityFileController {
	return FacilityFileController{}
}

// @Tags Facility File
// @Summary 獲取機構文件圖片
// @Description 直接獲取機構文件的圖片內容，可以在瀏覽器中直接顯示
// @Router /v1/system/facility-files/actions/preview [GET]
// @Security ApiKeyAuth
// @Param facilityFileId query uint64 true "機構文件Id"
// @Param thumb query string true "是否獲取縮略圖 Y=縮略圖 N=原圖"
// @Success 200 "Success"
func (con FacilityFileController) Preview(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityFileGetPreviewReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityFileService.CheckIdExist(db, &model.FacilityFile{}, req.FacilityFileId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		// 獲取圖片數據
		resp, err := services.FacilityFileService.Preview(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		c.Writer.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename*=utf-8''%s", xtool.ReplacePlus(resp.Filename)))
		c.Data(http.StatusOK, services.FacilityFileService.GetFileMimeType(resp.Filename), resp.FileBytes)

	} else {
		nc.BadRequestResponse(err)
	}
}
