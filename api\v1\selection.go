package v1

import (
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
)

type SelectionController struct {
	CommonController
}

func NewSelectionController() SelectionController {
	return SelectionController{}
}

// @Tags Selection
// @Summary 選項列表
// @Router /v1/public/selections [GET]
// @Produce  json
// @Param form query services.SelectionListReqByPublic true "parameter"
// @Success 200 {object} map[string][]services.SelectionListResp "Success"
func (con SelectionController) ListByPublic(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.SelectionListReqByPublic
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.SelectionService.List(db, services.SelectionListReq{
			SelectionTypes: req.SelectionTypes,
		})
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Selection
// @Summary 選項列表
// @Router /v1/app/selections [GET]
// @Produce  json
// @Param form query services.SelectionListReqByApp true "parameter"
// @Success 200 {object} map[string][]services.SelectionListResp "Success"
func (con SelectionController) ListByApp(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.SelectionListReqByApp
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.SelectionService.List(db, services.SelectionListReq{
			SelectionTypes: req.SelectionTypes,
		})
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Selection
// @Summary 選項列表
// @Router /v1/professional/selections [GET]
// @Produce  json
// @Param form query services.SelectionListReqByProfessional true "parameter"
// @Success 200 {object} map[string][]services.SelectionListResp "Success"
func (con SelectionController) ListByProfessional(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.SelectionListReqByProfessional
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.SelectionService.List(db, services.SelectionListReq{
			SelectionTypes: req.SelectionTypes,
		})
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Selection
// @Summary 選項列表
// @Router /v1/facility/selections [GET]
// @Produce  json
// @Param form query services.SelectionListReqByFacility true "parameter"
// @Success 200 {object} map[string][]services.SelectionListResp "Success"
func (con SelectionController) ListByFacility(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.SelectionListReqByFacility
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.SelectionService.List(db, services.SelectionListReq{
			SelectionTypes: req.SelectionTypes,
		})
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Selection
// @Summary 選項列表
// @Router /v1/system/selections [GET]
// @Produce  json
// @Param form query services.SelectionListReq true "parameter"
// @Success 200 {object} map[string][]services.SelectionListResp "Success"
func (con SelectionController) ListBySystem(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.SelectionListReq
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.SelectionService.List(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
