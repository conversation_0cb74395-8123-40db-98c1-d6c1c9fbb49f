package facility_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type HourlyRateController struct {
	v1.CommonController
}

func NewHourlyRateController() HourlyRateController {
	return HourlyRateController{}
}

// @Tags Hourly Rate
// @Summary 獲取機構的時薪列表
// @Description
// @Router /v1/facility/hourly-rates [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.HourlyRateListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Success 200 {object} []services.HourlyRateListResp "Success"
func (con HourlyRateController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.HourlyRateListReq
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.HourlyRateService.List(db, req, &pageSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Hourly Rate
// @Summary 批量更新機構的時薪
// @Description
// @Router /v1/facility/hourly-rates/actions/update [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.HourlyRateUpdateReq true "parameter"
// @Success 200 "Success"
func (con HourlyRateController) Update(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.HourlyRateUpdateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.HourlyRateService.CheckIdsExist(db, req.FacilityId, req.Items)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.HourlyRateService.Update(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)

	} else {
		nc.BadRequestResponse(err)
	}
}
