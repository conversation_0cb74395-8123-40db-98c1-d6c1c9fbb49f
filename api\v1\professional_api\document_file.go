package professional_api

import (
	"fmt"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"mime/multipart"
	"net/http"

	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	"github.com/gin-gonic/gin"
)

type ProfessionalDocumentFileController struct {
	v1.CommonController
}

func NewProfessionalDocumentFileController() ProfessionalDocumentFileController {
	return ProfessionalDocumentFileController{}
}

// @Tags Document File
// @Summary 獲取單據文件圖片
// @Description 直接獲取單據文件的圖片內容，可以在瀏覽器中直接顯示
// @Router /v1/professional/document-files/actions/preview [GET]
// @Security ApiKeyAuth
// @Param json query services.DocumentFileGetPreviewReq true "parameter"
// @Success 200 "Success"
func (con ProfessionalDocumentFileController) Preview(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.DocumentFileGetPreviewReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		req.UserId = nc.GetJWTUserId()

		// 獲取圖片數據
		resp, err := services.DocumentFileService.Preview(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		c.Writer.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename*=utf-8''%s", xtool.ReplacePlus(resp.Filename)))
		c.Data(http.StatusOK, services.DocumentFileService.GetFileMimeType(resp.Filename), resp.FileBytes)

	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Document File
// @Summary 上載單據文件
// @Description
// @Router /v1/professional/document-files/actions/upload [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.DocumentFileUploadReq true "parameter"
// @Param file formData file true "文件"
// @Success 200 {object} services.DocumentFileUploadResp "Success"
func (con ProfessionalDocumentFileController) Upload(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.DocumentFileUploadReq
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		req.UserId = nc.GetJWTUserId()

		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.DocumentFileService.CheckFileCodeExist(req.FileCode)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		var formFile *multipart.FileHeader
		formFile, err = c.FormFile("file")
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		req.File = formFile

		tx := db.Begin()
		resp, err := services.DocumentFileService.Upload(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
