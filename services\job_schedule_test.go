package services

import (
	"fmt"
	"testing"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xmodel/xtype"
	"github.com/Norray/xrocket/xtool"
)

func TestJobScheduleService_CalcJobScheduleDates(t *testing.T) {
	s := new(jobScheduleService)
	timezone := "Australia/Sydney"
	// 設置時區為澳洲
	loc, _ := time.LoadLocation(timezone)
	time.Local = loc
	// 設置測試的開始時間
	startTime, _ := time.Parse(xtool.DateTimeSecA1, "2025-10-01 08:00:00")

	tests := []struct {
		name      string
		schedule  model.JobSchedule
		startTime time.Time
		wantDates []string
		wantErr   bool
	}{
		{
			name: "每日重複_間隔1天",
			schedule: model.JobSchedule{
				RepeatType:    model.JobScheduleRepeatDaily,
				BeginDate:     xtype.NewNullDate("2025-10-01"),
				EndDate:       xtype.NewNullDate("2025-10-05"),
				DailyInterval: 1,
			},
			startTime: startTime,
			wantDates: []string{"2025-10-01", "2025-10-02", "2025-10-03", "2025-10-04", "2025-10-05"},
			wantErr:   false,
		},
		{
			name: "每日重複_間隔2天",
			schedule: model.JobSchedule{
				RepeatType:    model.JobScheduleRepeatDaily,
				BeginDate:     xtype.NewNullDate("2025-10-01"),
				EndDate:       xtype.NewNullDate("2025-10-10"),
				DailyInterval: 2,
			},
			startTime: startTime,
			wantDates: []string{"2025-10-01", "2025-10-03", "2025-10-05", "2025-10-07", "2025-10-09"},
			wantErr:   false,
		},
		{
			name: "每週重複_每週三五日",
			schedule: model.JobSchedule{
				RepeatType:     model.JobScheduleRepeatWeekly,
				BeginDate:      xtype.NewNullDate("2025-10-01"), // 2025-10-01是週三
				EndDate:        xtype.NewNullDate("2025-10-14"), // 兩週後
				WeeklyInterval: 1,
				WeekDays:       "3,5,0", // 週三、週五、週日
			},
			startTime: startTime,
			wantDates: []string{"2025-10-01", "2025-10-03", "2025-10-05", "2025-10-08", "2025-10-10", "2025-10-12"},
			wantErr:   false,
		},
		{
			name: "每週重複_間隔2週",
			schedule: model.JobSchedule{
				RepeatType:     model.JobScheduleRepeatWeekly,
				BeginDate:      xtype.NewNullDate("2025-10-01"), // 週三
				EndDate:        xtype.NewNullDate("2025-10-29"), // 四週後
				WeeklyInterval: 2,
				WeekDays:       "3,4", // 週三、週四
			},
			startTime: startTime,
			wantDates: []string{"2025-10-01", "2025-10-02", "2025-10-15", "2025-10-16", "2025-10-29"},
			wantErr:   false,
		},
		{
			name: "每月重複_按日期_每月15日",
			schedule: model.JobSchedule{
				RepeatType:        model.JobScheduleRepeatMonthly,
				BeginDate:         xtype.NewNullDate("2025-10-01"),
				EndDate:           xtype.NewNullDate("2025-12-31"),
				MonthlyInterval:   1,
				MonthlyType:       model.JobScheduleMonthlyTypeDay,
				MonthlyDayOfMonth: 15,
			},
			startTime: startTime,
			wantDates: []string{"2025-10-15", "2025-11-15", "2025-12-15"},
			wantErr:   false,
		},
		{
			name: "每月重複_按日期_間隔2個月",
			schedule: model.JobSchedule{
				RepeatType:        model.JobScheduleRepeatMonthly,
				BeginDate:         xtype.NewNullDate("2025-10-01"),
				EndDate:           xtype.NewNullDate("2026-03-31"),
				MonthlyInterval:   2,
				MonthlyType:       model.JobScheduleMonthlyTypeDay,
				MonthlyDayOfMonth: 5,
			},
			startTime: startTime,
			wantDates: []string{"2025-10-05", "2025-12-05", "2026-02-05"},
			wantErr:   false,
		},
		{
			name: "每月重複_按星期_每月第二個週二",
			schedule: model.JobSchedule{
				RepeatType:       model.JobScheduleRepeatMonthly,
				BeginDate:        xtype.NewNullDate("2025-10-01"),
				EndDate:          xtype.NewNullDate("2025-12-31"),
				MonthlyInterval:  1,
				MonthlyType:      model.JobScheduleMonthlyTypeWeekday,
				MonthlyWeekIndex: 2,
				MonthlyWeekDay:   2, // 週二
			},
			startTime: startTime,
			wantDates: []string{"2025-10-14", "2025-11-11", "2025-12-09"},
			wantErr:   false,
		},
		{
			name: "每月重複_按星期_每兩月第三個週五",
			schedule: model.JobSchedule{
				RepeatType:       model.JobScheduleRepeatMonthly,
				BeginDate:        xtype.NewNullDate("2025-10-01"),
				EndDate:          xtype.NewNullDate("2026-03-31"),
				MonthlyInterval:  2,
				MonthlyType:      model.JobScheduleMonthlyTypeWeekday,
				MonthlyWeekIndex: 3,
				MonthlyWeekDay:   5, // 週五
			},
			startTime: startTime,
			wantDates: []string{"2025-10-17", "2025-12-19", "2026-02-20"},
			wantErr:   false,
		},
		{
			name: "開始時間接近當前時間",
			schedule: model.JobSchedule{
				RepeatType:    model.JobScheduleRepeatDaily,
				BeginDate:     xtype.NewNullDate("2025-05-09"),
				EndDate:       xtype.NewNullDate("2025-05-13"),
				DailyInterval: 1,
			},
			startTime: time.Date(2025, 5, 9, 13, 00, 0, 0, loc),                         // 只比排程開始時間早30分鐘
			wantDates: []string{"2025-05-10", "2025-05-11", "2025-05-12", "2025-05-13"}, // 第一天不應該包含
			wantErr:   false,
		},
		{
			name: "無效的日期格式",
			schedule: model.JobSchedule{
				RepeatType:    model.JobScheduleRepeatDaily,
				BeginDate:     xtype.NewNullDate("2025/10/01"), // 錯誤的日期格式
				EndDate:       xtype.NewNullDate("2025-10-05"),
				DailyInterval: 1,
			},
			startTime: startTime,
			wantDates: nil,
			wantErr:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fmt.Printf("執行測試: %s\n", tt.name)

			gotDates, err := s.CalcJobScheduleDates(tt.schedule, tt.startTime, timezone)

			if tt.wantErr {
				if err == nil {
					t.Errorf("預期錯誤，但獲得nil")
				}
				return
			}

			if err != nil {
				t.Errorf("預期無錯誤，但獲得: %v", err)
				return
			}

			if len(gotDates) != len(tt.wantDates) {
				t.Errorf("日期數量不匹配，預期 %d 個日期，實際獲得 %d 個", len(tt.wantDates), len(gotDates))
				t.Errorf("預期日期: %v", tt.wantDates)
				t.Errorf("實際獲得日期: %v", gotDates)
				return
			}

			for i, date := range gotDates {
				if date != tt.wantDates[i] {
					t.Errorf("第 %d 個日期不匹配，預期 %s，實際獲得 %s", i+1, tt.wantDates[i], date)
				}
			}

			fmt.Printf("測試通過: %s\n", tt.name)
			fmt.Printf("獲得的日期: %v\n", gotDates)
		})
	}
}
