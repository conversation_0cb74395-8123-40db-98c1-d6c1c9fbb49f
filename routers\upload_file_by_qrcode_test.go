package routers

import (
	"encoding/json"
	"fmt"
	"github.com/Norray/medic-crew/model"
	"os"
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

var facilityQrcode string
var professionalQrcode string

func TestGenUploadQrcode(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/app/files/actions/gen-upload-qrcode",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "生成上傳文件的二維碼",
		Cases: []xtest.TestCase{
			{
				SubName:           "機構-正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.GenUploadFileQrcodeReq{
					FileCode: model.FacilityFileCodeSignedAgreement,
				},
				CheckResultHandler: func(resp interface{}) bool {
					response := resp.(xresp.Response)
					var data services.GenUploadFileQrcodeResp
					dataByte, _ := json.Marshal(response.Data)
					_ = json.Unmarshal(dataByte, &data)
					facilityQrcode = data.Uuid
					return true
				},
			},
		},
	}
	xtest.RunTests(t, test)

	test2 := xtest.Test{
		Url:              programPath + "/v1/app/files/actions/gen-upload-qrcode",
		UserId:           8,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "生成上傳文件的二維碼",
		Cases: []xtest.TestCase{
			{
				SubName:           "專業人士-正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.GenUploadFileQrcodeReq{
					FileCode: model.ProfessionalFileCodeSignedAgreement,
				},
				CheckResultHandler: func(resp interface{}) bool {
					response := resp.(xresp.Response)
					var data services.GenUploadFileQrcodeResp
					dataByte, _ := json.Marshal(response.Data)
					_ = json.Unmarshal(dataByte, &data)
					professionalQrcode = data.Uuid
					return true
				},
			},
		},
	}
	xtest.RunTests(t, test2)
}

func TestExchangeQrcodeFacility(t *testing.T) {
	// 讀取圖片
	file, err := os.Open("demo.png")
	if err != nil {
		t.Error(err)
		return
	}
	test := xtest.Test{
		Url:        programPath + "/v1/public/files/actions/exchange-by-qrcode",
		Method:     xtest.Post,
		ParamsType: xtest.Form,
		Name:       "掃描二維碼上傳文件",
		Cases: []xtest.TestCase{
			{
				SubName:           "機構-正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ExchangeByQrcodeReq{
					Uuid: facilityQrcode,
				},
				File:          file,
				FileFieldName: "file",
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestExchangeQrcodeProfessional(t *testing.T) {
	// 讀取圖片
	file, err := os.Open("demo.png")
	if err != nil {
		t.Error(err)
		return
	}
	test := xtest.Test{
		Url:        programPath + "/v1/public/files/actions/exchange-by-qrcode",
		Method:     xtest.Post,
		ParamsType: xtest.Form,
		Name:       "掃描二維碼上傳文件",
		Cases: []xtest.TestCase{
			{
				SubName:           "機構-正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ExchangeByQrcodeReq{
					Uuid: professionalQrcode,
				},
				File:          file,
				FileFieldName: "file",
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestInquireByQrcode(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/app/files/actions/inquire-by-qrcode",
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		UserId:           5,
		UserIdWithDevice: true,
		Name:             "獲取通過二維碼上傳的文件id",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.InquireFileIdByQrcodeReq{
					Uuid: facilityQrcode,
				},
				CheckResultHandler: func(resp interface{}) bool {
					response := resp.(xresp.Response)
					var data services.FacilityFileUploadResp
					dataByte, _ := json.Marshal(response.Data)
					_ = json.Unmarshal(dataByte, &data)
					fmt.Println("facilityFileId:", data.FacilityFileId)
					return true
				},
			},
		},
	}
	xtest.RunTests(t, test)

	test2 := xtest.Test{
		Url:              programPath + "/v1/app/files/actions/inquire-by-qrcode",
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		UserId:           8,
		UserIdWithDevice: true,
		Name:             "獲取通過二維碼上傳的文件id",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.InquireFileIdByQrcodeReq{
					Uuid: professionalQrcode,
				},
				CheckResultHandler: func(resp interface{}) bool {
					response := resp.(xresp.Response)
					var data services.ProfessionalFileUploadFileResp
					dataByte, _ := json.Marshal(response.Data)
					_ = json.Unmarshal(dataByte, &data)
					fmt.Println("professionalFileId:", data.ProfessionalFileId)
					return true
				},
			},
		},
	}
	xtest.RunTests(t, test2)
}
