package facility_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type FacilityBlacklistController struct {
	v1.CommonController
}

func NewFacilityBlacklistController() FacilityBlacklistController {
	return FacilityBlacklistController{}
}

// @Tags Facility Blacklist
// @Summary 獲取機構黑名單列表
// @Description
// @Router /v1/facility/blacklists [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FacilityBlacklistListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Success 200 {object} []services.FacilityBlacklistListResp "Success"
func (con FacilityBlacklistController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityBlacklistListReq
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.FacilityBlacklistService.List(db, req, &pageSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility Blacklist
// @Summary 新增機構黑名單
// @Description
// @Router /v1/facility/blacklists/actions/create [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FacilityBlacklistCreateReq true "parameter"
// @Success 200 {object} services.FacilityBlacklistCreateResp "Success"
func (con FacilityBlacklistController) Create(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityBlacklistCreateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		req.UserId = nc.GetJWTUserId()

		var checkMsg []string
		var professional model.Professional
		checker := xapp.NewCK(c, true)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckIdExist(db, &professional, req.ProfessionalId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityBlacklistService.CanAddBlacklist(db, req.FacilityId, professional.UserId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		req.UserId = professional.UserId
		tx := db.Begin()
		resp, err := services.FacilityBlacklistService.Create(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility Blacklist
// @Summary 修改機構黑名單
// @Description
// @Router /v1/facility/blacklists/actions/edit [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FacilityBlacklistEditReq true "parameter"
// @Success 200 "Success"
func (con FacilityBlacklistController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityBlacklistEditReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var checkMsg []string
		var facilityBlacklist model.FacilityBlacklist
		checker := xapp.NewCK(c, true)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityBlacklistService.CheckIdExist(db, &facilityBlacklist, req.FacilityId, req.FacilityBlacklistId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		tx := db.Begin()
		err = services.FacilityBlacklistService.Edit(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility Blacklist
// @Summary 删除機構黑名單
// @Description
// @Router /v1/facility/blacklists/actions/delete [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FacilityBlacklistDeleteReq true "parameter"
// @Success 200 "Success"
func (con FacilityBlacklistController) Delete(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityBlacklistDeleteReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityBlacklistService.CheckIdExist(db, &model.FacilityBlacklist{}, req.FacilityId, req.FacilityBlacklistId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		tx := db.Begin()
		err = services.FacilityBlacklistService.Delete(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}
