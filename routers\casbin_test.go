package routers

import (
	"encoding/json"
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

var testCasbinId uint64

func TestCasbinCreate(t *testing.T) {
	// 使用debug運行才有顏色
	// 用戶信息，用於簽發jwt
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/casbin/actions/create",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "新增",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.CasbinCreateReq{
					CasbinName: "CONSOLE",
					Ptype:      "p",
					V0:         "user",
					V1:         "/medic-crew/api/v1/user-devices/actions/revoke",
					V2:         "POST",
					V3:         "",
					V4:         "",
					V5:         "",
					Level:      "",
					Remark:     "登出設備",
				},
				CheckResultHandler: func(resp interface{}) bool {
					response := resp.(xresp.Response)
					var data services.CasbinCreateResp
					dataByte, _ := json.Marshal(response.Data)
					_ = json.Unmarshal(dataByte, &data)
					testCasbinId = data.CasbinId
					return true
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestCasbinList(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/casbin",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.CasbinListReq{
					CasbinName: "CONSOLE",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestCasbinEdit(t *testing.T) {
	// 使用debug運行才有顏色
	// 用戶信息，用於簽發jwt
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/casbin/actions/edit",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "修改",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.CasbinEditReq{
					CasbinId:   testCasbinId,
					CasbinName: "CONSOLE",
					Ptype:      "p",
					V0:         "user",
					V1:         "/medic-crew/api/v1/user-devices/actions/revoke",
					V2:         "POST",
					V3:         "",
					V4:         "",
					V5:         "",
					Level:      "",
					Remark:     "登出設備",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestCasbinInquire(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/casbin/actions/inquire",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.CasbinInquireReq{
					CasbinName: "CONSOLE",
					CasbinId:   1,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestCasbinDelete(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/casbin/actions/delete",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "刪除",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.CasbinDeleteReq{
					CasbinName: "CONSOLE",
					CasbinId:   testCasbinId,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestCasbinReload(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/casbin/actions/reload",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "更新",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.CasbinReloadReq{
					CasbinName: "CONSOLE",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
