package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/gin-gonic/gin"
)

func facilityBlacklistRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		r.GET("/blacklists", facility_api.NewFacilityBlacklistController().List)
		r.POST("/blacklists/actions/create", facility_api.NewFacilityBlacklistController().Create)
		r.POST("/blacklists/actions/edit", facility_api.NewFacilityBlacklistController().Edit)
		r.POST("/blacklists/actions/delete", facility_api.NewFacilityBlacklistController().Delete)
	}
}
