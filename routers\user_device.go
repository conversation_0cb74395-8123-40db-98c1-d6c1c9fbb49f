package routers

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func userDevicePublicRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1").Use(handlers...)
	{
		r.POST("/user-devices/actions/apply", v1.NewUserDeviceController().UserDevicesApply)
		r.POST("/user-devices/actions/verify", v1.NewUserDeviceController().UserDevicesVerify)
	}
}

func userDeviceRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1").Use(handlers...)
	{
		r.POST("/user-devices/actions/revoke", v1.NewUserDeviceController().UserDeviceRevoke)
	}
}

func userDeviceSystemRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/system").Use(handlers...)
	{
		r.GET("/user-devices", system_api.NewUserDeviceController().UserDevices)
		r.POST("/user-devices/actions/change-status", system_api.NewUserDeviceController().ChangeStatus)
	}
}
