package routers

import (
	"testing"
	"time"

	"github.com/Norray/xrocket/xtool"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtest"
	"github.com/shopspring/decimal"
)

// region ------------------------------------------ 機構端 ------------------------------------------
func TestJobList(t *testing.T) {
	// 構建測試用例
	user := getTestUser(19)
	test := xtest.Test{
		Url:              programPath + "/v1/facility/jobs",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "列表",
		Cases: []xtest.TestCase{
			//{
			//	SubName:           "草稿",
			//	ExpectErrRespCode: xresp.StatusOK,
			//	Params: services.JobListReq{
			//		FacilityId:  user.FacilityId,
			//		JobCategory: services.JobCategoryDraft,
			//	},
			//},
			{
				SubName:           "招聘中",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobListReq{
					FacilityId: user.FacilityId,
					//PositionProfession: "",
					//ServiceLocationId:  0,
					//BeginTime:          "",
					//EndTime:            "",
					//JobScheduleId:      0,
					//Progress:           "",
					//Status:             "",
					JobCategory: services.JobCategoryOpen,
					//HiringStatus: "PARTIALLY_COMPLETE",
				},
				PageSet: xresp.PageSet{
					PageIndex: 1,
					PageSize:  10,
				},
			},
			//{
			//	SubName:           "待開始",
			//	ExpectErrRespCode: xresp.StatusOK,
			//	Params: services.JobListReq{
			//		FacilityId:  user.FacilityId,
			//		JobCategory: services.JobCategoryUpcoming,
			//	},
			//},
			//{
			//	SubName:           "進行中",
			//	ExpectErrRespCode: xresp.StatusOK,
			//	Params: services.JobListReq{
			//		FacilityId:  user.FacilityId,
			//		JobCategory: services.JobCategoryInProgress,
			//	},
			//},
			//{
			//	SubName:           "已結束",
			//	ExpectErrRespCode: xresp.StatusOK,
			//	Params: services.JobListReq{
			//		FacilityId:  user.FacilityId,
			//		JobCategory: services.JobCategoryComplete,
			//	},
			//},
			//{
			//	SubName:           "等待發佈",
			//	ExpectErrRespCode: xresp.StatusOK,
			//	Params: services.JobListReq{
			//		FacilityId:  user.FacilityId,
			//		JobCategory: services.JobCategoryWaiting,
			//	},
			//},
			//{
			//	SubName:           "已取消",
			//	ExpectErrRespCode: xresp.StatusOK,
			//	Params: services.JobListReq{
			//		FacilityId:  user.FacilityId,
			//		JobCategory: services.JobCategoryCancel,
			//	},
			//},
		},
	}
	xtest.RunTests(t, test)
}

func TestJobCreate(t *testing.T) {
	// 構建測試用例
	facilityId := uint64(3)
	test := xtest.Test{
		Url:              programPath + "/v1/facility/jobs/actions/create",
		UserId:           11,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "創建",
		Cases: []xtest.TestCase{
			//{
			//	SubName:           "正常",
			//	ExpectErrRespCode: xresp.StatusOK,
			//	Params: services.JobCreateReq{
			//		FacilityId:         facilityId,
			//		PositionProfession: model.JobPositionProfessionMedicalPractitioner,
			//		NumberOfPeople:     2,
			//		ServiceLocationId:  1,
			//		MinExperienceLevel:  "Senior",
			//		Specialisation:     "Emergency Care",
			//		Language:           model.JobLanguageEnglish,
			//		SupervisionLevel:   model.JobSupervisionFullySupervised,
			//		HourlyRate:         decimal.NewFromFloat(850.00),
			//		BreakTime:           10,
			//		BreakTimePayable:    model.JobShiftTimeBreakTimePayableY,
			//		Benefits:           "Including meal and transportation allowance",
			//		ShiftAllocation:    model.JobShiftAllocationAutomatic,
			//		Remark:             "Emergency experience required",
			//		JobShiftItems: []services.JobShiftItem{
			//			{
			//				BeginTime: "2025-04-27 08:00:00",
			//				EndTime:   "2025-04-27 18:00:00",
			//			},
			//			{
			//				BeginTime: "2025-04-28 08:00:00",
			//				EndTime:   "2025-04-28 12:00:00",
			//			},
			//		},
			//	},
			//},
			{
				SubName:           "暫存",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobCreateReq{
					Draft:              "Y",
					FacilityId:         facilityId,
					PositionProfession: model.JobPositionProfessionMedicalPractitioner,
					NumberOfPeople:     2,
					ServiceLocationId:  1,
					MinExperienceLevel: "",
					Specialisation:     "",
					Language:           "BENGALI,BAHASA_INDONESIA,ARABIC,CANTONESE,CHINESE,FRENCH,GERMAN,HINDI,ITALIAN,JAPANESE,KOREAN,MALAY,PORTUGUESE,RUSSIAN,SPANISH,TAGALOG,THAI,TURKISH,URDU",
					SupervisionLevel:   "",
					Benefits:           "",
					ShiftAllocation:    "",
					Remark:             "Emergency experience required",
					JobShiftItems: []services.JobShiftItem{
						{
							//BeginTime: "2025-04-27 08:00:00",
							//EndTime:   "2025-04-27 18:00:00",
							BeginTime: nil,
							EndTime:   nil,
						},
						{
							//BeginTime: "2025-04-28 08:00:00",
							//EndTime:   "2025-04-28 12:00:00",
						},
					},
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestJobSearch(t *testing.T) {
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/jobs/actions/search",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "搜索",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobSearchReq{
					FacilityId: 1,
					Limit:      10,
					Progress:   services.JobProgressUpcoming,
				},
			},
			{
				SubName:           "按職位名稱搜索",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobSearchReq{
					FacilityId:         1,
					PositionProfession: model.JobPositionProfessionEnrolledNurse,
					Limit:              10,
					Progress:           services.JobProgressInProgress,
				},
			},
			{
				SubName:           "按地點搜索",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobSearchReq{
					FacilityId:        1,
					ServiceLocationId: 1,
					Limit:             10,
					Progress:          services.JobProgressInProgress,
				},
			},
			{
				SubName:           "按時間範圍搜索",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobSearchReq{
					FacilityId: 1,
					BeginTime:  "2023-01-01 00:00:00",
					EndTime:    "2023-12-31 23:59:59",
					Limit:      10,
					Progress:   services.JobProgressInProgress,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestJobInquire(t *testing.T) {
	// 查詢一個存在的工作職位Id
	var jobId uint64 = 13
	facilityId := uint64(3)

	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/jobs/actions/inquire",
		UserId:           11,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobInquireReq{
					FacilityId: facilityId,
					JobId:      jobId,
				},
			},
			{
				SubName:           "記錄不存在",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobInquireReq{
					FacilityId: facilityId,
					JobId:      99999, // 假設不存在的Id
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestJobEdit(t *testing.T) {
	// 查詢一個存在的工作職位Id
	var jobId uint64 = 1
	beginTime, _ := time.Parse(xtool.DateTimeSecA1, "2023-01-01 09:00:00")
	endTime, _ := time.Parse(xtool.DateTimeSecA1, "2023-01-01 17:00:00")
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/jobs/actions/edit",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "編輯",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobEditReq{
					FacilityId:         1,
					JobId:              jobId,
					PositionProfession: model.JobPositionProfessionEnrolledNurse,
					NumberOfPeople:     2,
					ServiceLocationId:  1,
					MinExperienceLevel: "Advanced",
					Specialisation:     "Edited Specialization",
					Language:           "English, Spanish",
					SupervisionLevel:   model.JobSupervisionPartiallySupervised,
					Benefits:           "Edited Benefits",
					ShiftAllocation:    model.JobShiftAllocationManual,
					Remark:             "Edited Remarks",
					JobShiftItems: []services.JobShiftItem{
						{
							BeginTime:        &beginTime,
							EndTime:          &endTime,
							BreakTimePayable: "Y",
						},
					},
				},
			},
			{
				SubName:           "記錄不存在",
				ExpectErrRespCode: xresp.StatusNotFound,
				Params: services.JobEditReq{
					FacilityId:         1,
					JobId:              99999, // 假設不存在的Id
					PositionProfession: model.JobPositionProfessionRegisteredNurse,
					NumberOfPeople:     1,
					ServiceLocationId:  1,
					MinExperienceLevel: "Junior",
					Specialisation:     "Test Specialization",
					Language:           "English",
					SupervisionLevel:   model.JobSupervisionUnsupervised,
					ShiftAllocation:    model.JobShiftAllocationAutomatic,
					JobShiftItems: []services.JobShiftItem{
						{
							BeginTime:        &beginTime,
							EndTime:          &endTime,
							BreakTimePayable: "Y",
						},
					},
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestJobDelete(t *testing.T) {
	beginTime, _ := time.Parse(xtool.DateTimeSecA1, "2023-01-01 08:00:00")
	endTime, _ := time.Parse(xtool.DateTimeSecA1, "2023-01-01 18:00:00")
	// 創建一個新的工作職位用於刪除測試
	job := model.Job{
		FacilityId:         1,
		CreatedUserId:      1,
		UpdatedUserId:      0,
		JobScheduleId:      1,
		ScheduleTemplate:   "N",
		ServiceLocationId:  1,
		JobNo:              "JOB-TEST-DELETE",
		PositionProfession: model.JobPositionProfessionRegisteredNurse,
		NumberOfPeople:     1,
		MinExperienceLevel: "Junior",
		PreferredGrade:     "",
		Qualification:      "",
		Specialisation:     "Test Specialization",
		Language:           "English",
		SupervisionLevel:   model.JobSupervisionUnsupervised,
		SplitType:          "",
		Duration:           decimal.Zero,
		PayHours:           decimal.Zero,
		ShiftAllocation:    model.JobShiftAllocationAutomatic,
		ShiftTimeType:      "",
		WeekdayType:        "",
		Remark:             "",
		CancelReason:       "",
		Status:             model.JobStatusPending,
		PaymentTerms:       "PAY_IN_ARREARS",
		AcceptedCount:      0,
		BeginTime:          &beginTime,
		EndTime:            &endTime,
		CreateTime:         time.Now().UTC(),
		UpdateTime:         nil,
	}
	if err := xgorm.DB.Create(&job).Error; err != nil {
		t.Fatal(err)
	}

	// 創建班次時間
	jobShift := model.JobShift{
		FacilityId:       1,
		JobId:            job.Id,
		BeginTime:        &beginTime,
		EndTime:          &endTime,
		Duration:         decimal.NewFromInt(10),
		BreakDuration:    decimal.Zero,
		PayHours:         decimal.NewFromInt(10),
		HourlyRate:       decimal.NewFromInt(50),
		AllowanceAmount:  decimal.Zero,
		BreakTimePayable: "Y",
	}
	if err := xgorm.DB.Create(&jobShift).Error; err != nil {
		t.Fatal(err)
	}

	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/jobs/actions/delete",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "刪除",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobDeleteReq{
					FacilityId: 1,
					JobId:      job.Id,
				},
			},
			{
				SubName:           "記錄不存在",
				ExpectErrRespCode: xresp.StatusNotFound,
				Params: services.JobDeleteReq{
					FacilityId: 1,
					JobId:      99999, // 假設不存在的Id
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestJobUpdateStatus(t *testing.T) {
	facilityId := uint64(1)
	jobId := uint64(1)

	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/jobs/actions/update-status",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "更新狀態",
		Cases: []xtest.TestCase{
			{
				SubName:           "發佈工作",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobUpdateStatusReq{
					FacilityId: facilityId,
					JobId:      jobId,
					Status:     model.JobStatusPublish,
				},
			},
			{
				SubName:           "禁用工作",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobUpdateStatusReq{
					FacilityId: facilityId,
					JobId:      jobId,
					Status:     model.JobStatusDisable,
				},
			},
			{
				SubName:           "完成工作",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobUpdateStatusReq{
					FacilityId: facilityId,
					JobId:      jobId,
					Status:     model.JobStatusComplete,
				},
			},
			{
				SubName:           "取消工作",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobUpdateStatusReq{
					FacilityId: facilityId,
					JobId:      jobId,
					Status:     model.JobStatusCancel,
				},
			},
			{
				SubName:           "記錄不存在",
				ExpectErrRespCode: xresp.StatusNotFound,
				Params: services.JobUpdateStatusReq{
					FacilityId: 1,
					JobId:      99999, // 假設不存在的Id
					Status:     model.JobStatusPublish,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// endregion ------------------------------------------ 機構端 ------------------------------------------

// region ------------------------------------------ 專業人士端 ------------------------------------------

func TestProfessionalJobSearch(t *testing.T) {
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/jobs/actions/search",
		UserId:           42,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "搜索",
		Cases: []xtest.TestCase{
			{
				SubName:           "按距離排序",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobSearchForProfessionalReq{
					//CenterLocationLat: decimal.NewFromFloat(-33.865143),
					//CenterLocationLng: decimal.NewFromFloat(151.209900),
					LocationState: "",
					LocationCity:  "",
					BeginDate:     "",
					EndDate:       "",
					WeekdayType:   "",
					ShiftTimeType: "",
				},
				PageSet: xresp.PageSet{
					PageIndex: 1,
					PageSize:  30,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 測試語言篩選功能
func TestProfessionalJobSearchWithLanguageFilter(t *testing.T) {
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/jobs/actions/search",
		UserId:           42, // 使用一個有語言能力設定的專業人士
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "語言篩選搜索",
		Cases: []xtest.TestCase{
			{
				SubName:           "測試語言篩選功能",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobSearchForProfessionalReq{
					LocationState: "",
					LocationCity:  "",
					BeginDate:     "",
					EndDate:       "",
					WeekdayType:   "",
					ShiftTimeType: "",
				},
				PageSet: xresp.PageSet{
					PageIndex: 1,
					PageSize:  30,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalJobInquire(t *testing.T) {
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/jobs/actions/inquire",
		UserId:           15,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ProfessionalJobInquireReq{
					JobId: 96,
				},
			},
			{
				SubName:           "記錄不存在",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.ProfessionalJobInquireReq{
					JobId: 99999, // 假設不存在的Id
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalJobApply(t *testing.T) {
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/jobs/actions/apply",
		UserId:           15,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "申請",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ProfessionalJobApplyReq{
					JobId: 131,
				},
			},
			{
				SubName:           "記錄不存在",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.ProfessionalJobApplyReq{
					JobId: 99999, // 假設不存在的Id
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestJobUpdateCalendarNote(t *testing.T) {
	// 構建測試用例
	facilityId := uint64(7)
	test := xtest.Test{
		Url:              programPath + "/v1/facility/jobs/actions/update-calendar-note",
		UserId:           16,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "更新日曆備註",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常更新",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobUpdateCalendarNoteReq{
					FacilityId: facilityId,
					JobId:      46,
					Remark:     "這是一個測試備註",
				},
			},
			{
				SubName:           "工作不存在",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobUpdateCalendarNoteReq{
					FacilityId: facilityId,
					JobId:      99999,
					Remark:     "這是一個測試備註",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityJobAcceptInvite(t *testing.T) {
	user := getTestUser(16)
	test := xtest.Test{
		Url:              programPath + "/v1/facility/jobs/actions/invite",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "邀請專業人士",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobInviteProfessionalReq{
					FacilityId:       user.FacilityId,
					JobId:            197,
					JobApplicationId: 13,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityJobWithdrawInvite(t *testing.T) {
	user := getTestUser(16)
	test := xtest.Test{
		Url:              programPath + "/v1/facility/jobs/actions/withdraw-invite",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "撤回邀請",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobWithdrawInviteReq{
					FacilityId:       user.FacilityId,
					JobId:            197,
					JobApplicationId: 13,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// endregion ------------------------------------------ 專業人士端 ------------------------------------------

// region ------------------------------------------ 管理端 ------------------------------------------

func TestJobSystemList(t *testing.T) {
	// 構建測試用例
	user := getTestUser(1)
	test := xtest.Test{
		Url:              programPath + "/v1/system/jobs",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "招聘中",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobListReq{
					FacilityId: 7,
				},
				PageSet: xresp.PageSet{
					PageIndex: 1,
					PageSize:  10,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestJobSystemSearch(t *testing.T) {
	// 構建測試用例
	facilityId := uint64(7)
	test := xtest.Test{
		Url:              programPath + "/v1/system/jobs/actions/search",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "搜索",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobSearchReq{
					FacilityId: facilityId,
					Limit:      10,
					Progress:   services.JobProgressUpcoming,
				},
			},
			{
				SubName:           "按職位名稱搜索",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobSearchReq{
					FacilityId:         facilityId,
					PositionProfession: model.JobPositionProfessionEnrolledNurse,
					Limit:              10,
					Progress:           services.JobProgressInProgress,
				},
			},
			{
				SubName:           "按地點搜索",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobSearchReq{
					FacilityId:        facilityId,
					ServiceLocationId: 1,
					Limit:             10,
					Progress:          services.JobProgressInProgress,
				},
			},
			{
				SubName:           "按時間範圍搜索",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobSearchReq{
					FacilityId: facilityId,
					BeginTime:  "2023-01-01 00:00:00",
					EndTime:    "2023-12-31 23:59:59",
					Limit:      10,
					Progress:   services.JobProgressInProgress,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestJobSystemInquire(t *testing.T) {
	// 查詢一個存在的工作職位Id
	var jobId uint64 = 9
	facilityId := uint64(7)

	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/jobs/actions/inquire",
		UserId:           11,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobInquireReq{
					FacilityId: facilityId,
					JobId:      jobId,
				},
			},
			{
				SubName:           "記錄不存在",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobInquireReq{
					FacilityId: facilityId,
					JobId:      99999, // 假設不存在的Id
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// endregion ------------------------------------------ 管理端 ------------------------------------------
