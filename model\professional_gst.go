package model

import (
	"time"

	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xmodel/xtype"
)

// 專業人士
type ProfessionalGST struct {
	Id                uint64         `json:"id" gorm:"primary_key"`
	UserId            uint64         `json:"userId" gorm:"index:user_idx;not null"`                        // 用戶Id
	ProfessionalId    uint64         `json:"professionalId" gorm:"index:professional_idx;not null"`        // 專業人士Id
	EffectiveFrom     xtype.Date     `swagger:"string" json:"effectiveFrom" gorm:"type:date;not null"`     // 生效時間
	EffectiveTo       xtype.NullDate `swagger:"string" json:"effectiveTo" gorm:"type:date"`                // 失效時間
	RecordUpdatedDate xtype.Date     `swagger:"string" json:"recordUpdatedDate" gorm:"type:date;not null"` // ABN更新日期
	UpdateTime        time.Time      `json:"updateTime" gorm:"type:datetime(0);not null"`                  // 更新時間(UTC)
	xmodel.Model
}

func (p *ProfessionalGST) TableName() string {
	return "professional_gst"
}

func (p *ProfessionalGST) SwaggerDescription() string {
	return "專業人士GST"
}
