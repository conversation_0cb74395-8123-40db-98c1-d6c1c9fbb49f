package routers

import (
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

var testProfessionalId uint64

func TestProfessionalProfileInit(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/professional-profiles/actions/init",
		UserId:           8,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "初始化",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ProfessionalProfileInitReq{
					FirstName:        "Gapen",
					LastName:         "G",
					Phone:            "123456789",
					PermissionToWork: "Y",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalProfileInquire(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/professional-profiles/actions/inquire",
		UserId:           15,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalProfileEdit(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/professional-profiles/actions/edit",
		UserId:           9,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "編輯",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ProfessionalProfileEditReq{
					ProfessionalId: 1,
					EditType:       services.ProfessionalProfileEditTypeRegistrationAndCertification,

					//PersonalInformation: &services.ProfessionalProfileEditPersonalInformationReq{
					//	FirstName:                    "Gapen",
					//	LastName:                     "G",
					//	PermissionToWork:             "AUSTRALIAN_CITIZEN",
					//	Profession:                   "MEDICAL_PRACTITIONER",
					//	Gender:                       "MALE",
					//	DateOfBirth:                  "1990-01-01",
					//	Address:                      "",
					//	LocationLat:                  decimal.Zero,
					//	LocationLng:                  decimal.Zero,
					//	PreferredState:               "",
					//	PreferredLocality:            "",
					//	DistanceWithin:               decimal.Zero,
					//	PhotoFileId:                  0,
					//	EmergencyContactFirstName:    "",
					//	EmergencyContactLastName:     "",
					//	EmergencyContactPhone:        "",
					//	EmergencyContactRelationship: "",
					//},
					// WorkPreferencesAndExperience: &services.ProfessionalProfileEditWorkPreferencesAndExperienceReq{
					// 	PreferredGrade:                     "",
					// 	ExperienceLevel:                    "",
					// 	PreferredSpecialities:              "",
					// 	PersonalCareWorkQualificationFiles: []uint64{},
					// 	Experiences:                        []model.ProfessionalExperience{},
					// 	CompletedStudiesInLastThreeYears:   "",
					// 	Qualification:                      "",
					// 	QualificationEndDate:               "",
					// 	References:                         []model.ProfessionalReference{},
					// },
					RegistrationAndCertification: &services.ProfessionalProfileEditRegistrationAndCertificationReq{
						Files: map[string][]model.ProfessionalProfileFile{
							model.ProfessionalFileCodeIndemnityInsuranceCertificate: {
								{
									ProfessionalFileIds: []uint64{224},
									FileCode:            model.ProfessionalFileCodeIndemnityInsuranceCertificate,
									ExpiryDate:          "2025-04-11",
									Number:              "",
									Description:         "",
								},
							},
						},
					},
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalProfileProgress(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/professional-profiles/actions/progress",
		UserId:           9,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalProfileSubmit(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/professional-profiles/actions/submit",
		UserId:           8,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "提交",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
			},
		},
	}
	xtest.RunTests(t, test)
}

// 檢查ABN
func TestProfessionalProfileCheckAbn(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/professional-profiles/actions/check-abn",
		UserId:           8,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "檢查ABN",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ProfessionalProfileCheckAbnReq{
					AbnNumber: "***********",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 對比
func TestProfessionalProfileCompare(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/professional/professional-profiles/actions/compare",
		UserId:           9,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "對比",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ProfessionalProfileCompareReq{
					CurrentProfessionalId: 13,
					NewProfessionalId:     2,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 查詢付款資料
func TestProfessionalProfilePaymentDetail(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/professional-profiles/actions/payment-detail",
		UserId:           8,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
			},
		},
	}
	xtest.RunTests(t, test)
}

// 更新付款資料
func TestProfessionalProfileUpdatePaymentDetail(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/professional-profiles/actions/update-payment-detail",
		UserId:           8,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "更新付款資料",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ProfessionalProfileUpdatePaymentDetailReq{
					BankAccountName:   "Bank Account Name",
					BankStateBranch:   "Bank State Branch",
					BankAccountNumber: "Bank Account Number",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 專業人士資料列表測試
func TestProfessionalProfileListSystemRouter(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/professional-profiles",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "專業人士資料列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ProfessionalProfileListReq{
					DataType: model.ProfessionalDataTypeApproved,
					Status:   model.ProfessionalStatusApproved,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 專業人士資料查詢測試
func TestProfessionalProfileInquireSystemRouter(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/professional-profiles/actions/inquire",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "專業人士資料查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ProfessionalProfileInquireReq{
					ProfessionalId: 1,
				},
			},
		},
	}
	xtest.RunTests(t, test)

}

// 專業人士資料審核測試
func TestProfessionalProfileApproveSystemRouter(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/professional-profiles/actions/approve",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "專業人士資料審核",
		Cases: []xtest.TestCase{
			{
				SubName:           "審核",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ProfessionalProfileApproveReq{
					ProfessionalId: 2,
					Approve:        "Y",
					RejectReason:   "",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalReferenceFormInquire(t *testing.T) {
	test := xtest.Test{
		Url:        programPath + "/v1/professional/reference-forms/actions/preview",
		Method:     xtest.Get,
		ParamsType: xtest.Query,
		Name:       "專業人士推薦人查看圖片",
		Cases: []xtest.TestCase{
			{
				SubName:           "查看",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ProfessionalReferenceFormFileGetPreviewReq{
					ProfessionalId: 2,
					FormUuid:       "03cd1282-8c3d-4bcc-a019-43770ddca6cd",
					MatchUuid:      "8298c3a9-685b-4099-acea-55f482337874",
					Uuid:           "08555499-66d8-472b-bb5d-584b347eae559e79d39c-d06d-4221-8198-d62c2b5b86d7",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalReferenceFormInquireSystemRouter(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/professional-profiles/actions/inquire-reference",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "專業人士推薦人查看圖片",
		Cases: []xtest.TestCase{
			{
				SubName:           "查看",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ProfessionalReferenceFormSystemInquireReq{
					ProfessionalId: 2,
					FormUuid:       "03cd1282-8c3d-4bcc-a019-43770ddca6cd",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
