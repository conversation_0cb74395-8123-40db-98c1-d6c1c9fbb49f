package services

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgeoip2"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xhermes"
	"github.com/Norray/xrocket/xi18n"
	"github.com/Norray/xrocket/xjwt"
	"github.com/Norray/xrocket/xmail"
	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xredis"
	"github.com/Norray/xrocket/xtool"
	"github.com/gin-gonic/gin"
	"github.com/matcornic/hermes/v2"
	"github.com/mssola/user_agent"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

var UserDeviceService = new(userDeviceService)

type userDeviceService struct{}

type UserDevicesApplyReq struct {
	Username     string `json:"username" binding:"required_without=RefreshToken"` // 賬號
	Password     string `json:"password" binding:"required_without=RefreshToken"` // 密碼
	Device       string `json:"device" binding:"required,oneof=WEB APP"`          // Device WEB或者APP
	System       string `json:"system" binding:"required,oneof=SYSTEM USER"`      // System 管理端 SYSTEM 用戶端 USER
	RefreshToken string `json:"refreshToken"`                                     // 如果傳了，則無視 username 和 password 的值
}

type UserDevicesApplyResp struct {
	Key        string `json:"key"`        // 回執
	ExpiredMin int    `json:"expiredMin"` // 過期分鐘數
	Email      string `json:"email"`      // Email
}

const userDevicesApplyRecordCacheKey = "verify:user_device_apply"

// userDevicesApplyRecordCache redis緩存
type userDevicesApplyRecordCache struct {
	Key    string `json:"key"`    // 回執
	Code   string `json:"code"`   // 驗證碼
	Times  uint32 `json:"times"`  // 驗證次數
	UserId uint64 `json:"userId"` // 用戶ID
	Device string `json:"device"` // Device WEB或者APP
}

func (s *userDeviceService) UserDeviceApply(db *gorm.DB, req UserDevicesApplyReq, lang string) (UserDevicesApplyResp, *i18n.Message, error) {
	var resp UserDevicesApplyResp
	var err error
	var userTypeArr []string // 各系統允許請求的用戶類型
	userTypeArr, exist := systemForLogin[req.System]
	if !exist {
		return resp, nil, errors.New("invalid system")
	}
	var user xmodel.User
	var builder *gorm.DB
	if req.RefreshToken != "" {
		cacheKey := fmt.Sprintf(cacheRefreshTokenUserInfo, req.RefreshToken)
		var cache RefreshTokenUserInfoCache
		exist, err = xredis.GetStruct(db.Statement.Context, cacheKey, &cache)
		if err != nil {
			return resp, nil, err
		}
		if !exist {
			return resp, &canNotFoundRefreshMsg, nil
		}
		builder = db.Where("id = ?", cache.UserId)
	} else {
		pw := xtool.EncodeStringWithSalt(req.Password, xconfig.AppConf.PasswordSalt)
		builder = db.Where("BINARY username = ?", req.Username).
			Where("password = ?", pw)
	}
	if len(userTypeArr) > 0 {
		builder = builder.Where("user_type IN (?)", userTypeArr)
	}
	err = builder.
		Where("status = ?", "ENABLE").
		First(&user).Error
	if xgorm.IsSqlErr(err) {
		return resp, nil, err
	}
	if xgorm.IsNotFoundErr(err) {
		if req.RefreshToken == "" {
			msg := i18n.Message{
				ID:    "user_devices.apply.failed",
				Other: "Incorrect account or password.",
			}
			return resp, &msg, nil
		} else {
			msg := i18n.Message{
				ID:    MessageIDOAuthLoginFailed,
				Other: "Unable to sign in this account.",
			}
			return resp, &msg, nil
		}
	}
	if user.Email == "" {
		msg := i18n.Message{
			ID:    "user_devices.apply.no_email",
			Other: "Please setup your email first.",
		}
		return resp, &msg, nil
	}
	// 緩存到redis
	key := strings.ToLower(uuid.NewV4().String())
	code, err := xtool.GenRandIntWithZeroDigit(999999, 6)
	if err != nil {
		return resp, nil, err
	}
	record := userDevicesApplyRecordCache{
		Key:    key,
		Code:   code,
		UserId: user.Id,
		Device: req.Device,
	}
	cacheKey := fmt.Sprintf("%s:%s:%s:%s", userDevicesApplyRecordCacheKey, req.Device, user.Username, key)
	err = xredis.SetStruct(context.Background(), cacheKey, record, time.Minute*10)
	if err != nil {
		return resp, nil, err
	}

	// 構建電郵內容
	emailGreeting := i18n.Message{
		ID:    "user_devices.apply.email.greeting",
		Other: "Hi",
	}
	emailSignature := i18n.Message{
		ID:    "user_devices.apply.email.signature",
		Other: "Thanks",
	}
	emailIntros := i18n.Message{
		ID:    "user_devices.apply.email.intros",
		Other: "A sign in attempt requires further verification because we did not recognize your device. To complete the sign in, enter the verification code on the unrecognized device.",
	}
	emailInstructions := i18n.Message{
		ID:    "user_devices.apply.email.instructions",
		Other: "The following is the verification code, please fill in within 10 minutes.",
	}
	emailOutros := i18n.Message{
		ID:    "user_devices.apply.email.outros",
		Other: "If you did not attempt to sign in to your account, your password may be compromised. You may need to create a new, strong password for your account.",
	}
	emailSubject := i18n.Message{
		ID:    "user_devices.apply.email.subject",
		Other: "Please verify your device",
	}
	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLang(lang, &emailGreeting),
		Signature: xi18n.LocalizeWithLang(lang, &emailSignature),
		Name:      "",
		Intros: []string{
			xi18n.LocalizeWithLang(lang, &emailIntros),
		},
		Actions: []hermes.Action{
			{
				Instructions: xi18n.LocalizeWithLang(lang, &emailInstructions),
				InviteCode:   code,
			},
		},
		Outros: []string{
			xi18n.LocalizeWithLang(lang, &emailOutros),
		},
	}
	content, err := xhermes.GenerateHTML(HermesDefaultProduct(medicCrewLogoUrl), body)
	if err != nil {
		return resp, nil, err
	}
	err = xmail.SendMail(user.Email, fmt.Sprintf("[%s] %s", xconfig.MailConf.Name, xi18n.LocalizeWithLang(lang, &emailSubject)), "text/html", content)
	if err != nil {
		return resp, nil, err
	}
	resp.ExpiredMin = 10
	resp.Key = key
	resp.Email = s.starTheEmail(user.Email)
	return resp, nil, nil
}

func (s *userDeviceService) starTheEmail(email string) string {
	strArr := strings.Split(email, "@")
	if len(strArr) == 2 {
		name := strArr[0]
		if len(name) <= 2 {
			return email
		}
		return name[:2] + strings.Repeat("*", len(name)-2) + "@" + strArr[1]
	} else {
		return ""
	}
}

type UserDevicesVerifyReq struct {
	Username string `json:"username" binding:"required"`             // 賬號
	Key      string `json:"key" binding:"required"`                  // 回執
	Code     string `json:"code" binding:"required"`                 // 驗證碼
	Device   string `json:"device" binding:"required,oneof=WEB APP"` // Device WEB或者APP
}

type UserDevicesVerifyResp struct {
	Dk     string `json:"dk"` //Device Key
	Secret string `json:"secret"`
}

func (s *userDeviceService) UserDeviceVerify(c *gin.Context, db *gorm.DB, req UserDevicesVerifyReq) (UserDevicesVerifyResp, *i18n.Message, error) {
	record := userDevicesApplyRecordCache{}
	var resp UserDevicesVerifyResp
	cacheKey := fmt.Sprintf("%s:%s:%s:%s", userDevicesApplyRecordCacheKey, req.Device, req.Username, req.Key)
	exist, err := xredis.GetStruct(context.Background(), cacheKey, &record)
	if err != nil {
		return resp, nil, err
	}
	if !exist {
		msg := i18n.Message{
			ID:    "user_devices.verify.code_does_not_exist",
			Other: "The verification has expired, please resend the verification code.",
		}
		return resp, &msg, nil
	}
	if record.Key != req.Key || record.Code != req.Code {
		if record.Times >= 4 {
			//刪除key
			err = xredis.DeleteKey(context.Background(), cacheKey)
			if err != nil {
				return resp, nil, err
			}
			msg := i18n.Message{
				ID:    "user_devices.verify.too_much_try",
				Other: "Too many attempts, please try again later to resend the verification code.",
			}
			return resp, &msg, nil
		} else {
			record.Times = record.Times + 1
			//更新次數
			err = xredis.UpdateStruct(context.Background(), cacheKey, record, time.Minute*15)
			if err != nil {
				return resp, nil, err
			}
			msg := i18n.Message{
				ID:    "user_devices.verify.incorrect_code",
				Other: "Incorrect verification code.",
			}
			return resp, &msg, nil
		}
	}
	// 通過，開始發放dk
	dk := strings.ToLower(uuid.NewV4().String())
	deviceSecret := strings.ToLower(uuid.NewV4().String())
	ag := c.GetHeader("User-Agent")
	ua := user_agent.New(ag)
	name, _ := ua.Browser()
	os := ua.OS()
	nowTimestamp := time.Now().Unix()
	clientIP := c.ClientIP()
	userDevice := xmodel.UserDevice{
		UserId:         record.UserId,
		Device:         record.Device,
		DeviceKey:      dk,
		DeviceSecret:   deviceSecret,
		Name:           name,
		OS:             os,
		UserAgent:      ag,
		SignInTime:     nowTimestamp,
		SignInIP:       clientIP,
		LastActiveTime: nowTimestamp,
		LastActiveIP:   clientIP,
		Status:         xmodel.UserDeviceStatusEnable,
	}
	if err = db.Create(&userDevice).Error; err != nil {
		return resp, nil, err
	}
	// 刪除key
	_ = xredis.DeleteKey(c, cacheKey)
	resp.Dk = dk
	resp.Secret = deviceSecret
	return resp, nil, nil
}

func (s *userDeviceService) UserDeviceRevoke(db *gorm.DB, userId uint64, dk string) error {
	err := db.
		Model(&xmodel.UserDevice{}).
		Where("user_id = ?", userId).
		Where("device_key = ?", dk).
		Update("status", xmodel.UserDeviceStatusDisable).Error
	if err != nil {
		return err
	}
	_ = xjwt.DeleteUserDeviceKeyCache(context.Background(), fmt.Sprintf("%d", userId), dk)
	return nil
}

type UserDevicesReq struct {
	UserName            string `form:"userName" binding:"omitempty"`                    // 用戶姓名
	SignInIP            string `form:"signInIP" binding:"omitempty"`                    // 首次登入的ip
	LastActiveIP        string `form:"lastActiveIP" binding:"omitempty"`                // 最後一次登入的ip
	LastActiveSystem    string `form:"lastActiveSystem" binding:"omitempty"`            // 最後一次登入的系統 CONSOLE,ACCOUNTING,MANAGEMENT,AUDIT,SALES
	Name                string `form:"name" binding:"omitempty"`                        // 設備名
	OS                  string `form:"os" binding:"omitempty"`                          // 系統名
	SignInBeginTime     int64  `form:"signInBeginTime" binding:"omitempty"`             // 首次登入時間-開始(時間戳)
	SignInEndTime       int64  `form:"signInEndTime" binding:"omitempty"`               // 首次登入時間-結束(時間戳)
	LastActiveBeginTime int64  `form:"lastActiveBeginTime" binding:"omitempty"`         // 最近一次登入時間-開始(時間戳)
	LastActiveEndTime   int64  `form:"lastActiveEndTime" binding:"omitempty"`           // 最近一次登入時間-結束(時間戳)
	Online              string `form:"online" binding:"omitempty,oneof=Y N"`            // 是否在線 Y,N
	Status              string `form:"status" binding:"omitempty,oneof=ENABLE DISABLE"` // 篩選是否已經過期的設備 可傳ENABLE,DISABLE
	PageIndex           uint32 `form:"pageIndex" binding:"omitempty"`                   // 頁數
	PageSize            uint32 `form:"pageSize" binding:"omitempty"`                    // 每頁條目數
}

type UserDevicesResp struct {
	DeviceId         uint64 `json:"deviceId"`         // 設備ID
	UserId           uint64 `json:"userId"`           // 用戶ID
	UserName         string `json:"userName"`         // 用戶姓名
	UserType         string `json:"userType"`         // 用戶類型
	Device           string `json:"device"`           // 設備類型
	Name             string `json:"name"`             // 設備名
	OS               string `json:"os"`               // 系統名
	SignInIP         string `json:"signInIP"`         // 首次登入的ip
	SignInTime       int64  `json:"signInTime"`       // 首次登入時間
	LastActiveIP     string `json:"lastActiveIP"`     // 最後一次登入的ip
	LastActiveTime   int64  `json:"lastActiveTime"`   // 最後一次登入時間
	LastActiveSystem string `json:"lastActiveSystem"` // 最後一次登入的系統
	Status           string `json:"status"`           // 狀態
	SignInCountry    string `json:"signInCountry"`    // 首次登入國家
	SignInCity       string `json:"signInCity"`       // 首次登入城市
	LastCountry      string `json:"lastCountry"`      // 最後登入國家
	LastCity         string `json:"lastCity"`         // 最後登入城市
}

func (s *userDeviceService) List(db *gorm.DB, req UserDevicesReq, pageSet *xresp.PageSet) ([]UserDevicesResp, error) {
	var resp []UserDevicesResp
	builder := db.Table("user_device ud").
		Select([]string{
			"ud.id AS device_id",
			"ud.user_id",
			"u.name AS user_name",
			"u.user_type",
			"ud.device",
			"ud.name",
			"ud.os",
			"ud.sign_in_ip",
			"ud.sign_in_time",
			"ud.last_active_ip",
			"ud.last_active_time",
			"ud.last_active_system",
			"ud.status",
		}).
		Joins("LEFT JOIN user u ON u.id = ud.user_id")

	// IP搜索
	if req.LastActiveIP != "" {
		builder = builder.Where("ud.last_active_ip LIKE ?", xgorm.EscapeLikeWithWildcards(req.LastActiveIP))
	}
	if req.SignInIP != "" {
		builder = builder.Where("ud.sign_in_ip LIKE ?", xgorm.EscapeLikeWithWildcards(req.SignInIP))
	}

	// 上次時間
	if req.LastActiveBeginTime != 0 {
		builder = builder.Where("ud.last_active_time >= ?", req.LastActiveBeginTime)
	}
	if req.LastActiveEndTime != 0 {
		builder = builder.Where("ud.last_active_time <= ?", req.LastActiveEndTime)
	}

	// 登入時間
	if req.SignInEndTime != 0 {
		builder = builder.Where("ud.sign_in_time <= ?", req.SignInEndTime)
	}
	if req.SignInBeginTime != 0 {
		builder = builder.Where("ud.sign_in_time >= ?", req.SignInBeginTime)
	}

	if req.Name != "" {
		builder = builder.Where("ud.name LIKE ?", xgorm.EscapeLikeWithWildcards(req.Name))
	}
	if req.UserName != "" {
		builder = builder.Where("u.name LIKE ?", xgorm.EscapeLikeWithWildcards(req.UserName))
	}
	if req.OS != "" {
		builder = builder.Where("ud.os LIKE ?", xgorm.EscapeLikeWithWildcards(req.OS))
	}
	if req.Status != "" {
		builder = builder.Where("ud.status = ?", req.Status)
	}
	if req.LastActiveSystem != "" {
		builder = builder.Where("ud.last_active_system = ?", req.LastActiveSystem)
	}
	if req.Online != "" {
		if req.Online == "Y" {
			builder = builder.Where("ud.last_active_time >= ?", time.Now().Add(-time.Minute*5).Unix())
		} else if req.Online == "N" {
			builder = builder.Where("ud.last_active_time < ?", time.Now().Add(-time.Minute*5).Unix())
		}
	}

	err := builder.Scopes(xresp.Paginate(pageSet)).
		Order("ud.last_active_time DESC").
		Find(&resp).Error
	if err != nil {
		return resp, err
	}

	for i := range resp {
		resp[i].SignInCountry, resp[i].SignInCity, _ = xgeoip2.GetCountryAndCityInfo(resp[i].SignInIP)
		resp[i].LastCountry, resp[i].LastCity, _ = xgeoip2.GetCountryAndCityInfo(resp[i].LastActiveIP)
	}

	return resp, nil
}

type ChangeDeviceStatusReq struct {
	DeviceId uint64 `json:"deviceId"`
	Status   string `json:"status" binding:"required,oneof=ENABLE DISABLE"`
}

func (s *userDeviceService) ChangeStatus(nc xapp.NGinCtx, db *gorm.DB, req ChangeDeviceStatusReq) error {
	var device xmodel.UserDevice
	var err error
	if err = db.Model(&device).Where("id = ?", req.DeviceId).Updates(map[string]interface{}{"status": req.Status}).Error; xgorm.IsSqlErr(err) {
		return err
	}
	// 清空緩存,使用用戶立即下線
	if err = xjwt.DeleteUserDeviceKeyCache(nc.C, strconv.FormatUint(device.UserId, 10), device.DeviceKey); err != nil {
		return err
	}
	return nil
}
