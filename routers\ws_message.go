package routers

import (
	v1FacilityApi "github.com/Norray/medic-crew/api/v1/facility_api"
	v1ProfessionalApi "github.com/Norray/medic-crew/api/v1/professional_api"
	"github.com/gin-gonic/gin"
)

func wsMessageProfessionalRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/professional", handlers...)
	{
		wsGroup := r.Group("/ws")
		{
			con := v1ProfessionalApi.NewWsMessageController()
			wsGroup.GET("/actions/connect", con.Connect)
			wsGroup.GET("/sessions", con.SessionList)
			wsGroup.GET("/messages", con.MessageList)
			wsGroup.GET("/actions/unread-count", con.GetUnreadCount)
		}
	}
}

func wsMessageFacilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility", handlers...)
	{
		wsGroup := r.Group("/ws")
		{
			con := v1FacilityApi.NewWsMessageController()
			wsGroup.GET("/actions/connect", con.Connect)
			wsGroup.GET("/sessions", con.SessionList)
			wsGroup.GET("/messages", con.MessageList)
			wsGroup.GET("/actions/unread-count", con.GetUnreadCount)
		}
	}
}
