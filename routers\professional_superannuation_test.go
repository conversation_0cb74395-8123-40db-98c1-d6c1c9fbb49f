package routers

import (
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

func TestProfessionalSuperannuationEdit(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/professional/superannuation/actions/edit",
		UserId:           76,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "编辑退休金信息",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常编辑（草稿状态）",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ProfessionalSuperannuationEditReq{
					ProfessionalSuperannuationId: 1,
					DataType:                     model.ProfessionalSuperannuationDataTypeDraft,
					SuperannuationType:           model.ProfessionalSuperannuationTypeDefaultSuperFund,
					FullName:                     "felix lili",
					TaxFileNumber:                "TFN123456789",
					DeclarationConfirmed:         "1",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
