package middleware

import (
	"context"
	"fmt"
	"time"

	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xredis"
)

type userSub struct {
	Sub string `json:"sub"`
}

func GetUserRoleCache(ctx context.Context, userId string) ([]string, error) {
	key := fmt.Sprintf("cache:user_role:%s", userId)
	subs := make([]string, 0)
	var err error
	var exist bool
	exist, err = xredis.GetStruct(ctx, key, &subs)
	if err != nil {
		return subs, err
	}
	if exist {
		return subs, nil
	}
	//查數據庫
	var userSubs []userSub
	err = xgorm.DB.WithContext(ctx).
		Select("DISTINCT a.code AS sub").
		Table("user_role ur").
		Joins("JOIN role r ON ur.role_id = r.id").
		Joins("JOIN role_action ra ON r.id = ra.role_id").
		Joins("JOIN action a ON ra.action_id = a.id").
		Where("ur.user_id = ?", userId).
		Find(&userSubs).Error
	if xgorm.IsSqlErr(err) {
		return []string{}, err
	}
	if xgorm.IsSqlErr(err) {
		return []string{}, err
	}
	subsMap := make(map[string]bool)
	for _, v := range userSubs {
		if v.Sub != "" {
			subsMap[v.Sub] = true
		}
	}
	for k := range subsMap {
		subs = append(subs, k)
	}
	// 加上用戶類型
	var user xmodel.User
	err = xgorm.DB.WithContext(ctx).Where("id = ?", userId).First(&user).Error
	if err != nil {
		return []string{}, err
	}
	subs = append(subs, user.UserType)

	err = xredis.SetStruct(ctx, key, subs, time.Minute*1)
	if err != nil {
		return subs, err
	}
	return subs, nil
}
