
<style>
    #player-wrapper {
        position: relative;
        width: 640px;
        height: 360px;
    }

    #custom-overlay {
        display: none; /* 初始隱藏 */
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.9);
        color: white;
        font-size: 20px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 10;
    }

    #custom-overlay button {
        margin-top: 20px;
        padding: 10px 20px;
        background: #ff0000;
        border: none;
        color: white;
        cursor: pointer;
    }
    #end-screen {
        display: none; /* 一開始隱藏 */
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.9);
        color: white;
        font-size: 20px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    #end-screen button {
        margin-top: 20px;
        padding: 10px 20px;
        background: #ff0000;
        border: none;
        color: white;
        cursor: pointer;
    }
</style>
<div id="player-wrapper">
    <div id="player"></div>
    <div id="custom-overlay">
        <p>🎉 請完整觀看影片！</p>
        <p>🎉 影片播放結束！</p>
        <button onclick="replayVideo()">再看一次</button>
    </div>
</div>

<script src="https://www.youtube.com/iframe_api"></script>
<script>
    var player;
    var lastTime = 0;
    var checkInterval;
    var hasWatchedToEnd = false;

    // 初始化 YouTube 播放器
    function onYouTubeIframeAPIReady() {
        player = new YT.Player('player', {
            width: 640,
            height: 360,
            videoId: 'P0pGXQUo6sc', // 換成你的 YouTube 影片 ID
            playerVars: {
                controls: 0,   // ✅ 顯示進度條
                disablekb: 1,  // 禁用鍵盤操作 (禁止左右鍵快進)
                modestbranding: 1, // 隱藏 YouTube logo (有限度)
                rel: 0,        // 播完不要顯示推薦影片
            },
            events: {
                'onStateChange': onPlayerStateChange
            }
        });
    }

  function onPlayerStateChange(event) {
    if (event.data == YT.PlayerState.PLAYING) {
        // 影片播放 → 隱藏覆蓋層
        document.getElementById("custom-overlay").style.display = "none";
      clearInterval(checkInterval);
      checkInterval = setInterval(function() {
        var current = player.getCurrentTime();
        // 若使用者拖到更後面 → 拉回去
        if (current > lastTime + 2) {
          player.seekTo(lastTime, true);
            console.log('快進');
        } else {
          lastTime = current;
            console.log('正常播放');
        }
      }, 1000);
    } else if (event.data == YT.PlayerState.ENDED) {
      hasWatchedToEnd = true;
      console.log("✅ 用戶完整看完影片");document.getElementById("end-screen").style.display = "flex"; // 顯示自家結束畫面
      clearInterval(checkInterval);
        // 暫停或結束 → 顯示覆蓋層
        document.getElementById("custom-overlay").style.display = "flex";
    } else if (event.data == YT.PlayerState.PAUSED) {
      clearInterval(checkInterval);
        // 暫停或結束 → 顯示覆蓋層
        document.getElementById("custom-overlay").style.display = "flex";
    }
  }

    function replayVideo() {
        document.getElementById("custom-overlay").style.display = "none";
        player.seekTo(0);
        player.playVideo();
    }
</script>
