package services

import (
	"fmt"
	"math"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// 申請工作計算得分服務
var JobScoreService = new(jobScoreService)

type jobScoreService struct{}

// 計算專業人士得分 - 專業人士
func (s *jobScoreService) CalculateScore(db *gorm.DB, job model.Job, professional model.Professional) (int32, error) {
	var score int32
	traceId := xgorm.GetDBContextTraceId(db)

	if professional.Profession != job.PositionProfession {
		return score, fmt.Errorf("professional profession %s does not match job position profession %s", professional.Profession, job.PositionProfession)
	}

	// 獲取附加證書數量
	var additionalCertCount int32
	// 從數據庫獲取專業人士的附加證書數量
	profile, err := professional.UnmarshalProfile(professional.ProfileJson)
	if err != nil {
		return score, err
	}
	// 計算附加證書數量
	for _, file := range profile.Files {
		if file.FileCode == model.ProfessionalFileCodeAdditionalCertification {
			additionalCertCount += int32(len(file.ProfessionalFileIds))
		}
	}

	nowTime := time.Now().UTC().Truncate(time.Second)
	nowStr := nowTime.Format(xtool.DateTimeSecA1)

	// 根據不同專業類型計算分數
	switch professional.Profession {
	case model.ProfessionalProfessionMedicalPractitioner:
		// Medical Practitioner
		// 1. Preferred Grade：總共5個級別，從低到高每個等級+5分
		var gradeScore int32
		var preferredGrades []model.Selection
		if err = db.Where("selection_type = ?", model.SelectionTypePreferredGrade).Order("seq").Find(&preferredGrades).Error; err != nil {
			return score, err
		}
		var foundSpeciality model.ProfessionalPreferredSpeciality
		for _, speciality := range profile.PreferredSpecialities {
			if speciality.SubSpeciality == job.Specialisation {
				// 找到本職位對應的專業
				foundSpeciality = speciality
			}
		}
		for _, grade := range preferredGrades {
			if grade.Code == foundSpeciality.Grade {
				gradeScore = grade.Seq * 5
				break
			}
		}
		// 2.檢測 professional 是否經常性主動取消工作
		shiftCompletionScore, err := s.CalculateShiftCompletionAfterJobConfirmationScore(db, nowStr, professional)
		if err != nil {
			return score, err
		}
		// 3. Rating：檢測professional的評價如何，上限15分
		ratingScore, err := s.CalculateRatingScore(db, professional)
		if err != nil {
			return score, err
		}
		// 4. Experience Level(Minimum Classification)：總共7個級別，從低到高每個等級+2分
		var experienceLevelScore int32
		var experienceLevels []model.Selection
		if err = db.Where("selection_type = ?", model.SelectionTypeExperienceLevelMedicalPractitioner).Order("seq").Find(&experienceLevels).Error; err != nil {
			return score, err
		}
		for _, level := range experienceLevels {
			if level.Code == professional.ExperienceLevel {
				experienceLevelScore = level.Seq * 2
				break
			}
		}
		// 5. Availability / Responsiveness：檢測professional是否有海量投遞工作的行為
		responsivenessScore, err := s.CalculateResponsivenessScore(db, professional)
		if err != nil {
			return score, err
		}
		// 6. Worked at Facility Before：檢測professional是否有在該醫院的工作經驗，上限10分
		workHistoryScore, err := s.CalculateWorkedAtFacilityScore(db, nowStr, professional, job.FacilityId)
		if err != nil {
			return score, err
		}

		score = gradeScore + shiftCompletionScore + ratingScore + experienceLevelScore + responsivenessScore + workHistoryScore

		// 按順序打印分數明細日誌
		log.WithField("traceId", traceId).Infof("Medical Practitioner 分數明細 - 專業人士ID: %d, 職位ID: %d - Preferred Grade: %d分, Shift Completion: %d分, Rating: %d分, Experience Level: %d分, Responsiveness: %d分, Work History: %d分, 總分: %d分",
			professional.Id, job.Id, gradeScore, shiftCompletionScore, ratingScore, experienceLevelScore, responsivenessScore, workHistoryScore, score)

	case model.ProfessionalProfessionEnrolledNurse:
		// Enrolled Nurse
		// 1. Experience Level：表示professional的工作經驗，總共4個級別，從低到高每個等級+7分，上限28分
		var experienceLevelScore int32
		var experienceLevels []model.Selection
		if err = db.Where("selection_type = ?", model.SelectionTypeExperienceLevelGeneral).Order("seq").Find(&experienceLevels).Error; err != nil {
			return score, err
		}
		for _, level := range experienceLevels {
			if level.Code == professional.ExperienceLevel {
				experienceLevelScore = level.Seq * 7
			}
		}

		// 2. Endorsed to administer medication：表示professional的是否有這個資格證明，總共2個選項，上限5分
		var endorsedScore int32
		if professional.MedicationEndorsement == model.ProfessionalMedicationEndorsementY {
			endorsedScore = 5
		} else {
			endorsedScore = 0
		}

		// 3. Shift Completion after job confirmation：檢測professional是否經常性主動取消工作
		shiftCompletionScore, err := s.CalculateShiftCompletionAfterJobConfirmationScore(db, nowStr, professional)
		if err != nil {
			return score, err
		}

		// 4. Rating：檢測professional的評價如何，上限15分
		ratingScore, err := s.CalculateRatingScore(db, professional)
		if err != nil {
			return score, err
		}

		// 5. Availability / Responsiveness：檢測professional是否有海量投遞工作的行為，上限16分
		responsivenessScore, err := s.CalculateResponsivenessScore(db, professional)
		if err != nil {
			return score, err
		}

		// 6. Worked at Facility Before：檢測professional是否有在該醫院的工作經驗，上限8分
		workHistoryScore, err := s.CalculateNurseWorkedAtFacilityBeforeScore(db, nowStr, professional, job.FacilityId)
		if err != nil {
			return score, err
		}

		// 7. Additional Certification：每個證書2分，上限8分
		var additionalCertScore int32
		if additionalCertCount > 4 {
			additionalCertScore = 8
		} else {
			additionalCertScore = additionalCertCount * 2
		}

		score = experienceLevelScore + endorsedScore + shiftCompletionScore + ratingScore + responsivenessScore + workHistoryScore + additionalCertScore

		// 按順序打印分數明細日誌
		log.WithField("traceId", traceId).Infof("Enrolled Nurse 分數明細 - 專業人士ID: %d, 職位ID: %d - Experience Level: %d分, Endorsed: %d分, Shift Completion: %d分, Rating: %d分, Responsiveness: %d分, Work History: %d分, Additional Cert: %d分, 總分: %d分",
			professional.Id, job.Id, experienceLevelScore, endorsedScore, shiftCompletionScore, ratingScore, responsivenessScore, workHistoryScore, additionalCertScore, score)

	case model.ProfessionalProfessionRegisteredNurse:
		// Registered Nurse
		// 1. Experience Level：總共5個級別，從低到高每個等級+6分
		var experienceLevelScore int32
		var experienceLevels []model.Selection
		if err = db.Where("selection_type = ?", model.SelectionTypeExperienceLevelRegisteredNurse).Order("seq").Find(&experienceLevels).Error; err != nil {
			return score, err
		}
		for _, level := range experienceLevels {
			if level.Code == professional.ExperienceLevel {
				experienceLevelScore = level.Seq * 6
				break
			}
		}

		// 2. Shift Completion after job confirmation：檢測professional是否經常性主動取消工作
		shiftCompletionScore, err := s.CalculateShiftCompletionAfterJobConfirmationScore(db, nowStr, professional)
		if err != nil {
			return score, err
		}

		// 3. Rating：檢測professional的評價如何，上限20分
		ratingScore, err := s.CalculateRatingScoreForRegisteredNurse(db, professional)
		if err != nil {
			return score, err
		}

		// 4. Availability / Responsiveness：檢測professional是否有海量投遞工作的行為，上限16分
		responsivenessScore, err := s.CalculateResponsivenessScore(db, professional)
		if err != nil {
			return score, err
		}

		// 5. Worked at Facility Before：檢測professional是否有在該醫院的工作經驗，上限8分
		workHistoryScore, err := s.CalculateNurseWorkedAtFacilityBeforeScore(db, nowStr, professional, job.FacilityId)
		if err != nil {
			return score, err
		}

		// 6. Additional Certification：每個證書2分，上限6分
		var additionalCertScore int32
		if additionalCertCount > 3 {
			additionalCertScore = 6
		} else {
			additionalCertScore = additionalCertCount * 2
		}

		score = experienceLevelScore + shiftCompletionScore + ratingScore + responsivenessScore + workHistoryScore + additionalCertScore

		// 按順序打印分數明細日誌
		log.WithField("traceId", traceId).Infof("Registered Nurse 分數明細 - 專業人士ID: %d, 職位ID: %d - Experience Level: %d分, Shift Completion: %d分, Rating: %d分, Responsiveness: %d分, Work History: %d分, Additional Cert: %d分, 總分: %d分",
			professional.Id, job.Id, experienceLevelScore, shiftCompletionScore, ratingScore, responsivenessScore, workHistoryScore, additionalCertScore, score)

	case model.ProfessionalProfessionPersonalCareWorker:
		// Personal Care Worker
		// 1. Experience Level：表示professional的工作經驗，總共4個級別，從低到高每個等級+7分，上限28分
		var experienceLevelScore int32
		var experienceLevels []model.Selection
		if err = db.Where("selection_type = ?", model.SelectionTypeExperienceLevelGeneral).Order("seq").Find(&experienceLevels).Error; err != nil {
			return score, err
		}
		for _, level := range experienceLevels {
			if level.Code == professional.ExperienceLevel {
				experienceLevelScore = level.Seq * 7
				break
			}
		}

		// 2. Shift Completion after job confirmation：檢測professional是否經常性主動取消工作
		shiftCompletionScore, err := s.CalculateShiftCompletionAfterJobConfirmationScore(db, nowStr, professional)
		if err != nil {
			return score, err
		}

		// 3. Rating：檢測professional的評價如何，上限20分
		ratingScore, err := s.CalculateRatingScoreForRegisteredNurse(db, professional) // 使用與 Registered Nurse 相同的評分標準
		if err != nil {
			return score, err
		}

		// 4. Availability / Responsiveness：檢測professional是否有海量投遞工作的行為，上限16分
		responsivenessScore, err := s.CalculateResponsivenessScore(db, professional)
		if err != nil {
			return score, err
		}

		// 5. Worked at Facility Before：檢測professional是否有在該醫院的工作經驗，上限10分
		workHistoryScore, err := s.CalculateWorkedAtFacilityScore(db, nowStr, professional, job.FacilityId) // 使用與 Medical Practitioner 相同的評分標準
		if err != nil {
			return score, err
		}

		// 6. Additional Certification：每個證書2分，上限6分
		var additionalCertScore int32
		if additionalCertCount > 3 {
			additionalCertScore = 6
		} else {
			additionalCertScore = additionalCertCount * 2
		}

		score = experienceLevelScore + shiftCompletionScore + ratingScore + responsivenessScore + workHistoryScore + additionalCertScore

		// 按順序打印分數明細日誌
		log.WithField("traceId", traceId).Infof("Personal Care Worker 分數明細 - 專業人士ID: %d, 職位ID: %d - Experience Level: %d分, Shift Completion: %d分, Rating: %d分, Responsiveness: %d分, Work History: %d分, Additional Cert: %d分, 總分: %d分",
			professional.Id, job.Id, experienceLevelScore, shiftCompletionScore, ratingScore, responsivenessScore, workHistoryScore, additionalCertScore, score)
	}

	return score, nil
}

// CalculateShiftCompletionAfterJobConfirmationScore 檢測professional是否經常性主動取消工作
func (s *jobScoreService) CalculateShiftCompletionAfterJobConfirmationScore(db *gorm.DB, nowStr string, professional model.Professional) (int32, error) {
	var score int32
	var completedJobQty int64
	if err := db.Table("job j").Joins("JOIN job_application ja ON j.id = ja.job_id").
		Where("ja.user_id = ?", professional.UserId).
		Where("ja.status = ?", model.JobApplicationStatusAccept).
		Where("j.end_time <= ?", nowStr).
		Count(&completedJobQty).Error; err != nil {
		return score, err
	}

	var shiftCompletionQty int64
	if err := db.Table("job j").Joins("JOIN job_application ja ON j.id = ja.job_id").
		Where("ja.user_id = ?", professional.UserId).
		Where("ja.status = ?", model.JobApplicationStatusProfessionalCancel).
		Count(&shiftCompletionQty).Error; err != nil {
		return score, err
	}

	shiftPercent := math.Round(float64(completedJobQty) / float64(shiftCompletionQty+completedJobQty) * 100)

	switch {
	case shiftPercent >= 95:
		score = 20
	case shiftPercent >= 85:
		score = 16
	case shiftPercent >= 75:
		score = 12
	case shiftPercent >= 65:
		score = 8
	case shiftPercent >= 5:
		score = 4
	default:
		score = 0
	}

	if shiftCompletionQty <= 1 && score < 10 {
		// 取消次數小於或等於1次時，如果按百分比計算出來得分比10分低時，取10分
		score = 10
	}

	return score, nil
}

// CalculateRatingScore 檢測 professional 的評分
func (s *jobScoreService) CalculateRatingScore(db *gorm.DB, professional model.Professional) (int32, error) {
	var score int32

	// 查詢該 professional 的所有評價
	var feedbacks []model.JobFeedback
	if err := db.Select("jf.*").Table("job_feedback jf").Joins("JOIN professional p ON jf.professional_id = p.id").Where("p.user_id = ?", professional.UserId).Find(&feedbacks).Error; err != nil {
		return score, err
	}

	// 如果沒有任何評價，返回9分
	if len(feedbacks) == 0 {
		return 9, nil
	}

	// 計算平均評分
	var totalRating int
	for _, feedback := range feedbacks {
		totalRating += feedback.Rating
	}

	avgRating := float64(totalRating) / float64(len(feedbacks))

	// 根據平均評分計算分數
	switch {
	case avgRating >= 5.0:
		score = 15 // 5星，15分
	case avgRating >= 4.0:
		score = 12 // 4星-4.9星，12分
	case avgRating >= 3.0:
		score = 9 // 3星-3.9星，9分
	case avgRating >= 2.0:
		score = 6 // 2星-2.9星，6分
	case avgRating >= 1.0:
		score = 3 // 1星-1.9星，3分
	default:
		score = 0 // 0星-0.9星，0分
	}

	return score, nil
}

// CalculateResponsivenessScore 檢測 Availability / Responsiveness 的評分
func (s *jobScoreService) CalculateResponsivenessScore(db *gorm.DB, professional model.Professional) (int32, error) {
	var score int32
	var acceptJobQty int64
	if err := db.Table("job_application ja").
		Where("ja.user_id = ?", professional.UserId).
		Where("ja.status = ? OR ja.status = ? OR ja.status = ? ", model.JobApplicationStatusAccept, model.JobApplicationStatusFacilityCancel, model.JobApplicationStatusProfessionalCancel).
		Count(&acceptJobQty).Error; err != nil {
		return score, err
	}

	var declineQty int64
	if err := db.Table("job_application ja").
		Where("ja.user_id = ?", professional.UserId).
		Where("ja.status = ?", model.JobApplicationStatusDecline).
		Count(&declineQty).Error; err != nil {
		return score, err
	}

	// 邀請但是一直沒有響應最後自動取消了
	var timeoutQty int64
	if err := db.Table("ws_message wm").
		Where("wm.receiver_id = ?", professional.UserId).
		Where("wm.processed = ?", model.WsMessageProcessedY).
		Where("wm.receiver_type = ?", model.WsMessageReceiverTypeProfessional).
		Where("wm.process_result = ?", model.WsMessageProcessResultInvitationTimeout).
		Where("wm.message_type = ?", model.WsMessageTypeJobInvitation).
		Count(&timeoutQty).Error; err != nil {
		return score, err
	}

	if acceptJobQty+declineQty+timeoutQty == 0 {
		score = 8
		return score, nil
	}

	// 計算 Availability / Responsiveness 評分
	responsivenessScore := math.Round(float64(acceptJobQty) / float64(acceptJobQty+declineQty+timeoutQty) * 100)
	switch {
	case responsivenessScore >= 90:
		score = 16
	case responsivenessScore >= 75:
		score = 12
	case responsivenessScore >= 50:
		score = 8
	case responsivenessScore >= 30:
		score = 4
	default:
		score = 0
	}

	return score, nil
}

// CalculateWorkedAtFacilityScore 檢測 professional 是否有在該醫院的工作經驗，上限10分
func (s *jobScoreService) CalculateWorkedAtFacilityScore(db *gorm.DB, nowStr string, professional model.Professional, facilityId uint64) (int32, error) {
	var score int32
	var workedCount int64

	// 查詢該專業人士在特定醫院的工作次數
	// 從檢查是否在該機構有過工作,只要過了job的工作結束時間就當做complete
	if err := db.Table("job j").Joins("JOIN job_application ja ON j.id = ja.job_id").
		Where("ja.user_id = ?", professional.UserId).
		Where("ja.status = ?", model.JobApplicationStatusAccept).
		Where("j.facility_id = ?", facilityId).
		Where("j.end_time <= ?", nowStr).
		Count(&workedCount).Error; err != nil {
		return score, err
	}

	// 根據工作次數計算分數
	switch {
	case workedCount >= 10:
		score = 10 // 10次或以上，10分
	case workedCount >= 7:
		score = 8 // 7-9次，8分
	case workedCount >= 4:
		score = 5 // 4-6次，5分
	case workedCount >= 1:
		score = 3 // 1-3次，3分
	default:
		score = 0 // 0次，0分
	}

	return score, nil
}

// CalculateNurseWorkedAtFacilityBeforeScore 檢測 professional 是否有在該醫院的工作經驗，上限8分
func (s *jobScoreService) CalculateNurseWorkedAtFacilityBeforeScore(db *gorm.DB, nowStr string, professional model.Professional, facilityId uint64) (int32, error) {
	var score int32
	var workedCount int64

	// 查詢該專業人士在特定醫院的工作次數
	// 從檢查是否在該機構有過工作,只要過了job的工作結束時間就當做complete
	if err := db.Table("job j").Joins("JOIN job_application ja ON j.id = ja.job_id").
		Where("ja.user_id = ?", professional.UserId).
		Where("ja.status = ?", model.JobApplicationStatusAccept).
		Where("j.facility_id = ?", facilityId).
		Where("j.end_time <= ?", nowStr).
		Count(&workedCount).Error; err != nil {
		return score, err
	}

	// 根據工作次數計算分數
	switch {
	case workedCount >= 10:
		score = 8 // 10+ 次，8分
	case workedCount >= 7:
		score = 6 // 7 to 9 次，6分
	case workedCount >= 4:
		score = 4 // 4 to 6 次，4分
	case workedCount >= 1:
		score = 2 // 1 to 3 次，2分
	default:
		score = 0 // 0 次，0分
	}

	return score, nil
}

// CalculateRatingScoreForRegisteredNurse 檢測 professional 的評分 (Registered Nurse 專用)
func (s *jobScoreService) CalculateRatingScoreForRegisteredNurse(db *gorm.DB, professional model.Professional) (int32, error) {
	var score int32

	// 查詢該 professional 的所有評價
	var feedbacks []model.JobFeedback
	if err := db.Select("jf.*").Table("job_feedback jf").Joins("JOIN professional p ON jf.professional_id = p.id").Where("p.user_id = ?", professional.UserId).Find(&feedbacks).Error; err != nil {
		return score, err
	}

	// 如果沒有任何評價，返回9分
	if len(feedbacks) == 0 {
		return 9, nil
	}

	// 計算平均評分
	var totalRating int
	for _, feedback := range feedbacks {
		totalRating += feedback.Rating
	}

	avgRating := float64(totalRating) / float64(len(feedbacks))

	// 根據平均評分計算分數
	switch {
	case avgRating >= 5.0:
		score = 20 // 5星，20分
	case avgRating >= 4.0:
		score = 16 // 4星-4.9星，16分
	case avgRating >= 3.0:
		score = 12 // 3星-3.9星，12分
	case avgRating >= 2.0:
		score = 8 // 2星-2.9星，8分
	case avgRating >= 1.0:
		score = 4 // 1星-1.9星，4分
	default:
		score = 0 // 0星-0.9星，0分
	}

	return score, nil
}
