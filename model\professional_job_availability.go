package model

import (
	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xmodel/xtype"
)

const (
	ProfessionalJobAvailabilityFilterTypeAvailableDate    = "AVAILABLE_DATE"    // 可用日期篩選
	ProfessionalJobAvailabilityFilterTypeUnavailableDate  = "UNAVAILABLE_DATE"  // 不可用日期篩選
	ProfessionalJobAvailabilityFilterTypeAvailableWeekday = "AVAILABLE_WEEKDAY" // 星期篩選
)

// ProfessionalJobAvailability 專業人士工作可用性
type ProfessionalJobAvailability struct {
	Id         uint64         `json:"id" gorm:"primary_key"`
	UserId     uint64         `json:"userId" gorm:"index:user_idx;not null"`                    // userId
	FilterType string         `json:"filterType" gorm:"type:varchar(255);not null"`             // 篩選類型
	BeginDate  xtype.NullDate `swaggertype:"string" json:"availableBeginDate" gorm:"type:date"` // 可用開始日期
	EndDate    xtype.NullDate `swaggertype:"string" json:"availableEndDate" gorm:"type:date"`   // 可用結束日期
	Value      string         `json:"value" gorm:"type:varchar(255);not null"`                  // AVAILABLE_WEEKDAY時,記錄1,2,3,4,5,6,7
	xmodel.Model
}

func (p *ProfessionalJobAvailability) TableName() string {
	return "professional_job_availability"
}

func (p *ProfessionalJobAvailability) SwaggerDescription() string {
	return "專業人士工作可用性"
}
