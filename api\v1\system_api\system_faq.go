package system_api

import (
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type FaqController struct{}

func NewFaqController() FaqController {
	return FaqController{}
}

// @Tags Faq
// @Summary 新增常見問題
// @Description
// @Router /v1/system/faqs/actions/create [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FaqCreateReq true "parameter"
// @Success 200 {object} services.FaqCreateResp "Success"
func (con FaqController) Create(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FaqCreateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FaqService.CheckAnswerFileExist(db, req.Answer)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		tx := db.Begin()
		resp, err := services.FaqService.Create(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Faq
// @Summary 獲取常見問題列表
// @Description
// @Router /v1/system/faqs [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FaqListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Success 200 {object} []services.FaqListResp "Success"
func (con FaqController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FaqListReq
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.FaqService.List(db, req, &pageSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Faq
// @Summary 搜索常見問題
// @Description
// @Router /v1/system/faqs/actions/search [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FaqSearchReq true "parameter"
// @Success 200 {object} []services.FaqSearchResp "Success"
func (con FaqController) Search(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FaqSearchReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.FaqService.Search(db, req, model.UserUserTypeSystemAdmin)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Faq
// @Summary 修改常見問題
// @Description
// @Router /v1/system/faqs/actions/edit [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FaqEditReq true "parameter"
// @Success 200 "Success"
func (con FaqController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FaqEditReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FaqService.CheckIdExist(db, &model.Faq{}, req.FaqId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.FaqService.CheckAnswerFileExist(db, req.Answer)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		tx := db.Begin()
		err = services.FaqService.Edit(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Faq
// @Summary 查询常見問題
// @Description
// @Router /v1/system/faqs/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FaqInquireReq true "parameter"
// @Success 200 {object} services.FaqInquireResp "Success"
func (con FaqController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FaqInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.FaqService.Inquire(db, req)
		if xgorm.IsNotFoundErr(err) {
			nc.BadRequestResponse(err)
			return
		}
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Faq
// @Summary 删除常見問題
// @Description
// @Router /v1/system/faqs/actions/delete [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FaqDeleteReq true "parameter"
// @Success 200 "Success"
func (con FaqController) Delete(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FaqDeleteReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FaqService.CheckIdExist(db, &model.Faq{}, req.FaqId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		tx := db.Begin()
		err = services.FaqService.Delete(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}
