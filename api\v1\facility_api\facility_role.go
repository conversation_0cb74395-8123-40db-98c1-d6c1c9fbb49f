package facility_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xmodel"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type FacilityRoleController struct {
	v1.CommonController
}

func NewFacilityRoleController() FacilityRoleController {
	return FacilityRoleController{}
}

// @Tags User
// @Summary 機構角色列表
// @Router /v1/facility/roles [GET]
// @Produce  json
// @Param form query services.RoleListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Success 200 {object} []services.RoleListResp "Success"
func (con FacilityRoleController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.RoleListReq
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.UserType = model.UserUserTypeFacilityUser
		req.FacilityId, err = con.GetUserFacilityId(nc, db)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		resp, err := services.RoleService.List(db, req, &pageSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Role
// @Summary 機構角色搜索
// @Router /v1/facility/roles/actions/search [GET]
// @Produce  json
// @Param form query services.RoleSearchReq true "parameter"
// @Success 200 {object} services.RoleSearchResp "Success"
func (con FacilityRoleController) Search(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.RoleSearchReq
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.UserType = model.UserUserTypeFacilityUser
		req.FacilityId, err = con.GetUserFacilityId(nc, db)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		result, err := services.RoleService.Search(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(result)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Role
// @Summary 查询角色
// @Description
// @Router /v1/facility/roles/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.RoleInquireReq true "parameter"
// @Success 200 {object} services.RoleInquireResp "Success"
func (con FacilityRoleController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.RoleInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.UserType = model.UserUserTypeFacilityUser
		req.FacilityId, err = con.GetUserFacilityId(nc, db)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		resp, err := services.RoleService.Inquire(db, req)
		if xgorm.IsNotFoundErr(err) {
			nc.BadRequestResponse(err)
			return
		}
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags User
// @Summary 機構角色創建
// @Router /v1/facility/roles/actions/create [POST]
// @Produce  json
// @Param form query services.RoleCreateReq true "parameter"
// @Success 200 {object} services.RoleCreateResp "Success"
func (con FacilityRoleController) Create(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.RoleCreateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.UserType = model.UserUserTypeFacilityUser
		req.FacilityId, err = con.GetUserFacilityId(nc, db)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		alerter := xapp.NewCK(c)
		alerter.
			Run(func() (bool, i18n.Message, error) {
				return services.RoleService.CheckRoleNameAlreadyExists(db, model.UserUserTypeFacilityUser, req.Name, req.FacilityId)
			})
		var alertMsg []string
		alertMsg, err = alerter.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(alertMsg) > 0 {
			nc.AlertResponse(alertMsg)
			return
		}
		tx := db.Begin()
		resp, err := services.RoleService.Create(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags User
// @Summary 機構角色編輯
// @Router /v1/facility/roles/actions/edit [POST]
// @Produce  json
// @Param form query services.RoleEditReq true "parameter"
// @Success 200 "Success"
func (con FacilityRoleController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.RoleEditReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.UserType = model.UserUserTypeFacilityUser
		req.FacilityId, err = con.GetUserFacilityId(nc, db)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.RoleService.CheckRoleExists(db, model.UserUserTypeFacilityUser, req.RoleId, req.FacilityId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.RoleService.CheckRoleCanUpdateByFacility(db, req.RoleId)
			})
		if nc.BadRequestResponseIfCheckerFailed(checker, req) {
			return
		}

		alertChecker := xapp.NewCK(c)
		alertChecker.
			Run(func() (bool, i18n.Message, error) {
				return services.RoleService.CheckRoleNameAlreadyExists(db, model.UserUserTypeFacilityUser, req.Name, req.FacilityId, req.RoleId)
			})
		var alertMsg []string
		alertMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(alertMsg) > 0 {
			nc.AlertResponse(alertMsg)
			return
		}
		tx := db.Begin()
		err = services.RoleService.Edit(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags User
// @Summary 機構角色刪除
// @Router /v1/facility/roles/actions/delete [POST]
// @Produce  json
// @Param form query services.RoleDeleteReq true "parameter"
// @Success 200 "Success"
func (con FacilityRoleController) Delete(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.RoleDeleteReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.UserType = model.UserUserTypeFacilityUser
		req.FacilityId, err = con.GetUserFacilityId(nc, db)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.RoleService.CheckRoleExists(db, model.UserUserTypeFacilityUser, req.RoleId, req.FacilityId)
			})
		if nc.BadRequestResponseIfCheckerFailed(checker, req) {
			return
		}

		var alertMsg []string
		alerter := xapp.NewCK(c)
		if alertMsg, err = alerter.
			Run(func() (bool, i18n.Message, error) {
				return services.RoleService.CheckRoleCanDelete(db, req.RoleId)
			}).Result(); err != nil {
			nc.ErrorResponse(req, err)
			return
		} else if len(alertMsg) > 0 {
			nc.AlertResponse(alertMsg)
			return
		}

		tx := db.Begin()
		err = services.RoleService.Delete(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Menu Action
// @Summary 某角色進行功能選擇
// @Description
// @Router /v1/facility/roles/actions/select-action [POST]
// @Produce  json
// @Security ApiKeyAuth
// @Param json body services.RoleActionSelectReq true "parameter"
// @Success 200 "Success"
func (con FacilityRoleController) RoleSelectActions(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.RoleActionSelectReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.System = model.MenuSystemFacility

		var facilityId uint64
		facilityId, err = con.GetUserFacilityId(nc, db)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		var role xmodel.Role
		// 檢查合法性
		var checkMsg []string
		checkMsg, err = xapp.NewCK(c).
			Run(func() (bool, i18n.Message, error) {
				return services.RoleService.CheckIdExist(db, &role, req.RoleId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.RoleService.CheckRoleExists(db, model.UserUserTypeFacilityUser, req.RoleId, facilityId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.RoleService.CheckRoleCanUpdateByFacility(db, req.RoleId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.MenuActionService.CheckActionIdsExist(db, req.System, req.Actions)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.MenuActionService.CheckRoleActionIds(db, role.UserType, req.Actions)
			}).
			Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		tx := db.Begin()
		if err = services.MenuActionService.SelectRoleActions(tx, req); err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		// 刪除角色緩存
		_ = services.RoleService.ClearUserRoleCache(c, req.RoleId)
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Menu Action
// @Summary 某角色選擇了哪些功能
// @Description
// @Router /v1/facility/roles/actions/action-list [GET]
// @Produce  json
// @Security ApiKeyAuth
// @Param roleId query uint64 true "角色id"
// @Success 200 {object} []services.RoleActionListResp "Success"
func (con FacilityRoleController) RoleActionList(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.RoleActionListReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.System = model.MenuSystemFacility
		var facilityId uint64
		facilityId, err = con.GetUserFacilityId(nc, db)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		// 檢查合法性
		var checkMsg []string
		checkMsg, err = xapp.NewCK(c).
			Run(func() (bool, i18n.Message, error) {
				return services.RoleService.CheckRoleExists(db, model.UserUserTypeFacilityUser, req.RoleId, facilityId)
			}).
			Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		result, err := services.MenuActionService.ListRoleActions(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(result)
	} else {
		nc.BadRequestResponse(err)
	}
}
