package routers

import (
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

// 專業人士 - 獲取用戶通知列表測試
func TestProfessionalSystemNotificationGetList(t *testing.T) {
	user := getTestUser(15) // 使用專業人士用戶

	test := xtest.Test{
		Url:              programPath + "/v1/professional/system-notifications",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "專業人士 - 獲取用戶通知列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常獲取通知列表",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.GetUserNotificationListReq{
					Read: "N",
				},
			},
			{
				SubName:           "獲取已讀通知列表",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.GetUserNotificationListReq{
					Read: "Y",
				},
			},
			{
				SubName:           "獲取所有通知列表",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.GetUserNotificationListReq{},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 專業人士 - 標記通知為已讀測試
func TestProfessionalSystemNotificationMarkAsRead(t *testing.T) {
	user := getTestUser(15)

	test := xtest.Test{
		Url:              programPath + "/v1/professional/system-notifications/actions/mark-read",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "專業人士 - 標記通知為已讀",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常標記通知為已讀",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.MarkNotificationAsReadReq{
					NotificationIds: []uint64{1, 2, 3},
				},
			},
			{
				SubName:           "空通知ID列表",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.MarkNotificationAsReadReq{
					NotificationIds: []uint64{},
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 專業人士 - 標記所有通知為已讀測試
func TestProfessionalSystemNotificationMarkAllAsRead(t *testing.T) {
	user := getTestUser(15)

	test := xtest.Test{
		Url:              programPath + "/v1/professional/system-notifications/actions/mark-all-read",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "專業人士 - 標記所有通知為已讀",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常標記所有通知為已讀",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            struct{}{},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 專業人士 - 刪除通知測試
func TestProfessionalSystemNotificationDelete(t *testing.T) {
	user := getTestUser(15)

	test := xtest.Test{
		Url:              programPath + "/v1/professional/system-notifications/actions/delete",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "專業人士 - 刪除通知",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常刪除通知",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.DeleteNotificationReq{
					NotificationIds: []uint64{1, 2, 3},
				},
			},
			{
				SubName:           "空通知ID列表",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.DeleteNotificationReq{
					NotificationIds: []uint64{},
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 專業人士 - 獲取未讀通知數量測試
func TestProfessionalSystemNotificationGetUnreadCount(t *testing.T) {
	user := getTestUser(15)

	test := xtest.Test{
		Url:              programPath + "/v1/professional/system-notifications/unread-count",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "專業人士 - 獲取未讀通知數量",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常獲取未讀通知數量",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            struct{}{},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 機構 - 獲取用戶通知列表測試
func TestFacilitySystemNotificationGetList(t *testing.T) {
	user := getTestUser(16) // 使用機構用戶

	test := xtest.Test{
		Url:              programPath + "/v1/facility/system-notifications",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "機構 - 獲取用戶通知列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常獲取通知列表",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.GetUserNotificationListReq{
					Read: "N",
				},
			},
			{
				SubName:           "獲取已讀通知列表",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.GetUserNotificationListReq{
					Read: "Y",
				},
			},
			{
				SubName:           "獲取所有通知列表",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.GetUserNotificationListReq{},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 機構 - 標記通知為已讀測試
func TestFacilitySystemNotificationMarkAsRead(t *testing.T) {
	user := getTestUser(16)

	test := xtest.Test{
		Url:              programPath + "/v1/facility/system-notifications/actions/mark-read",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "機構 - 標記通知為已讀",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常標記通知為已讀",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.MarkNotificationAsReadReq{
					NotificationIds: []uint64{1, 2, 3},
				},
			},
			{
				SubName:           "空通知ID列表",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.MarkNotificationAsReadReq{
					NotificationIds: []uint64{},
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 機構 - 標記所有通知為已讀測試
func TestFacilitySystemNotificationMarkAllAsRead(t *testing.T) {
	user := getTestUser(16)

	test := xtest.Test{
		Url:              programPath + "/v1/facility/system-notifications/actions/mark-all-read",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "機構 - 標記所有通知為已讀",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常標記所有通知為已讀",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            struct{}{},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 機構 - 刪除通知測試
func TestFacilitySystemNotificationDelete(t *testing.T) {
	user := getTestUser(16)

	test := xtest.Test{
		Url:              programPath + "/v1/facility/system-notifications/actions/delete",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "機構 - 刪除通知",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常刪除通知",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.DeleteNotificationReq{
					NotificationIds: []uint64{1, 2, 3},
				},
			},
			{
				SubName:           "空通知ID列表",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.DeleteNotificationReq{
					NotificationIds: []uint64{},
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 機構 - 獲取未讀通知數量測試
func TestFacilitySystemNotificationGetUnreadCount(t *testing.T) {
	user := getTestUser(16)

	test := xtest.Test{
		Url:              programPath + "/v1/facility/system-notifications/unread-count",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "機構 - 獲取未讀通知數量",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常獲取未讀通知數量",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            struct{}{},
			},
		},
	}
	xtest.RunTests(t, test)
}
