package routers

import (
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

// 獲取用戶通知列表測試
func TestSystemNotificationGetList(t *testing.T) {
	user := getTestUser(15) // 使用專業人士用戶

	test := xtest.Test{
		Url:              programPath + "/v1/app/system-notifications",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "獲取用戶通知列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常獲取通知列表",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.GetUserNotificationListReq{
					Read: "N",
				},
			},
			{
				SubName:           "獲取已讀通知列表",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.GetUserNotificationListReq{
					Read: "Y",
				},
			},
			{
				SubName:           "獲取所有通知列表",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.GetUserNotificationListReq{},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 標記通知讀取狀態測試
func TestSystemNotificationMarkAsRead(t *testing.T) {
	user := getTestUser(15)

	test := xtest.Test{
		Url:              programPath + "/v1/app/system-notifications/actions/mark-read",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "標記通知讀取狀態",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常標記通知為已讀",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.MarkNotificationAsReadReq{
					SystemNotificationIds: []uint64{1, 2, 3},
					Read:                  "Y",
				},
			},
			{
				SubName:           "正常標記通知為未讀",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.MarkNotificationAsReadReq{
					SystemNotificationIds: []uint64{1, 2, 3},
					Read:                  "N",
				},
			},
			{
				SubName:           "空通知ID列表",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.MarkNotificationAsReadReq{
					SystemNotificationIds: []uint64{},
					Read:                  "Y",
				},
			},
			{
				SubName:           "無效的讀取狀態",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.MarkNotificationAsReadReq{
					SystemNotificationIds: []uint64{1, 2, 3},
					Read:                  "X",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 標記所有通知為已讀測試
func TestSystemNotificationMarkAllAsRead(t *testing.T) {
	user := getTestUser(15)

	test := xtest.Test{
		Url:              programPath + "/v1/app/system-notifications/actions/mark-all-read",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "標記所有通知為已讀",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常標記所有通知為已讀",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            struct{}{},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 刪除通知測試
func TestSystemNotificationDelete(t *testing.T) {
	user := getTestUser(15)

	test := xtest.Test{
		Url:              programPath + "/v1/app/system-notifications/actions/delete",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "刪除通知",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常刪除通知",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.DeleteNotificationReq{
					SystemNotificationIds: []uint64{1, 2, 3},
				},
			},
			{
				SubName:           "空通知ID列表",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.DeleteNotificationReq{
					SystemNotificationIds: []uint64{},
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 獲取未讀通知數量測試
func TestSystemNotificationGetUnreadCount(t *testing.T) {
	user := getTestUser(15)

	test := xtest.Test{
		Url:              programPath + "/v1/app/system-notifications/unread-count",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "獲取未讀通知數量",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常獲取未讀通知數量",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            struct{}{},
			},
		},
	}
	xtest.RunTests(t, test)
}

// 機構用戶測試 - 使用相同的公用 API
func TestSystemNotificationGetListWithFacilityUser(t *testing.T) {
	user := getTestUser(16) // 使用機構用戶

	test := xtest.Test{
		Url:              programPath + "/v1/app/system-notifications",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "機構用戶 - 獲取用戶通知列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常獲取通知列表",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.GetUserNotificationListReq{
					Read: "N",
				},
			},
			{
				SubName:           "獲取已讀通知列表",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.GetUserNotificationListReq{
					Read: "Y",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
