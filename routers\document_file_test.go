package routers

import (
	"github.com/Norray/medic-crew/model"
	"io/ioutil"
	"os"
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

func TestDocumentFileUpload(t *testing.T) {
	// 讀取圖片
	file, err := os.Open("demo.png")
	if err != nil {
		t.Error(err)
		return
	}
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/document-files/actions/upload",
		UserId:           9,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Form,
		Name:             "上傳單據文件",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常上傳",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.DocumentFileUploadReq{
					FileCode: model.DocumentFileCodeWages,
				},
				File:          file,
				FileFieldName: "file",
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestDocumentFilePreview(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/document-files/actions/preview",
		UserId:           18,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "預覽和下載文件",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.DocumentFileGetPreviewReq{
					DocumentFileId: 52,
				},
				CheckResultFileHandler: func(fileData []byte) bool {
					if len(fileData) == 0 {
						return false
					}
					err := ioutil.WriteFile("test.png", fileData, 0644)
					if err != nil {
						return false
					}
					return true
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityDocumentFilePreview(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/document-files/actions/preview",
		UserId:           16,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "預覽和下載文件",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.DocumentFileGetPreviewReq{
					DocumentFileId: 52,
				},
				CheckResultFileHandler: func(fileData []byte) bool {
					if len(fileData) == 0 {
						return false
					}
					err := ioutil.WriteFile("test2.png", fileData, 0644)
					if err != nil {
						return false
					}
					return true
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
