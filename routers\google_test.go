package routers

import (
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
	"github.com/shopspring/decimal"
)

func TestGoogleTimezone(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/facility/google/timezone",
		UserId:           12,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "獲取時區",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.TimezoneRequest{
					Latitude:  decimal.NewFromFloat(22.500074),
					Longitude: decimal.NewFromFloat(114.054431),
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
