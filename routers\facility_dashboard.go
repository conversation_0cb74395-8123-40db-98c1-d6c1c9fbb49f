package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/gin-gonic/gin"
)

// 機構儀表板路由 - 機構端
func facilityDashboardRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		controller := facility_api.NewFacilityDashboardController()
		r.GET("/dashboard/summary", controller.DashboardSummary)                        // 儀表板匯總統計
		r.GET("/dashboard/expense", controller.FacilityExpense)                         // 機構支出統計
		r.GET("/dashboard/job-statistic", controller.FacilityJobStatistic)              // 機構職業發佈工作量統計
		r.GET("/dashboard/job-salary-statistic", controller.FacilityJobSalaryStatistic) // 機構職業發佈工作量統計
	}
}
