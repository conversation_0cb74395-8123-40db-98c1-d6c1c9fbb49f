#!/bin/sh

# 默認的 Go 命令
DEFAULT_GO_CMD=""
GO_CMD=""

local_go_path=".githooks/.go-path.local"

# 檢查本地配置文件是否存在
if [ -f "$local_go_path" ]; then
    # 讀取配置文件中的 Go 路徑，並去除可能存在的空白字符
    CUSTOM_GO_CMD=$(cat "$local_go_path" | tr -d '[:space:]')
    if [ -n "$CUSTOM_GO_CMD" ]; then
        # 檢查自定義路徑是否可執行
        if [ -x "$CUSTOM_GO_CMD" ]; then
            GO_CMD="$CUSTOM_GO_CMD"
            echo "使用自定義 Go 路徑：$GO_CMD"
        else
            echo "自定義 Go 路徑 ($CUSTOM_GO_CMD) 不存在或不可執行，請檢查是否正確配置。"
            exit 1
        fi
    fi
fi

# 如果GO_CMD為空，則報錯
if [ -z "$GO_CMD" ]; then
    echo "- 請先配置 .githooks/.go-path.local 文件"
    exit 1
fi

echo ""
echo "開始執行提交前檢查..."
echo ""

# 先檢查 go 版本
echo "1. 正在檢查 Go 版本..."
GO_VERSION=$("$GO_CMD" version)
if [ $? -ne 0 ]; then
    echo "Go 版本檢查失敗，請檢查 Go 是否正確安裝或環境變量是否正確。"
    exit 1
fi
echo "- Go 版本檢查成功，版本號：$GO_VERSION"

# ------------------------------ 檢查代碼 ------------------------------

# 生成 i18n 文件
echo ""
echo "2. 正在生成 i18n 文件..."
eval "./gen_i18n.sh"
if [ $? -ne 0 ]; then
    echo "- i18n 文件生成失敗，請檢查錯誤。"
    exit 1
fi

# 格式化檢查
echo ""
echo "3. 正在檢查代碼格式..."
UNFORMATTED_FILES=$("$GO_CMD" fmt ./...)
if [ -n "$UNFORMATTED_FILES" ]; then
    echo "- 以下文件需要格式化："
    echo "$UNFORMATTED_FILES"
    exit 1
fi

# 檢查代碼靜態分析
echo ""
echo "4. 正在進行靜態代碼分析..."
"$GO_CMD" vet ./...
if [ $? -ne 0 ]; then
    echo "- 靜態分析失敗，請修復問題後再提交。"
    exit 1
fi

# 檢查是否存在未 rollback 的代碼
echo ""
echo "5. 正在檢查未 rollback 的代碼..."

# 根據操作系統選擇對應的工具
if [ "$(uname)" = "Darwin" ]; then
    # Mac 系統
    ROLLBACK_TOOL="./find_miss_rollback_windows"
else
    # Windows 系統
    ROLLBACK_TOOL="./find_miss_rollback_windows.exe"
fi

if [ -f "$ROLLBACK_TOOL" ]; then
    # 執行工具並獲取輸出
    ROLLBACK_RESULT=$("$ROLLBACK_TOOL" 2>&1)
    ROLLBACK_EXIT_CODE=$?
    
    # 根據輸出內容判斷檢查結果
    if echo "$ROLLBACK_RESULT" | grep -q "沒有發現漏 rollback"; then
        echo "- 未發現漏 rollback 代碼，檢查通過"
    elif echo "$ROLLBACK_RESULT" | grep -q "可能漏掉 rollback/commit 的地方"; then
        echo "- 發現未 rollback 的代碼："
        echo "$ROLLBACK_RESULT" | grep -v "掃描文件:"
        exit 1
    else
        echo "- Rollback 檢查工具執行出錯："
        echo "$ROLLBACK_RESULT"
        exit 1
    fi
else
    echo "- 警告：找不到 rollback 檢查工具 ($ROLLBACK_TOOL)，跳過檢查。"
fi

echo ""
echo "所有檢查通過，正在提交..."
