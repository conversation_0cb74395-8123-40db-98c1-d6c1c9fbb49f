package routers

import (
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
	"io/ioutil"
	"os"
	"testing"
)

var testFacilityFileId uint64

func TestFacilityFileUpload(t *testing.T) {
	// 讀取圖片
	file, err := os.Open("demo.pdf")
	if err != nil {
		t.Error(err)
		return
	}
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/facility-files/actions/upload",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Form,
		Name:             "上傳機構文件",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常上傳",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityFileUploadReq{
					FacilityId: 2,
					FileCode:   model.FacilityFileCodeSignedAgreement,
				},
				File:          file,
				FileFieldName: "file",
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityFacilityFilePreview(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/facility-files/actions/preview",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "預覽和下載文件",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityFileGetPreviewReq{
					FacilityFileId: 1,
					Thumb:          "Y",
				},
				CheckResultFileHandler: func(fileData []byte) bool {
					if len(fileData) == 0 {
						return false
					}
					err := ioutil.WriteFile("test.png", fileData, 0644)
					if err != nil {
						return false
					}
					return true
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSystemFacilityFilePreview(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/facility-files/actions/preview",
		UserId:           22,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "預覽和下載文件",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityFileGetPreviewReq{
					FacilityFileId: 1,
					Thumb:          "Y",
				},
				CheckResultFileHandler: func(fileData []byte) bool {
					if len(fileData) == 0 {
						return false
					}
					err := ioutil.WriteFile("test.png", fileData, 0644)
					if err != nil {
						return false
					}
					return true
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
