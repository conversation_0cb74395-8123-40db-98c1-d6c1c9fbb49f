package model

import (
	"fmt"
	"strings"

	"github.com/jinzhu/copier"
)

const (
	ProfessionalReferenceFormVersion1 = "1.0"

	ProfessionalReferenceFormFieldTypeText     = "TEXT"
	ProfessionalReferenceFormFieldTypeTextarea = "TEXTAREA"
	ProfessionalReferenceFormFieldTypeRadio    = "RADIO"
)

// ProfessionalReferenceForm 是包含所有表單數據的根結構，不需要數據庫表
type ProfessionalReferenceForm struct {
	Version             string                                   `json:"version"`             // 版本號
	FormTitle           string                                   `json:"formTitle"`           // 表單標題
	FormDescription     string                                   `json:"formDescription"`     // 表單描述，說明表單的用途和填寫指南
	FormRemark          string                                   `json:"formRemark"`          // 表單備註
	Sections            []ProfessionalReferenceSection           `json:"sections"`            // 表單的各個部分，每個部分包含多個表單字段
	Confirmation        ProfessionalReferenceConfirmation        `json:"confirmation"`        // 表單確認部分，通常包含確認條款和勾選框
	ElectronicSignature ProfessionalReferenceElectronicSignature `json:"electronicSignature"` // 電子簽名設置，用於表單的電子簽名功能
	MatchUuids          []string                                 `json:"matchUuids"`          // 存在于裡面的設備才能提交
}

func (f ProfessionalReferenceForm) AOrAn(name string) string {
	// 將name全部小寫，並且檢查第一個字母是否為a,e,i,o,u
	name = strings.ToLower(name)
	if len(name) > 0 {
		firstChar := name[0:1]
		if firstChar == "a" || firstChar == "e" || firstChar == "i" || firstChar == "o" || firstChar == "u" {
			return "an"
		}
	}
	return "a"
}

// DefaultForm 生成一個預設的專業推薦表單
func (f ProfessionalReferenceForm) DefaultForm(professionNameMap map[string]string, professional Professional, reference ProfessionalReference) ProfessionalReferenceForm {
	yesOptionWithRemark := ProfessionalReferenceOption{
		Value:          "Y",
		Label:          "Yes",
		DisplayRemark:  "Y",
		RemarkRequired: "Y",
		RemarkTitle:    "Please explain",
	}
	yesOption := ProfessionalReferenceOption{
		Value:         "Y",
		Label:         "Yes",
		DisplayRemark: "N",
	}
	noOption := ProfessionalReferenceOption{
		Value:         "N",
		Label:         "No",
		DisplayRemark: "N",
	}
	noOptionWithRemark := ProfessionalReferenceOption{
		Value:          "N",
		Label:          "No",
		DisplayRemark:  "Y",
		RemarkRequired: "Y",
		RemarkTitle:    "Please explain",
	}
	withReservationsOption := ProfessionalReferenceOption{
		Value:          "WITH_RESERVATIONS",
		Label:          "With reservations",
		DisplayRemark:  "Y",
		RemarkRequired: "Y",
		RemarkTitle:    "Please explain",
	}

	return ProfessionalReferenceForm{
		Version:         ProfessionalReferenceFormVersion1,
		FormTitle:       "Medic Crew support worker reference for " + professional.Name(),
		FormDescription: "The following questions are about your experience with " + professional.Name() + " and whether you'd recommend them to provide services to people with a disability and/or aged care.",
		FormRemark:      "This should take no longer than a few minutes to complete. If necessary, please update any information and answer the following questions honestly.",
		Sections: []ProfessionalReferenceSection{
			{
				SectionTitle: "Applicant Information",
				Fields: []ProfessionalReferenceField{
					{
						Id:       "is_applicant_name",
						Type:     ProfessionalReferenceFormFieldTypeRadio,
						Label:    "Is the applicant's name " + professional.Name() + "?",
						Required: "Y",
						Options: []ProfessionalReferenceOption{
							yesOption,
							noOptionWithRemark,
						},
						ExpectedValue: "Y",
					},
					{
						Id:       "is_medical_practitioner",
						Type:     ProfessionalReferenceFormFieldTypeRadio,
						Label:    fmt.Sprintf("Is the applicant %s %s?", f.AOrAn(professionNameMap[professional.Profession]), professionNameMap[professional.Profession]),
						Required: "Y",
						Options: []ProfessionalReferenceOption{
							yesOption,
							noOptionWithRemark,
						},
						ExpectedValue: "Y",
					},
				},
			},
			{
				SectionTitle: "Referee Information",
				Fields: []ProfessionalReferenceField{
					{
						Id:       "is_referee_name",
						Type:     ProfessionalReferenceFormFieldTypeRadio,
						Label:    "Is your name " + reference.Name() + "?",
						Required: "Y",
						Options: []ProfessionalReferenceOption{
							yesOption,
							noOptionWithRemark,
						},
						ExpectedValue: "Y",
					},
					{
						Id:       "is_doctor",
						Type:     ProfessionalReferenceFormFieldTypeRadio,
						Label:    fmt.Sprintf("Is your position/title '%s'?", reference.ReferenceRole),
						Required: "Y",
						Options: []ProfessionalReferenceOption{
							yesOption,
							noOptionWithRemark,
						},
						ExpectedValue: "Y",
					},
					{
						Id:       "worked_together",
						Type:     ProfessionalReferenceFormFieldTypeRadio,
						Label:    "Have you worked with " + professional.Name() + " at " + reference.ReferenceFacility + reference.WorkTogetherDescription(reference) + "?",
						Required: "Y",
						Options: []ProfessionalReferenceOption{
							yesOption,
							noOptionWithRemark,
						},
						ExpectedValue: "Y",
					},
					{
						Id:          "relationship",
						Type:        ProfessionalReferenceFormFieldTypeTextarea,
						Label:       "Please detail the nature of your relationship with " + professional.Name() + "?",
						Required:    "Y",
						Placeholder: "E.g. direct boss, colleagues, friend etc...",
					},
				},
			},
			{
				SectionTitle: "Reference Questions",
				Description:  "Please answer the following based on your experience working with the applicant.",
				Fields: []ProfessionalReferenceField{
					{
						Id:          "responsibilities",
						Type:        ProfessionalReferenceFormFieldTypeTextarea,
						Label:       "1. Can you briefly describe the applicant's responsibilities in their role?",
						Required:    "Y",
						Placeholder: "Enter",
					},
					{
						Id:          "competence",
						Type:        ProfessionalReferenceFormFieldTypeText,
						Label:       "2. How would you rate the applicant's overall competence in their role?",
						Required:    "Y",
						Placeholder: "Enter",
					},
					{
						Id:          "communication",
						Type:        ProfessionalReferenceFormFieldTypeText,
						Label:       "3. How would you rate the applicant's communication and professionalism with patients and colleagues?",
						Required:    "Y",
						Placeholder: "Enter",
					},
					{
						Id:       "issues",
						Type:     ProfessionalReferenceFormFieldTypeRadio,
						Label:    "4. Have you observed any issues related to reliability, punctuality or attitude?",
						Required: "Y",
						Options: []ProfessionalReferenceOption{
							yesOptionWithRemark,
							noOption,
						},
					},
					{
						Id:       "recommend",
						Type:     ProfessionalReferenceFormFieldTypeRadio,
						Label:    "5. Would you recommend this person for casual healthcare work?",
						Required: "Y",
						Options: []ProfessionalReferenceOption{
							yesOption,
							withReservationsOption,
							noOptionWithRemark,
						},
					},
				},
			},
		},
		Confirmation: ProfessionalReferenceConfirmation{
			Text:     "I confirm that the information provided above is accurate and based on my direct professional experience with the applicant.",
			Required: "Y",
			Checked:  "N",
		},
		ElectronicSignature: ProfessionalReferenceElectronicSignature{
			Placeholder:          "Electronic Signature",
			ProfessionalFileUuid: "",
		},
		MatchUuids: []string{},
	}
}

func (v ProfessionalReferenceOption) ChangeRemarkTitle(remarkTitle string) ProfessionalReferenceOption {
	v.RemarkTitle = remarkTitle
	return v
}

// ProfessionalReferenceOption 表示單選按鈕或下拉選擇框的可選項
type ProfessionalReferenceOption struct {
	Value          string `json:"value"`          // 選項的值，用於表單提交和數據處理
	Label          string `json:"label"`          // 選項的顯示標籤，用於用戶界面展示
	DisplayRemark  string `json:"displayRemark"`  // 選此選項時是否需要顯示備註
	RemarkRequired string `json:"remarkRequired"` // 選此選項時是否必須填寫備註
	RemarkTitle    string `json:"remarkTitle"`    // 備註標題
}

// ProfessionalReferenceField 表示具有各種類型和屬性的表單字段
type ProfessionalReferenceField struct {
	Id            string                        `json:"id"`            // 字段的唯一標識符
	Type          string                        `json:"type"`          // 字段類型，如文本、數字、單選、多選等
	Label         string                        `json:"label"`         // 字段的顯示標籤
	Required      string                        `json:"required"`      // 是否為必填字段
	Value         string                        `json:"value"`         // 字段的預設值或用戶輸入值
	Remark        string                        `json:"remark"`        // 字段的備註，用於添加額外信息,例如選No時候需要填寫的內容
	Options       []ProfessionalReferenceOption `json:"options"`       // 適用於單選或多選字段的選項列表
	Placeholder   string                        `json:"placeholder"`   // 輸入框的佔位文本
	ExpectedValue string                        `json:"expectedValue"` // 預期答案
}

// ProfessionalReferenceSection 表示表單字段的邏輯分組
type ProfessionalReferenceSection struct {
	SectionTitle string                       `json:"sectionTitle"` // 部分的標題
	Description  string                       `json:"description"`  // 部分的描述文本，說明該部分的用途
	Fields       []ProfessionalReferenceField `json:"fields"`       // 該部分包含的表單字段列表
}

// ProfessionalReferenceConfirmation 表示表單底部的確認勾選框
type ProfessionalReferenceConfirmation struct {
	Text     string `json:"text"`     // 確認文本，通常包含條款或聲明內容
	Required string `json:"required"` // 是否必須勾選才能提交表單
	Checked  string `json:"checked"`  // 用戶是否已勾選
}

// ProfessionalReferenceElectronicSignature 表示電子簽名設置
type ProfessionalReferenceElectronicSignature struct {
	Placeholder          string `json:"placeholder"`          // 佔位文本
	ProfessionalFileUuid string `json:"professionalFileUuid"` // 用戶上傳簽名的圖片 UUId，在表 ProfessionalFile
}

// ProfessionalReferenceElectronicSignature 表示電子簽名設置
type ProfessionalReferenceElectronicSignatureForUser struct {
	Placeholder          string `json:"placeholder"`          // 佔位文本
	ProfessionalFileUuid string `json:"professionalFileUuid"` // 用戶上傳簽名的圖片 UUId，在表 ProfessionalFile
	ProfessionalFileData string `json:"professionalFileData"` // 用戶上傳簽名的圖片的base64
}

type ProfessionalReferenceFormForUser struct {
	Version             string                                          `json:"version"`             // 版本號
	FormTitle           string                                          `json:"formTitle"`           // 表單標題
	FormDescription     string                                          `json:"formDescription"`     // 表單描述，說明表單的用途和填寫指南
	FormRemark          string                                          `json:"formRemark"`          // 表單備註
	Sections            []ProfessionalReferenceSectionForUser           `json:"sections"`            // 表單的各個部分，每個部分包含多個表單字段
	Confirmation        ProfessionalReferenceConfirmation               `json:"confirmation"`        // 表單確認部分，通常包含確認條款和勾選框
	ElectronicSignature ProfessionalReferenceElectronicSignatureForUser `json:"electronicSignature"` // 電子簽名設置，用於表單的電子簽名功能
}

// ProfessionalReferenceField 表示具有各種類型和屬性的表單字段
type ProfessionalReferenceFieldForUser struct {
	Id          string                        `json:"id"`          // 字段的唯一標識符
	Type        string                        `json:"type"`        // 字段類型，如文本、數字、單選、多選等
	Label       string                        `json:"label"`       // 字段的顯示標籤
	Required    string                        `json:"required"`    // 是否為必填字段
	Value       string                        `json:"value"`       // 字段的預設值或用戶輸入值
	Remark      string                        `json:"remark"`      // 字段的備註，用於添加額外信息,例如選No時候需要填寫的內容
	Options     []ProfessionalReferenceOption `json:"options"`     // 適用於單選或多選字段的選項列表
	Placeholder string                        `json:"placeholder"` // 輸入框的佔位文本
}

// ProfessionalReferenceSection 表示表單字段的邏輯分組
type ProfessionalReferenceSectionForUser struct {
	SectionTitle string                              `json:"sectionTitle"` // 部分的標題
	Description  string                              `json:"description"`  // 部分的描述文本，說明該部分的用途
	Fields       []ProfessionalReferenceFieldForUser `json:"fields"`       // 該部分包含的表單字段列表
}

func (f ProfessionalReferenceForm) FormForUser() ProfessionalReferenceFormForUser {
	var form ProfessionalReferenceFormForUser
	_ = copier.Copy(&form, f)
	return form
}
