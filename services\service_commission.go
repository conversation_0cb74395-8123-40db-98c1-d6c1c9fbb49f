package services

import (
	"fmt"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

var CommissionService = new(commissionService)

type commissionService struct{}

func (s *commissionService) CheckIdExist(db *gorm.DB, m *model.Commission, id uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.commission.id.does_not_exist",
		Other: "No such record, please try after reloading.",
	}
	var err error
	if err = db.First(&m, id).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

type CommissionCreateReq struct {
	Level          string          `json:"level" binding:"required"`
	CommissionRate decimal.Decimal `json:"commissionRate" binding:"required"`
}

type CommissionCreateResp struct {
	CommissionId uint64 `json:"commissionId"`
}

func (s *commissionService) Create(db *gorm.DB, req CommissionCreateReq) (CommissionCreateResp, error) {
	var resp CommissionCreateResp
	var err error
	var m model.Commission
	_ = copier.Copy(&m, req)
	if err = db.Create(&m).Error; err != nil {
		return resp, err
	}
	resp.CommissionId = m.Id
	return resp, nil
}

type CommissionListReq struct {
	Level string `form:"level"`
}

type CommissionListResp struct {
	CommissionId   uint64          `json:"commissionId"`
	Level          string          `json:"level"`
	CommissionRate decimal.Decimal `json:"commissionRate"`
}

func (s *commissionService) List(db *gorm.DB, req CommissionListReq, pageSet *xresp.PageSet) ([]CommissionListResp, error) {
	var err error
	var resp []CommissionListResp
	builder := db.Table("commission AS c").Select([]string{
		"c.id AS commission_id",
		"c.level",
		"c.commission_rate",
	})

	if req.Level != "" {
		builder = builder.Where("c.level LIKE ?", xgorm.EscapeLikeWithWildcards(req.Level))
	}
	if err = builder.Scopes(xresp.Paginate(pageSet)).Order("c.id").Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

type CommissionSearchReq struct {
	Level      string `form:"level"`
	SelectedId uint64 `form:"selectedId"`
	Limit      int    `form:"limit"`
}

type CommissionSearchResp struct {
	CommissionId   uint64          `json:"commissionId"`
	Level          string          `json:"level"`
	CommissionRate decimal.Decimal `json:"commissionRate"`
}

func (s *commissionService) Search(db *gorm.DB, req CommissionSearchReq) ([]CommissionSearchResp, error) {
	var err error
	var resp []CommissionSearchResp
	builder := db.Table("commission AS c").Select([]string{
		"c.id AS commission_id",
		"c.level",
		"c.commission_rate",
	})

	if req.Level != "" {
		builder = builder.Where("c.level LIKE ?", xgorm.EscapeLikeWithWildcards(req.Level))
	}
	if req.SelectedId != 0 {
		builder = builder.Order(fmt.Sprintf("IF(c.id = %d,0,1)", req.SelectedId))
	}
	if req.Limit > 0 {
		builder = builder.Limit(req.Limit)
	}
	if err = builder.Order("c.id").Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

type CommissionEditReq struct {
	CommissionId   uint64          `json:"commissionId" binding:"required"`
	Level          string          `json:"level" binding:"required"`
	CommissionRate decimal.Decimal `json:"commissionRate" binding:"required"`
}

func (s *commissionService) Edit(db *gorm.DB, req CommissionEditReq) error {
	var err error
	var m model.Commission
	if err = db.First(&m, req.CommissionId).Error; err != nil {
		return err
	}
	_ = copier.Copy(&m, req)
	if err = db.Save(&m).Error; err != nil {
		return err
	}
	return nil
}

type CommissionInquireReq struct {
	CommissionId uint64 `form:"commissionId" binding:"required"`
}

type CommissionInquireResp struct {
	CommissionId   uint64          `json:"commissionId"`
	Level          string          `json:"level"`
	CommissionRate decimal.Decimal `json:"commissionRate"`
}

func (s *commissionService) Inquire(db *gorm.DB, req CommissionInquireReq) (CommissionInquireResp, error) {
	var err error
	var resp CommissionInquireResp
	var m model.Commission
	if err = db.First(&m, req.CommissionId).Error; err != nil {
		return resp, err
	}
	_ = copier.Copy(&resp, m)
	resp.CommissionId = m.Id
	return resp, nil
}

type CommissionDeleteReq struct {
	CommissionId uint64 `json:"commissionId" binding:"required"`
}

func (s *commissionService) Delete(db *gorm.DB, req CommissionDeleteReq) error {
	var err error
	if err = db.Delete(&model.Commission{}, req.CommissionId).Error; err != nil {
		return err
	}
	return nil
}
