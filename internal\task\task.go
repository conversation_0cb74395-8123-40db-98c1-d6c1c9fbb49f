package task

import (
	"github.com/Norray/xrocket/xamqp"
	log "github.com/sirupsen/logrus"
)

func SetupWorkers() {
	xamqp.DefaultSetupWithReconnection(func() {
		//var err error
		log.Info("init task")
		//// 工作計劃定時發佈工作
		//err = xamqp.QueueDeclare(JobSchedulePublishTask)
		//if err != nil {
		//	log.Fatalf("can not init job schedule publish task: %v", err)
		//}
		//err = xamqp.SetupWorker(JobSchedulePublishTask, JobSchedulePublish)
		//if err != nil {
		//	log.Fatalf("can not init job schedule publish task: %v", err)
		//}
		//
		//// 生成代付發票草稿
		//err = xamqp.QueueDeclare(CreateInvoiceDraftTask)
		//if err != nil {
		//	log.Fatalf("can not init create invoice draft task: %v", err)
		//}
		//err = xamqp.SetupWorker(CreateInvoiceDraftTask, CreateInvoiceDraft)
		//if err != nil {
		//	log.Fatalf("can not init create invoice draft task: %v", err)
		//}
		//
		//// 發送預付發票
		//err = xamqp.QueueDeclare(SendPayUpfrontInvoiceTask)
		//if err != nil {
		//	log.Fatalf("can not init send pay upfront invoice task: %v", err)
		//}
		//err = xamqp.SetupWorker(SendPayUpfrontInvoiceTask, SendPayUpfrontInvoice)
		//if err != nil {
		//	log.Fatalf("can not init send pay upfront invoice task: %v", err)
		//}
		//
		//// 生成賠付賬單
		//err = xamqp.QueueDeclare(GenerateCompensationConfirmationNoteTask)
		//if err != nil {
		//	log.Fatalf("can not init generate compensation confirmation note task task: %v", err)
		//}
		//err = xamqp.SetupWorker(GenerateCompensationConfirmationNoteTask, GenerateCompensationConfirmationNote)
		//if err != nil {
		//	log.Fatalf("can not init generate compensation confirmation note task: %v", err)
		//}
	})
}

func Close() {
	log.Info("amqp close")
	xamqp.Close()
}
