package facility_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type JobApplicationController struct {
	v1.CommonController
}

func NewJobApplicationController() JobApplicationController {
	return JobApplicationController{}
}

// @Tags JobApplication
// @Summary 查詢工作職位申請列表
// @Description 查詢特定工作職位的申請者列表，支持分頁和條件過濾
// @Router /v1/facility/job-applications [GET]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.JobApplicationListForFacilityReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Param sortingKey query string false "score 匹配分數"
// @Param sortingType query string false "a=順序,d=倒序"
// @Success 200 {object} []services.JobApplicationListResp "Success"
func (con JobApplicationController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobApplicationListForFacilityReq
	var pageSet xresp.PageSet
	var sortSet xresp.SortingSet

	_ = c.ShouldBindQuery(&pageSet)
	_ = c.ShouldBindQuery(&sortSet)

	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		// 檢查工作職位是否存在
		checker := xapp.NewCK(c)
		checker.Run(func() (bool, i18n.Message, error) {
			return services.JobService.CheckIdExist(db, &model.Job{}, req.JobId, req.FacilityId)
		})

		checkMsg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		resp, err := services.JobApplicationService.List(db, req.GetBaseReq(), &pageSet, sortSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags JobApplication
// @Summary 查詢工作職位邀請列表
// @Description 查詢特定工作職位的邀請列表
// @Router /v1/facility/job-applications/actions/invite-list [GET]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.JobApplicationInviteListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Param sortingKey query string false "inviteTime 邀請時間"
// @Param sortingType query string false "a=順序,d=倒序"
// @Success 200 {object} []services.JobApplicationInviteListResp "Success"
func (con JobApplicationController) InviteList(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobApplicationInviteListReq
	var pageSet xresp.PageSet
	var sortSet xresp.SortingSet

	_ = c.ShouldBindQuery(&pageSet)
	_ = c.ShouldBindQuery(&sortSet)

	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.JobApplicationService.InviteList(db, req, &pageSet, sortSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags JobApplication
// @Summary 查詢工作職位申請詳情
// @Description 查詢特定工作職位申請的詳細信息
// @Router /v1/facility/job-applications/actions/inquire [GET]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.JobApplicationInquireForFacilityReq true "parameter"
// @Success 200 {object} services.JobApplicationInquireResp "Success"
func (con JobApplicationController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobApplicationInquireForFacilityReq

	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var application model.JobApplication
		checker := xapp.NewCK(c)
		checker.Run(func() (bool, i18n.Message, error) {
			return services.JobApplicationService.CheckIdExistByFacility(db, &application, req.JobApplicationId, req.FacilityId)
		})

		checkMsg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		resp, err := services.JobApplicationService.Inquire(db, req.GetBaseReq())
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility Job
// @Summary 獲取機構的工作職位信息列表 (每日日曆)
// @Description
// @Router /v1/facility/jobs/actions/calendar-day [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.JobCalendarDayReq true "parameter"
// @Success 200 {object} []services.JobCalendarDayResp "Success"
func (con JobApplicationController) JobCalendarDay(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobCalendarDayReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}
		resp, err := services.JobApplicationService.JobCalendarDay(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility Job
// @Summary 獲取機構的工作職位信息列表 (每日日曆)
// @Description
// @Router /v1/facility/jobs/actions/calendar-month [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.JobCalendarMonthReq true "parameter"
// @Success 200 {object} []services.JobCalendarMonthResp "Success"
func (con JobApplicationController) JobCalendarMonth(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobCalendarMonthReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}
		resp, err := services.JobApplicationService.JobCalendarMonth(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Job
// @Summary 取消工作申請
// @Description 機構取消已開始接洽的工作申請
// @Router /v1/facility/job-applications/actions/cancel [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FacilityJobApplicationCancelReq true "parameter"
// @Success 200 "Success"
func (con JobApplicationController) Cancel(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityJobApplicationCancelReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		// 檢查申請是否可以取消
		var alertMsg []string
		alerter := xapp.NewCK(c)
		alerter.
			Run(func() (bool, i18n.Message, error) {
				return services.JobApplicationService.CheckJobApplicationCanCancelByFacility(db, req.JobApplicationIds, req.JobId, req.FacilityId)
			})
		alertMsg, err = alerter.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(alertMsg) > 0 {
			nc.AlertResponse(alertMsg)
			return
		}

		// 執行取消操作
		tx := db.Begin()
		req.ReqUserId = nc.GetJWTUserId()
		err = services.JobApplicationService.JobApplicationCancelByFacility(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()

		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// JobApplicationListByFacilitySession
// @Tags JobApplication
// @Summary 查詢機構的工作職位申請列表 (聊天會話)
// @Description 查詢機構的工作職位申請列表 (聊天會話)
// @Router /v1/facility/job-applications/actions/list-by-facility-session [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.JobApplicationListByFacilitySessionReq true "parameter"
// @Success 200 {object} []services.JobApplicationDetailBySessionResp "Success"
func (con JobApplicationController) ListByFacilitySession(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobApplicationListByFacilitySessionReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		// 檢查聊天會話是否存在
		checker := xapp.NewCK(c)
		checker.Run(func() (bool, i18n.Message, error) {
			// 檢查機構與專業人士的聊天會話是否存在
			return services.JobApplicationService.CheckFacilityProfessionalApplicationByUserId(db, req.FacilityId, req.ProfessionalUserId)
		})

		checkMsg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		resp, err := services.JobApplicationService.JobApplicationListByFacilitySession(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags JobApplication
// @Summary 機構發起聊天
// @Description 機構發起聊天
// @Router /v1/facility/job-applications/actions/chat [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ChatByFacilityReq true "parameter"
// @Success 200 "Success"
func (con JobApplicationController) Chat(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ChatByFacilityReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		checker := xapp.NewCK(c)
		checker.Run(func() (bool, i18n.Message, error) {
			return services.JobApplicationService.CheckIdExistByFacility(db, &model.JobApplication{}, req.JobApplicationId, req.FacilityId)
		})

		checkMsg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		tx := db.Begin()
		err = services.JobApplicationService.ChatByFacility(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}
