package professional_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
)

type ProfessionalFacilityController struct {
	v1.CommonController
}

func NewProfessionalFacilityController() ProfessionalFacilityController {
	return ProfessionalFacilityController{}
}

// @Tags Facility
// @Summary 查询專業人員可申請的機構
// @Description
// @Router /v1/professional/facilities/actions/search [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FacilitySearchForProfessionalReq true "parameter"
// @Success 200 {object} []services.FacilitySearchForProfessionalResp "Success"
func (con ProfessionalFacilityController) Search(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilitySearchForProfessionalReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		req.ReqUserId = nc.GetJWTUserId()

		resp, err := services.FacilityService.FacilitySearchForProfessional(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
