package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/Norray/medic-crew/api/v1/professional_api"
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func jobApplicationFacilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		controller := facility_api.NewJobApplicationController()
		r.GET("/job-applications", controller.List)                                                   // 查詢工作職位申請列表
		r.GET("/job-applications/actions/invite-list", controller.InviteList)                         // 查詢工作職位邀請列表
		r.GET("/job-applications/actions/inquire", controller.Inquire)                                // 查詢工作職位申請詳情
		r.POST("/job-applications/actions/cancel", controller.Cancel)                                 // 機構取消已開始接洽的工作申請
		r.POST("/job-applications/actions/chat", controller.Chat)                                     // 機構發起聊天
		r.GET("/jobs/actions/calendar-day", controller.JobCalendarDay)                                // 機構的工作日曆
		r.GET("/jobs/actions/calendar-month", controller.JobCalendarMonth)                            // 機構的工作月曆
		r.GET("/job-applications/actions/list-by-facility-session", controller.ListByFacilitySession) // 查詢機構的工作職位申請列表 (聊天會話)
	}
}

func jobApplicationProfessionalRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/professional").Use(handlers...)
	{
		controller := professional_api.NewJobApplicationController()
		r.GET("/my-jobs", controller.MyJob)                                                                   // 獲取我的工作職位信息列表
		r.GET("/my-jobs/actions/statistic", controller.Statistic)                                             // 獲取我的工作職位統計
		r.POST("/my-jobs/actions/withdraw", controller.Withdraw)                                              // 撤回工作申請
		r.POST("/my-jobs/actions/cancel", controller.Cancel)                                                  // 取消已開始接洽的工作申請
		r.POST("/my-jobs/actions/update-calendar-note", controller.UpdateCalendarNote)                        // 更新工作申請備註
		r.POST("/my-jobs/actions/accept-invite", controller.AcceptInvite)                                     // 接受工作邀請
		r.POST("/my-jobs/actions/decline-invite", controller.DeclineInvite)                                   // 拒絕工作邀請
		r.GET("/job-applications/actions/list-by-professional-session", controller.ListByProfessionalSession) // 查詢專業人士的工作職位申請列表 (聊天會話)
		r.POST("/job-applications/actions/chat", controller.ChatByProfessional)                               // 專業人士打開聊天會話
	}
}

func jobApplicationSystemRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/system").Use(handlers...)
	{
		controller := system_api.NewJobApplicationController()
		r.GET("/job-applications", controller.List)                    // 查詢工作職位申請列表
		r.GET("/job-applications/actions/inquire", controller.Inquire) // 查詢工作職位申請詳情
		r.GET("/job-applications/actions/my-jobs", controller.MyJob)   // 查詢我的工作職位申請列表
	}
}
