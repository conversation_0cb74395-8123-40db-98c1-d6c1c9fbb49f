package model

import (
	"time"

	"github.com/Norray/xrocket/xmodel"
)

// 通知狀態常量
const (
	SystemNotificationUserReadY    = "Y" // 已讀
	SystemNotificationUserReadN    = "N" // 未讀
	SystemNotificationUserDeletedY = "Y" // 已刪除
	SystemNotificationUserDeletedN = "N" // 未刪除
)

// 系統通知用戶關聯表
type SystemNotificationUser struct {
	Id                   uint64     `json:"id" gorm:"primary_key"`
	SystemNotificationId uint64     `json:"systemNotificationId" gorm:"index:system_notification_idx;not null"` // 系統通知ID
	UserId               uint64     `json:"userId" gorm:"index:user_idx;not null"`                              // 用戶ID
	Read                 string     `json:"read" gorm:"type:varchar(1);index:status_idx;not null"`              // 狀態 Y N
	ReadTime             *time.Time `json:"readTime" gorm:"type:datetime(0)"`                                   // 已讀時間
	Deleted              string     `json:"deleted" gorm:"type:varchar(1);index:deleted_idx;not null"`          // 是否刪除 Y N
	xmodel.Model
}

func (m SystemNotificationUser) TableName() string {
	return "system_notification_user"
}

func (m SystemNotificationUser) SwaggerDescription() string {
	return "系統通知用戶關聯"
}
