package v1

import (
	"fmt"
	"net/http"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type DocumentController struct{}

func NewDocumentController() DocumentController {
	return DocumentController{}
}

// @Tags Document PDF
// @Summary Document PDF
// @Description
// @Router /v1/app/documents/actions/pdf [GET]
// @Produce  json
// @Security ApiKeyAuth
// @Param json body services.DocumentPDFReq true "parameter"
// @Success 200 "Success"
func (con DocumentController) DocumentPDF(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.DocumentPDFReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var document model.Document
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.DocumentService.CheckIdExistByUserId(db, &document, req.DocumentId, nc.GetJWTUserId())
			})
		msg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(msg) > 0 {
			nc.BadRequestResponseWithCheckMsg(msg)
			return
		}

		content, err := services.DocumentService.DocumentPDF(db, req)
		if xgorm.IsSqlErr(err) {
			nc.ErrorResponse(req, err)
			return
		}

		fileName := xtool.FileNameAddTimestamp(fmt.Sprintf("%s.pdf", document.DocumentNo.String))
		disposition := "attachment"
		if req.Disposition == "inline" {
			disposition = req.Disposition
		}
		c.Writer.Header().Set("Content-Disposition", fmt.Sprintf("%s; filename*=utf-8''%s", disposition, xtool.ReplacePlus(fileName)))
		http.ServeContent(c.Writer, c.Request, fileName, time.Now(), content)

	} else {
		nc.BadRequestResponse(err)
	}
}
