package routers

import (
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

var testFacilityId uint64

func TestFacilityEdit(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/facilities/actions/edit",
		UserId:           11,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "修改",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityEditReq{
					FacilityId:              3,
					BreakTimeEveryHour:      4,
					BreakTimeDurationMinute: 60,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityInquire(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/facilities/actions/inquire",
		UserId:           11,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityInquireReq{
					FacilityId: 3,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilitySearchForProfessional(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/facilities/actions/search",
		UserId:           15,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilitySearchForProfessionalReq{
					SelectedId: 0,
					Limit:      10,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilitySearchForSystem(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/facilities/actions/search",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilitySearchReq{
					//ProfessionalUserId: 15,
					SelectedId: 0,
					Limit:      10,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
