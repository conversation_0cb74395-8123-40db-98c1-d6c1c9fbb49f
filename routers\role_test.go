package routers

import (
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

func TestSystemRoleList(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/roles",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "系統角色列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.RoleListReq{},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSystemRoleSearch(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/roles/actions/search",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "系統角色搜索",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.RoleSearchReq{
					Name: "",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSystemRoleInquire(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/roles/actions/inquire",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "系統角色查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.RoleInquireReq{
					RoleId: 2,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSystemRoleCreate(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/roles/actions/create",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "創建系統角色",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常創建",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.RoleCreateReq{
					Name: "Admin",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSystemRoleEdit(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/roles/actions/edit",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "編輯系統角色",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常編輯",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.RoleEditReq{
					RoleId: 1,
					Name:   "修改後的角色",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSystemRoleDelete(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/roles/actions/delete",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "刪除系統角色",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常刪除",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.RoleDeleteReq{
					RoleId: 1,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityRoleList(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/facility/roles",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "機構角色列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.RoleListReq{},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityRoleSearch(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/facility/roles/actions/search",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "機構角色搜索",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.RoleSearchReq{
					Name: "",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityRoleInquire(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/facility/roles/actions/inquire",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "機構角色查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.RoleInquireReq{
					RoleId: 3,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
func TestFacilityRoleCreate(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/facility/roles/actions/create",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "創建機構角色",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常創建",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.RoleCreateReq{
					Name: "Facility Admin",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityRoleEdit(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/facility/roles/actions/edit",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "編輯機構角色",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常編輯",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.RoleEditReq{
					RoleId: 1,
					Name:   "修改後的機構角色",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityRoleDelete(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/facility/roles/actions/delete",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "刪除機構角色",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常刪除",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.RoleDeleteReq{
					RoleId: 1,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
