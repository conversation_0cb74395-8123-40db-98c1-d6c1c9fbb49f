package services

import (
	"encoding/json"
	"errors"
	"time"

	"github.com/Norray/xrocket/xgorm"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gorm.io/gorm"

	"github.com/Norray/medic-crew/model"
)

var TrainingService = new(trainingService)

type trainingService struct{}

// 檢查用戶是否提交了模塊中所有問題的答案
func (s *trainingService) CheckAllQuestionsAnswered(db *gorm.DB, moduleId uint64, answers []TrainingAnswer) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.training.module.incomplete_answers",
		Other: "Please answer all questions in this module before submitting.",
	}

	// 獲取模塊詳情
	module, err := s.getModuleById(db, moduleId)
	if err != nil {
		return false, i18n.Message{}, err
	}

	var detail model.TrainingModuleDetail
	err = json.Unmarshal([]byte(module.DetailJson), &detail)
	if err != nil {
		return false, i18n.Message{}, err
	}

	// 檢查是否提交了所有問題的答案
	totalQuestions := len(detail.Questions)
	submittedQuestions := len(answers)

	if submittedQuestions != totalQuestions {
		return false, msg, nil
	}

	// 檢查提交的問題序號是否與模塊中的問題序號匹配
	questionSeqMap := make(map[int32]bool)
	for _, question := range detail.Questions {
		questionSeqMap[question.Seq] = true
	}

	for _, answer := range answers {
		if !questionSeqMap[answer.QuestionSeq] {
			return false, msg, nil
		}
	}

	return true, i18n.Message{}, nil
}

// 檢查用戶是否已經完成指定模塊的所有答題
func (s *trainingService) CheckModuleNotCompleted(db *gorm.DB, userId uint64, moduleId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.training.module.already_completed",
		Other: "All questions in this module have been completed correctly.",
	}

	// 獲取用戶進度
	progress, err := s.GetUserProgress(db, userId, moduleId)
	if err != nil {
		return false, i18n.Message{}, err
	}

	// 如果沒有進度記錄，說明還沒開始，可以提交
	if progress == nil {
		return true, i18n.Message{}, nil
	}

	// 如果所有問題都已正確完成，則不允許重複提交
	if progress.QuestionComplete == "Y" {
		return false, msg, nil
	}

	return true, i18n.Message{}, nil
}

// 檢查用戶是否已經完成所有培訓
func (s *trainingService) CheckProfessionalModuleNotCompleted(db *gorm.DB, userId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.training.module.professional.all.not_completed",
		Other: "Please complete all training first.",
	}
	var err error
	// 獲取用戶進度
	var trainingModule model.TrainingModule
	if err = db.Table("training_module AS tm").
		Joins("JOIN professional_training_progress AS ptp ON ptp.training_module_id = tm.id").
		Where("ptp.user_id = ?", userId).
		Where("ptp.question_complete = ?", "N").
		First(&trainingModule).Error; xgorm.IsSqlErr(err) {
		return false, i18n.Message{}, err
	}
	if xgorm.IsNotFoundErr(err) {
		return true, i18n.Message{}, nil
	}
	return false, msg, nil
}

// 檢查用戶是否有權限訪問指定模塊
func (s *trainingService) CanAccessModule(db *gorm.DB, userId uint64, moduleId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.training.module.access_denied",
		Other: "Access denied. Please complete the previous module first.",
	}
	var err error

	// 獲取目標模塊的序號
	var targetModule model.TrainingModule
	err = db.Where("id = ?", moduleId).First(&targetModule).Error
	if xgorm.IsSqlErr(err) {
		return false, i18n.Message{}, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, i18n.Message{
			ID:    "checker.training.module.not_found",
			Other: "Training module not found.",
		}, nil
	}

	// 如果是第一個模塊，直接允許訪問
	if targetModule.Seq == 1 {
		return true, i18n.Message{}, nil
	}

	// 獲取前一個模塊
	var prevModule model.TrainingModule
	err = db.Where("seq = ? AND status = ?", targetModule.Seq-1, model.TrainingModuleStatusActive).First(&prevModule).Error
	if xgorm.IsSqlErr(err) {
		return false, i18n.Message{}, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, i18n.Message{
			ID:    "checker.training.module.prev_not_found",
			Other: "Previous training module not found.",
		}, nil
	}

	// 檢查前一個模塊是否已完成
	var progress model.ProfessionalTrainingProgress
	err = db.
		Where("user_id = ?", userId).
		Where("training_module_id = ?", prevModule.Id).
		First(&progress).Error
	if xgorm.IsSqlErr(err) {
		return false, i18n.Message{}, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil // 前一個模塊未開始
	}

	// 檢查前一個模塊是否完成
	if progress.QuestionComplete != "Y" {
		return false, msg, nil
	}

	return true, i18n.Message{}, nil
}

// 獲取培訓模塊列表
func (s *trainingService) GetModuleList(db *gorm.DB) ([]model.TrainingModule, error) {
	var modules []model.TrainingModule
	err := db.Where("status = ?", model.TrainingModuleStatusActive).
		Order("seq ASC").
		Find(&modules).Error
	return modules, err
}

// 獲取模塊詳情（包含順序訪問控制）
func (s *trainingService) GetModuleDetail(db *gorm.DB, userId uint64, moduleId uint64) (*model.TrainingModule, *model.TrainingModuleDetail, error) {
	// 獲取模塊信息
	var module model.TrainingModule
	err := db.Where("id = ? AND status = ?", moduleId, model.TrainingModuleStatusActive).First(&module).Error
	if err != nil {
		return nil, nil, err
	}

	// 解析模塊詳情JSON
	var detail model.TrainingModuleDetail
	err = json.Unmarshal([]byte(module.DetailJson), &detail)
	if err != nil {
		return &module, nil, err
	}

	return &module, &detail, nil
}

// 獲取模塊問題列表（附帶已回答答案）
func (s *trainingService) GetModuleQuestions(db *gorm.DB, userId uint64, moduleId uint64) ([]model.TrainingQuestion, []model.ProfessionalTrainingAnswer, error) {
	// 獲取模塊信息
	var module model.TrainingModule
	err := db.Where("id = ? AND status = ?", moduleId, model.TrainingModuleStatusActive).First(&module).Error
	if err != nil {
		return nil, nil, err
	}

	// 解析模塊詳情JSON獲取問題列表
	var detail model.TrainingModuleDetail
	err = json.Unmarshal([]byte(module.DetailJson), &detail)
	if err != nil {
		return nil, nil, err
	}

	// 獲取用戶已回答的答案
	var progress model.ProfessionalTrainingProgress
	err = db.Where("user_id = ? AND training_module_id = ?", userId, moduleId).First(&progress).Error
	if err != nil && !xgorm.IsNotFoundErr(err) {
		return detail.Questions, nil, err
	}

	var answers []model.ProfessionalTrainingAnswer
	if err == nil {
		answers, err = progress.UnmarshalAnswers()
		if err != nil {
			return detail.Questions, nil, err
		}
	}

	return detail.Questions, answers, nil
}

// 回答問題（順序回答控制）
func (s *trainingService) AnswerQuestion(db *gorm.DB, userId uint64, moduleId uint64, questionSeq int32, optionSeq int32) error {
	// 獲取模塊信息
	var module model.TrainingModule
	err := db.Where("id = ? AND status = ?", moduleId, model.TrainingModuleStatusActive).First(&module).Error
	if err != nil {
		return err
	}

	// 解析模塊詳情JSON
	var detail model.TrainingModuleDetail
	err = json.Unmarshal([]byte(module.DetailJson), &detail)
	if err != nil {
		return err
	}

	// 根據questionSeq找到對應問題
	var targetQuestion *model.TrainingQuestion
	for i := range detail.Questions {
		if detail.Questions[i].Seq == questionSeq {
			targetQuestion = &detail.Questions[i]
			break
		}
	}
	if targetQuestion == nil {
		return errors.New("問題不存在")
	}

	// 檢查選項序號是否有效
	if optionSeq < 1 || int(optionSeq) > len(targetQuestion.Options) {
		return errors.New("無效的選項序號")
	}

	// 獲取用戶培訓進度 （回答問題前，進度一定存在，因為已經觀看視頻）
	var progress model.ProfessionalTrainingProgress
	err = db.Where("user_id = ? AND training_module_id = ?", userId, moduleId).First(&progress).Error
	if err != nil {
		return err
	}

	// 解析現有答案
	existingAnswers, err := progress.UnmarshalAnswers()
	if err != nil {
		return err
	}

	// 檢查是否已經回答過此問題
	found := false
	for i, answer := range existingAnswers {
		if answer.QuestionSeq == questionSeq {
			// 更新現有答案
			existingAnswers[i].SelectedOption = optionSeq
			existingAnswers[i].IsCorrect = targetQuestion.Options[optionSeq-1].IsCorrect
			existingAnswers[i].AnswerTime = time.Now().UTC().Format(time.RFC3339)
			found = true
			break
		}
	}

	// 如果沒有找到現有答案，添加新答案
	if !found {
		newAnswer := model.ProfessionalTrainingAnswer{
			QuestionSeq:    questionSeq,
			SelectedOption: optionSeq,
			IsCorrect:      targetQuestion.Options[optionSeq-1].IsCorrect,
			AnswerTime:     time.Now().UTC().Format(time.RFC3339),
		}
		existingAnswers = append(existingAnswers, newAnswer)
	}

	// 更新進度記錄
	err = progress.MarshalAnswers(existingAnswers)
	if err != nil {
		return err
	}

	// 檢查是否所有問題都回答正確
	allCorrect, err := s.checkAllQuestionsCorrect(db, moduleId, existingAnswers)
	if err != nil {
		return err
	}

	if allCorrect {
		nowTime := time.Now().UTC()
		progress.QuestionComplete = "Y"
		progress.Status = model.TrainingProgressStatusCompleted
		progress.CompleteTime = &nowTime
	}
	if err = db.Save(&progress).Error; err != nil {
		return err
	}

	return nil
}

// 檢查所有問題是否都回答正確
func (s *trainingService) checkAllQuestionsCorrect(db *gorm.DB, moduleId uint64, answers []model.ProfessionalTrainingAnswer) (bool, error) {
	// 獲取模塊信息
	var module model.TrainingModule
	err := db.Where("id = ? AND status = ?", moduleId, model.TrainingModuleStatusActive).First(&module).Error
	if err != nil {
		return false, err
	}

	// 解析模塊詳情JSON獲取問題列表
	var detail model.TrainingModuleDetail
	err = json.Unmarshal([]byte(module.DetailJson), &detail)
	if err != nil {
		return false, err
	}

	// 檢查每個問題是否都有正確答案
	for _, question := range detail.Questions {
		found := false
		for _, answer := range answers {
			if answer.QuestionSeq == question.Seq {
				if answer.IsCorrect != "Y" {
					return false, nil // 有錯誤答案
				}
				found = true
				break
			}
		}
		if !found {
			return false, nil // 有未回答的問題
		}
	}

	return true, nil
}

// region ---------------------------------------------------- 檢查視頻是否已觀看完成 ----------------------------------------------------

// 檢查用戶是否已觀看完視頻
func (s *trainingService) CheckVideoWatched(db *gorm.DB, userId uint64, moduleId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.training.video.not_watched",
		Other: "Please watch the training video first.",
	}
	var err error

	// 檢查模塊是否存在且有效
	var module model.TrainingModule
	err = db.Where("id = ? AND status = ?", moduleId, model.TrainingModuleStatusActive).First(&module).Error
	if xgorm.IsSqlErr(err) {
		return false, i18n.Message{}, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, i18n.Message{
			ID:    "checker.training.module.not_found",
			Other: "Training module not found.",
		}, nil
	}

	// 檢查用戶培訓進度
	var progress model.ProfessionalTrainingProgress
	err = db.Where("user_id = ? AND training_module_id = ?", userId, moduleId).First(&progress).Error
	if xgorm.IsSqlErr(err) {
		return false, i18n.Message{}, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil // 未開始觀看
	}

	// 檢查視頻是否已觀看完成
	if progress.VideoWatchComplete != "Y" {
		return false, msg, nil
	}

	return true, i18n.Message{}, nil
}

// endregion ---------------------------------------------------- 檢查視頻是否已觀看完成 ----------------------------------------------------

// region ---------------------------------------------------- 獲取用戶在指定模塊的培訓進度 ----------------------------------------------------

func (s *trainingService) GetUserProgress(db *gorm.DB, userId uint64, moduleId uint64) (*model.ProfessionalTrainingProgress, error) {
	var progress model.ProfessionalTrainingProgress
	err := db.Where("user_id = ? AND training_module_id = ?", userId, moduleId).First(&progress).Error
	if xgorm.IsNotFoundErr(err) {
		return nil, nil // 未開始
	}
	if err != nil {
		return nil, err
	}

	return &progress, nil
}

// endregion ---------------------------------------------------- 獲取用戶在指定模塊的培訓進度 ----------------------------------------------------

// region ---------------------------------------------------- API Service Methods ----------------------------------------------------

// region ---------------------------------------------------- 獲取培訓模塊列表 ----------------------------------------------------

// 培訓模塊列表請求
type TrainingModuleListReq struct {
	UserId uint64 `form:"-" json:"-"` // 用戶ID，在控制器中設置
}

// 培訓模塊列表響應
type TrainingModuleListResp struct {
	Id                 uint64 `json:"id"`
	Title              string `json:"title"`
	Description        string `json:"description"`
	Seq                int32  `json:"seq"`
	CanAccess          string `json:"canAccess"`          // 是否可以訪問（基於順序控制）Y/N
	IsCompleted        string `json:"isCompleted"`        // 是否已完成 Y/N
	VideoWatchComplete string `json:"videoWatchComplete"` // 是否觀看完成 Y/N
}

// ModuleForProfessional 獲取專業人員培訓模塊列表（API 專用，優化版）
func (s *trainingService) ModuleForProfessional(db *gorm.DB, req TrainingModuleListReq) ([]TrainingModuleListResp, error) {
	// 1. 獲取所有有效的培訓模塊（按順序排列）
	var modules []model.TrainingModule
	err := db.Where("status = ?", model.TrainingModuleStatusActive).
		Order("seq ASC").
		Find(&modules).Error
	if err != nil {
		return nil, err
	}

	if len(modules) == 0 {
		return []TrainingModuleListResp{}, nil
	}

	// 2. 批量獲取用戶所有模塊的進度信息（一次查詢）
	var moduleIds []uint64
	for _, module := range modules {
		moduleIds = append(moduleIds, module.Id)
	}

	var userProgresses []model.ProfessionalTrainingProgress
	err = db.Where("user_id = ? AND training_module_id IN ?", req.UserId, moduleIds).
		Find(&userProgresses).Error
	if err != nil {
		return nil, err
	}

	// 3. 構建進度映射，方便快速查找
	progressMap := make(map[uint64]*model.ProfessionalTrainingProgress)
	for i := range userProgresses {
		progressMap[userProgresses[i].TrainingModuleId] = &userProgresses[i]
	}

	// 4. 計算訪問權限和完成狀態
	var resp []TrainingModuleListResp
	var lastCompletedSeq int32 = 0

	// 找到最後一個完成的模塊序號
	for _, module := range modules {
		if progress, exists := progressMap[module.Id]; exists && progress.QuestionComplete == "Y" {
			if module.Seq > lastCompletedSeq {
				lastCompletedSeq = module.Seq
			}
		}
	}

	// 5. 構建響應列表
	for _, module := range modules {
		progress := progressMap[module.Id]

		// 計算訪問權限：第一個模塊或前一個模塊已完成
		canAccess := "N"
		if module.Seq == 1 || module.Seq <= lastCompletedSeq+1 {
			canAccess = "Y"
		}

		// 計算完成狀態
		isCompleted := "N"
		if progress != nil && progress.QuestionComplete == "Y" {
			isCompleted = "Y"
		}
		videoWatchComplete := "N"
		if progress != nil && progress.VideoWatchComplete == "Y" {
			videoWatchComplete = "Y"
		}

		resp = append(resp, TrainingModuleListResp{
			Id:                 module.Id,
			Title:              module.Title,
			Description:        module.Description,
			Seq:                module.Seq,
			CanAccess:          canAccess,
			VideoWatchComplete: videoWatchComplete,
			IsCompleted:        isCompleted,
		})
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 獲取培訓模塊列表 ----------------------------------------------------

// region ---------------------------------------------------- 獲取培訓模塊詳情 ----------------------------------------------------

// 培訓模塊詳情請求
type TrainingModuleDetailReq struct {
	ModuleId uint64 `form:"moduleId" binding:"required" json:"moduleId"`
	UserId   uint64 `form:"-" json:"-"` // 用戶ID，在控制器中設置
}

// 培訓模塊基本信息（不包含敏感字段）
type TrainingModuleBasic struct {
	Id          uint64 `json:"id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Seq         int32  `json:"seq"`
}

// 培訓問題選項（不包含正確答案）
type TrainingQuestionOptionSafe struct {
	OptionText string `json:"optionText"`
	Seq        int32  `json:"seq"`
}

// 培訓問題（不包含正確答案）
type TrainingQuestionSafe struct {
	TrainingModuleId uint64                       `json:"trainingModuleId"`
	QuestionText     string                       `json:"questionText"`
	QuestionType     string                       `json:"questionType"` // SINGLE_CHOICE
	Options          []TrainingQuestionOptionSafe `json:"options"`
	Seq              int32                        `json:"seq"`
}

// 培訓模塊詳情響應
type TrainingModuleDetailResp struct {
	Module             TrainingModuleBasic       `json:"module"`
	Video              model.TrainingModuleVideo `json:"video"`
	Questions          []TrainingQuestionSafe    `json:"questions"`
	VideoWatchComplete string                    `json:"videoWatchComplete"` // Y/N
	QuestionComplete   string                    `json:"questionComplete"`   // Y/N
}

// ModuleDetail 獲取培訓模塊詳情
func (s *trainingService) ModuleDetail(db *gorm.DB, req TrainingModuleDetailReq) (TrainingModuleDetailResp, error) {
	resp := TrainingModuleDetailResp{
		VideoWatchComplete: "N",
		QuestionComplete:   "N",
	}
	// 獲取模塊詳情
	module, detail, err := s.GetModuleDetail(db, req.UserId, req.ModuleId)
	if err != nil {
		return resp, err
	}

	// 創建清理過的模塊基本信息（移除 detailJson 和 status）
	resp.Module = TrainingModuleBasic{
		Id:          module.Id,
		Title:       module.Title,
		Description: module.Description,
		Seq:         module.Seq,
	}

	// 創建清理過的問題列表（移除 isCorrect）
	var safeQuestions []TrainingQuestionSafe
	for _, question := range detail.Questions {
		var safeOptions []TrainingQuestionOptionSafe
		for _, option := range question.Options {
			safeOptions = append(safeOptions, TrainingQuestionOptionSafe{
				OptionText: option.OptionText,
				Seq:        option.Seq,
			})
		}

		safeQuestions = append(safeQuestions, TrainingQuestionSafe{
			QuestionText: question.QuestionText,
			QuestionType: question.QuestionType,
			Options:      safeOptions,
			Seq:          question.Seq,
		})
	}
	progress, err := s.GetUserProgress(db, req.UserId, req.ModuleId)
	if err != nil {
		return resp, err
	}
	if progress != nil {
		resp.VideoWatchComplete = progress.VideoWatchComplete
		resp.QuestionComplete = progress.QuestionComplete
	}

	resp.Video = detail.Video
	resp.Questions = safeQuestions
	return resp, nil
}

// endregion ---------------------------------------------------- 獲取培訓模塊詳情 ----------------------------------------------------

// region ---------------------------------------------------- 獲取培訓模塊問題列表 ----------------------------------------------------

// 培訓模塊問題請求
type TrainingModuleQuestionsReq struct {
	ModuleId uint64 `form:"moduleId" binding:"required" json:"moduleId"`
	UserId   uint64 `form:"-" json:"-"` // 用戶ID，在控制器中設置
}

// 培訓模塊問題響應項目
type TrainingQuestionWithAnswer struct {
	model.TrainingQuestion
	SelectedOption int32  `json:"selectedOption"` // 用戶選擇的選項序號，未回答時為 0
	IsCorrect      string `json:"isCorrect"`      // 是否正確 Y/N，未回答時為空字符串
}

// 培訓模塊問題響應
type TrainingModuleQuestionsResp struct {
	Questions []TrainingQuestionWithAnswer `json:"questions"`
}

// ModuleQuestions 獲取培訓模塊問題列表
func (s *trainingService) ModuleQuestions(db *gorm.DB, req TrainingModuleQuestionsReq) (*TrainingModuleQuestionsResp, error) {
	// 獲取問題列表
	questions, answers, err := s.GetModuleQuestions(db, req.UserId, req.ModuleId)
	if err != nil {
		return nil, err
	}

	// 創建答案映射，方便快速查找
	answerMap := make(map[int32]*model.ProfessionalTrainingAnswer)
	for i := range answers {
		answerMap[answers[i].QuestionSeq] = &answers[i]
	}

	// 合併問題和對應的用戶答案
	var questionsWithAnswers []TrainingQuestionWithAnswer
	for _, question := range questions {
		questionWithAnswer := TrainingQuestionWithAnswer{
			TrainingQuestion: question,
			SelectedOption:   0,  // 默認未回答
			IsCorrect:        "", // 默認未回答
		}

		// 如果用戶已回答此問題，填入答案信息
		if fullAnswer, exists := answerMap[question.Seq]; exists {
			questionWithAnswer.SelectedOption = fullAnswer.SelectedOption
			questionWithAnswer.IsCorrect = fullAnswer.IsCorrect
		}

		questionsWithAnswers = append(questionsWithAnswers, questionWithAnswer)
	}

	resp := &TrainingModuleQuestionsResp{
		Questions: questionsWithAnswers,
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 獲取培訓模塊問題列表 ----------------------------------------------------

// region ---------------------------------------------------- 回答培訓問題 ----------------------------------------------------

// 單個答案結構
type TrainingAnswer struct {
	QuestionSeq int32 `json:"questionSeq" binding:"required"` // 問題序號
	OptionSeq   int32 `json:"optionSeq" binding:"required"`   // 選項序號
}

// 回答問題請求
type TrainingAnswerQuestionReq struct {
	ModuleId uint64           `json:"moduleId" binding:"required"` // 模塊ID
	Answers  []TrainingAnswer `json:"answers" binding:"required"`  // 答案列表
	UserId   uint64           `json:"-"`                           // 用戶ID，在控制器中設置
}

// 單個答案結果
type TrainingAnswerResult struct {
	QuestionSeq    int32  `json:"questionSeq"`    // 問題序號
	SelectedOption int32  `json:"selectedOption"` // 用戶選擇的選項序號
	IsCorrect      string `json:"isCorrect"`      // Y/N
}

// 回答問題響應
type TrainingAnswerQuestionResp struct {
	Results     []TrainingAnswerResult `json:"results"`     // 每個答案的結果
	AllComplete string                 `json:"allComplete"` // 是否所有問題都完成 Y/N
}

// Answer 回答培訓問題（批量提交）
func (s *trainingService) Answer(db *gorm.DB, req TrainingAnswerQuestionReq) (*TrainingAnswerQuestionResp, error) {
	// 獲取問題詳情來檢查答案是否正確
	module, err := s.getModuleById(db, req.ModuleId)
	if err != nil {
		return nil, err
	}

	var detail model.TrainingModuleDetail
	err = json.Unmarshal([]byte(module.DetailJson), &detail)
	if err != nil {
		return nil, err
	}

	// 創建問題映射表以便快速查找
	questionMap := make(map[int32]model.TrainingQuestion)
	for _, question := range detail.Questions {
		questionMap[question.Seq] = question
	}

	// 處理每個答案並收集結果
	var results []TrainingAnswerResult
	for _, answer := range req.Answers {
		isCorrect := "N"

		// 檢查問題是否存在
		if question, exists := questionMap[answer.QuestionSeq]; exists {
			// 檢查選項是否有效且正確
			if int(answer.OptionSeq) > 0 && int(answer.OptionSeq) <= len(question.Options) {
				if question.Options[answer.OptionSeq-1].IsCorrect == "Y" {
					isCorrect = "Y"
				}
			}
		}

		// 提交單個答案
		err = s.AnswerQuestion(db, req.UserId, req.ModuleId, answer.QuestionSeq, answer.OptionSeq)
		if err != nil {
			return nil, err
		}

		results = append(results, TrainingAnswerResult{
			QuestionSeq:    answer.QuestionSeq,
			SelectedOption: answer.OptionSeq,
			IsCorrect:      isCorrect,
		})
	}

	// 檢查是否全部完成
	progress, err := s.GetUserProgress(db, req.UserId, req.ModuleId)
	if err != nil {
		return nil, err
	}
	allComplete := "N"
	if progress != nil && progress.QuestionComplete == "Y" {
		allComplete = "Y"
	}

	resp := &TrainingAnswerQuestionResp{
		Results:     results,
		AllComplete: allComplete,
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 回答培訓問題 ----------------------------------------------------

// region ---------------------------------------------------- 標記視頻觀看完成 ----------------------------------------------------

// 標記視頻觀看完成請求
type TrainingMarkVideoWatchedReq struct {
	ModuleId uint64 `json:"moduleId" binding:"required"`
	UserId   uint64 `json:"-"` // 用戶ID，在控制器中設置
}

// VideoWatched 標記視頻觀看完成
func (s *trainingService) VideoWatched(db *gorm.DB, req TrainingMarkVideoWatchedReq) error {
	// 檢查模塊是否存在且有效
	var module model.TrainingModule
	err := db.Where("id = ? AND status = ?", req.ModuleId, model.TrainingModuleStatusActive).First(&module).Error
	if err != nil {
		return err
	}

	// 獲取或創建用戶培訓進度
	var progress model.ProfessionalTrainingProgress
	err = db.Where("user_id = ? AND training_module_id = ?", req.UserId, req.ModuleId).First(&progress).Error
	if xgorm.IsNotFoundErr(err) {
		// 創建新的進度記錄
		nowTime := time.Now().UTC()
		progress = model.ProfessionalTrainingProgress{
			UserId:             req.UserId,
			TrainingModuleId:   req.ModuleId,
			VideoWatchComplete: "Y",
			QuestionComplete:   "N",
			AnswersJson:        "[]",
			Status:             model.TrainingProgressStatusAnswering,
			StartTime:          &nowTime,
			CompleteTime:       nil,
		}
		err = db.Create(&progress).Error
		if err != nil {
			return err
		}
	} else if err != nil {
		return err
	} else {
		// 更新現有記錄
		progress.VideoWatchComplete = "Y"
		if progress.StartTime == nil {
			nowTime := time.Now().UTC()
			progress.StartTime = &nowTime
		}
		if err = db.Save(&progress).Error; err != nil {
			return err
		}
	}

	return nil
}

// endregion ---------------------------------------------------- 標記視頻觀看完成 ----------------------------------------------------

// region ---------------------------------------------------- 獲取用戶培訓進度 ----------------------------------------------------

// 獲取用戶培訓進度請求
type TrainingUserProgressReq struct {
	ModuleId uint64 `form:"moduleId" binding:"required" json:"moduleId"`
	UserId   uint64 `form:"-" json:"-"` // 用戶ID，在控制器中設置
}

// 獲取用戶培訓進度響應
type TrainingUserProgressResp struct {
	UserId             uint64     `json:"userId"`
	TrainingModuleId   uint64     `json:"trainingModuleId"`
	VideoWatchComplete string     `json:"videoWatchComplete"` // Y/N
	QuestionComplete   string     `json:"questionComplete"`   // Y/N
	Status             string     `json:"status"`             // WATCHING/ANSWERING/COMPLETED
	StartTime          *time.Time `json:"startTime"`
	CompleteTime       *time.Time `json:"completeTime"`
}

// UserProgress 獲取用戶培訓進度
func (s *trainingService) UserProgress(db *gorm.DB, req TrainingUserProgressReq) (*TrainingUserProgressResp, error) {
	progress, err := s.GetUserProgress(db, req.UserId, req.ModuleId)
	if err != nil {
		return nil, err
	}

	if progress == nil {
		return nil, nil // 沒有進度記錄時返回 nil
	}

	resp := &TrainingUserProgressResp{
		UserId:             progress.UserId,
		TrainingModuleId:   progress.TrainingModuleId,
		VideoWatchComplete: progress.VideoWatchComplete,
		QuestionComplete:   progress.QuestionComplete,
		Status:             progress.Status,
		StartTime:          progress.StartTime,
		CompleteTime:       progress.CompleteTime,
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 獲取用戶培訓進度 ----------------------------------------------------

// getModuleById 根據ID獲取模塊（內部方法）
func (s *trainingService) getModuleById(db *gorm.DB, moduleId uint64) (*model.TrainingModule, error) {
	var module model.TrainingModule
	err := db.Where("id = ? AND status = ?", moduleId, model.TrainingModuleStatusActive).First(&module).Error
	return &module, err
}

// endregion ---------------------------------------------------- API Service Methods ----------------------------------------------------
