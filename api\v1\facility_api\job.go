package facility_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type JobController struct {
	v1.CommonController
}

func NewJobController() JobController {
	return JobController{}
}

// @Tags Job
// @Summary 查詢工作職位列表
// @Description 查詢工作職位列表，支持分頁和條件過濾
// @Router /v1/facility/jobs [GET]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.JobListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Param sortingKey query string false "hourlyRate 時薪,createTime 建立時間,beginTime 開始時間"
// @Param sortingType query string false "a=順序,d=倒序"
// @Success 200 {object} []services.JobListResp "Success"
func (con JobController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobListReq
	var pageSet xresp.PageSet
	var sortSet xresp.SortingSet
	_ = c.ShouldBindQuery(&pageSet)
	_ = c.ShouldBindQuery(&sortSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.JobService.List(db, req, &pageSet, sortSet, "FACILITY")
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Job
// @Summary 搜索工作職位
// @Description 搜索工作職位，支持條件過濾和優先排序
// @Router /v1/facility/jobs/actions/search [GET]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.JobSearchReq true "parameter"
// @Success 200 {object} []services.JobSearchResp "Success"
func (con JobController) Search(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobSearchReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.JobService.Search(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Job
// @Summary 創建工作職位
// @Description 創建工作職位
// @Router /v1/facility/jobs/actions/create [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.JobCreateReq true "parameter"
// @Success 200 {object} services.JobCreateResp "Success"
func (con JobController) Create(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var err error
	var req services.JobCreateReq
	var draftReq services.JobCreateDraftReq

	// 先嘗試以草稿模式綁定，檢查是否為草稿模式
	if err = c.ShouldBindBodyWith(&draftReq, binding.JSON); err != nil {
		nc.BadRequestResponse(err)
		return
	}
	if draftReq.Draft == "Y" {
		_ = copier.Copy(&req, draftReq)
	} else if err = c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		nc.BadRequestResponse(err)
		return
	}

	db := xgorm.DB.WithContext(c)

	if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
		nc.NoPermissionResponse(resource.ForbiddenMsg)
		return
	}
	// Checker
	checker := xapp.NewCK(c)

	// 檢查Selection
	con.CheckSelectionExist(checker, db, model.SelectionTypeProfessionalProfession, req.PositionProfession)
	con.CheckSelectionsExist(checker, db, model.SelectionTypeLanguage, req.Language)
	switch req.PositionProfession {
	case model.JobPositionProfessionMedicalPractitioner:
		con.CheckSelectionsExist(checker, db, model.SelectionTypeExperienceLevelMedicalPractitioner, req.MinExperienceLevel)
	case model.JobPositionProfessionRegisteredNurse:
		con.CheckSelectionsExist(checker, db, model.SelectionTypeExperienceLevelRegisteredNurse, req.MinExperienceLevel)
	case model.JobPositionProfessionEnrolledNurse, model.JobPositionProfessionPersonalCareWorker:
		con.CheckSelectionsExist(checker, db, model.SelectionTypeExperienceLevelGeneral, req.MinExperienceLevel)
	}
	if req.Specialisation != "" {
		checker.Run(func() (bool, i18n.Message, error) {
			return services.ProfessionalProfileService.CheckPreferredSpecialities(db, req.PositionProfession, req.Specialisation)
		})
	}

	if req.Draft != "Y" {
		var serviceLocation model.ServiceLocation
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.JobService.CheckShiftTimeRange(req.JobShiftItems)
			}).
			Run(func() (bool, i18n.Message, error) {
				if req.PublishNow != "Y" {
					return services.JobService.CheckPublishTime(db, *req.PublishTime)
				}
				return true, i18n.Message{}, nil
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.ServiceLocationService.CheckIdExist(db, &serviceLocation, req.ServiceLocationId, req.FacilityId)
			}).
			Run(func() (bool, i18n.Message, error) {
				beginTime, endTime := services.JobService.GetShiftTimeRange(req.JobShiftItems)
				if beginTime != nil && endTime != nil {
					beginStr := beginTime.UTC().Format(xtool.DateDayA)
					endStr := endTime.UTC().Format(xtool.DateDayA)
					return services.FacilityAgreementService.CheckJobTimeInRange(db, req.FacilityId, beginStr, endStr)
				}
				return true, i18n.Message{}, nil
			}).
			Run(func() (bool, i18n.Message, error) {
				beginTime, _ := services.JobService.GetShiftTimeRange(req.JobShiftItems)
				return services.JobService.CheckCanPublish(model.JobStatusPending, beginTime, serviceLocation.Timezone)
			}).
			Run(func() (bool, i18n.Message, error) {
				if req.Draft == "Y" || len(req.Files) == 0 {
					return true, i18n.Message{}, nil
				}
				return services.FacilityFileService.CheckIdsExist(db, req.FacilityId, req.Files)
			})
	}

	checkMsg, err := checker.Result()
	if err != nil {
		nc.ErrorResponse(req, err)
		return
	}
	if len(checkMsg) > 0 {
		nc.BadRequestResponseWithCheckMsg(checkMsg)
		return
	}

	// 設置創建者和更新者ID
	req.CreatedUserId = nc.GetJWTUserId()
	req.UpdatedUserId = nc.GetJWTUserId()
	req.ScheduleTemplate = model.JobScheduleTemplateN
	tx := db.Begin()
	resp, err := services.JobService.Create(tx, req)
	if err != nil {
		tx.Rollback()
		nc.ErrorResponse(req, err)
		return
	}
	tx.Commit()
	nc.OKResponse(resp)
}

// @Tags Job
// @Summary 編輯工作職位
// @Description 編輯工作職位
// @Router /v1/facility/jobs/actions/edit [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.JobEditReq true "parameter"
// @Success 200 "Success"
func (con JobController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobEditReq
	var draftReq services.JobEditDraftReq

	// 先嘗試以草稿模式綁定，檢查是否為草稿模式
	if err := c.ShouldBindBodyWith(&draftReq, binding.JSON); err != nil {
		nc.BadRequestResponse(err)
		return
	}
	if draftReq.Draft == "Y" {
		_ = copier.Copy(&req, draftReq)
	} else if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		nc.BadRequestResponse(err)
		return
	}

	db := xgorm.DB.WithContext(c)
	if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
		nc.NoPermissionResponse(resource.ForbiddenMsg)
		return
	}
	// Checker
	var job model.Job
	checker := xapp.NewCK(c, true)
	checker.
		Run(func() (bool, i18n.Message, error) {
			return services.JobService.CheckIdExist(db, &job, req.JobId, req.FacilityId)
		}).
		Run(func() (bool, i18n.Message, error) {
			return services.JobService.CheckCanEdit(db, req.FacilityId, req.JobId)
		})

	if req.Draft != "Y" {
		var serviceLocation model.ServiceLocation
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.JobService.CheckShiftTimeRange(req.JobShiftItems)
			}).
			Run(func() (bool, i18n.Message, error) {
				if req.PublishNow != "Y" {
					return services.JobService.CheckPublishTime(db, *req.PublishTime)
				}
				return true, i18n.Message{}, nil
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.ServiceLocationService.CheckIdExist(db, &serviceLocation, req.ServiceLocationId, req.FacilityId)
			}).
			Run(func() (bool, i18n.Message, error) {
				beginTime, endTime := services.JobService.GetShiftTimeRange(req.JobShiftItems)
				if beginTime != nil && endTime != nil {
					beginStr := beginTime.UTC().Format(xtool.DateDayA)
					endStr := endTime.UTC().Format(xtool.DateDayA)
					return services.FacilityAgreementService.CheckJobTimeInRange(db, req.FacilityId, beginStr, endStr)
				}
				return true, i18n.Message{}, nil
			}).
			Run(func() (bool, i18n.Message, error) {
				beginTime, _ := services.JobService.GetShiftTimeRange(req.JobShiftItems)
				return services.JobService.CheckCanPublish(model.JobStatusPending, beginTime, serviceLocation.Timezone)
			}).
			Run(func() (bool, i18n.Message, error) {
				if req.Draft == "Y" || len(req.Files) == 0 {
					return true, i18n.Message{}, nil
				}
				return services.FacilityFileService.CheckIdsExist(db, req.FacilityId, req.Files)
			})
	}

	// 檢查Selection
	con.CheckSelectionExist(checker, db, model.SelectionTypeProfessionalProfession, req.PositionProfession)
	con.CheckSelectionsExist(checker, db, model.SelectionTypeLanguage, req.Language)
	switch req.PositionProfession {
	case model.JobPositionProfessionMedicalPractitioner:
		con.CheckSelectionsExist(checker, db, model.SelectionTypeExperienceLevelMedicalPractitioner, req.MinExperienceLevel)
	case model.JobPositionProfessionRegisteredNurse:
		con.CheckSelectionsExist(checker, db, model.SelectionTypeExperienceLevelRegisteredNurse, req.MinExperienceLevel)
	case model.JobPositionProfessionEnrolledNurse, model.JobPositionProfessionPersonalCareWorker:
		con.CheckSelectionsExist(checker, db, model.SelectionTypeExperienceLevelGeneral, req.MinExperienceLevel)
	}

	if req.Specialisation != "" {
		checker.Run(func() (bool, i18n.Message, error) {
			return services.ProfessionalProfileService.CheckPreferredSpecialities(db, req.PositionProfession, req.Specialisation)
		})
	}

	checkMsg, err := checker.Result()
	if err != nil {
		nc.ErrorResponse(req, err)
		return
	}
	if len(checkMsg) > 0 {
		nc.BadRequestResponseWithCheckMsg(checkMsg)
		return
	}

	// 設置更新者ID
	req.UpdatedUserId = nc.GetJWTUserId()

	tx := db.Begin()
	err = services.JobService.Edit(tx, req)
	if err != nil {
		tx.Rollback()
		nc.ErrorResponse(req, err)
		return
	}
	tx.Commit()
	nc.OKResponse(nil)
}

// @Tags Job
// @Summary 查詢工作職位詳情
// @Description 查詢工作職位詳情
// @Router /v1/facility/jobs/actions/inquire [GET]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.JobInquireReq true "parameter"
// @Success 200 {object} services.JobInquireResp "Success"
func (con JobController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.JobService.CheckIdExist(db, &model.Job{}, req.JobId, req.FacilityId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		resp, err := services.JobService.Inquire(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Job
// @Summary 刪除工作職位
// @Description 刪除工作職位
// @Router /v1/facility/jobs/actions/delete [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.JobDeleteReq true "parameter"
// @Success 200 "Success"
func (con JobController) Delete(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobDeleteReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.JobService.CheckIdExist(db, &model.Job{}, req.JobId, req.FacilityId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.JobService.CheckCanDelete(db, req.FacilityId, req.JobId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.JobService.Delete(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Job
// @Summary 更新工作職位狀態
// @Description 更新工作職位狀態
// @Router /v1/facility/jobs/actions/update-status [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.JobUpdateStatusReq true "parameter"
// @Success 200 "Success"
func (con JobController) UpdateStatus(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobUpdateStatusReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.ReqUserId = nc.GetJWTUserId()

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		// Checker
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.JobService.CheckIdExist(db, &model.Job{}, req.JobId, req.FacilityId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.JobService.CheckCanUpdateStatus(db, req.JobId, req.Status)
			})
		checkMsg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.JobService.UpdateStatus(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Job
// @Summary 更新工作日曆備註
// @Description 更新工作的日曆備註信息
// @Router /v1/facility/jobs/actions/update-calendar-note [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.JobUpdateCalendarNoteReq true "parameter"
// @Success 200 "Success"
func (con JobController) UpdateCalendarNote(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobUpdateCalendarNoteReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.Run(func() (bool, i18n.Message, error) {
			return services.JobService.CheckCanUpdateCalendarNote(db, req.JobId, req.FacilityId)
		})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.JobService.UpdateCalendarNote(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Job
// @Summary 邀請專業人士
// @Description 邀請專業人士
// @Router /v1/facility/jobs/actions/invite [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.JobInviteProfessionalReq true "parameter"
// @Success 200 "Success"
func (con JobController) InviteProfessional(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobInviteProfessionalReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var checkMsg []string
		var jobApplication model.JobApplication
		checker := xapp.NewCK(c, true)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.JobApplicationService.CheckIdExist(db, &jobApplication, req.JobApplicationId)
			})

		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		var alertMsg []string
		alerter := xapp.NewCK(c)
		alerter.
			Run(func() (bool, i18n.Message, error) { // 檢查是否可以邀請專業人士
				return services.JobService.CheckCanInviteProfessional(db, req.FacilityId, req.JobId)
			})
		alertMsg, err = alerter.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(alertMsg) > 0 {
			nc.AlertResponse(alertMsg)
			return
		}
		req.InviterUserId = nc.GetJWTUserId()
		tx := db.Begin()
		err = services.JobService.InviteProfessional(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Job
// @Summary 撤回邀請
// @Description 撤回邀請
// @Router /v1/facility/jobs/actions/withdraw-invite [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.JobWithdrawInviteReq true "parameter"
// @Success 200 "Success"
func (con JobController) WithdrawInvite(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobWithdrawInviteReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.JobApplicationService.CheckIdExist(db, &model.JobApplication{}, req.JobApplicationId)
			})

		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		var alertMsg []string
		alerter := xapp.NewCK(c)
		alerter.
			Run(func() (bool, i18n.Message, error) { // 檢查是否可以撤回邀請
				return services.JobService.CheckCanWithdrawInvite(db, req.FacilityId, req.JobId, req.JobApplicationId)
			})
		alertMsg, err = alerter.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(alertMsg) > 0 {
			nc.AlertResponse(alertMsg)
			return
		}

		tx := db.Begin()
		err = services.JobService.WithdrawInvite(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Job
// @Summary 修改班次分配方式
// @Description 修改班次分配方式
// @Router /v1/facility/jobs/actions/update-shift-allocation [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.JobUpdateShiftAllocationReq true "parameter"
// @Success 200 "Success"
func (con JobController) JobUpdateShiftAllocation(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobUpdateShiftAllocationReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.JobService.CheckIdExist(db, &model.Job{}, req.JobId, req.FacilityId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.JobService.CheckCanEditShiftAllocation(db, req.FacilityId, req.JobId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.JobService.JobUpdateShiftAllocation(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}
