#!/bin/sh

# 默認的 Go 命令
DEFAULT_GO_CMD=""
GO_CMD=""

local_go_path=".githooks/.go-path.local"

# 檢查本地配置文件是否存在
if [ -f "$local_go_path" ]; then
    # 讀取配置文件中的 Go 路徑，並去除可能存在的空白字符
    CUSTOM_GO_CMD=$(cat "$local_go_path" | tr -d '[:space:]')
    if [ -n "$CUSTOM_GO_CMD" ]; then
        # 檢查自定義路徑是否可執行
        if [ -x "$CUSTOM_GO_CMD" ]; then
            GO_CMD="$CUSTOM_GO_CMD"
            echo "使用自定義 Go 路徑：$GO_CMD"
        else
            echo "自定義 Go 路徑 ($CUSTOM_GO_CMD) 不存在或不可執行，請檢查是否正確配置。"
            exit 1
        fi
    fi
fi

# 如果GO_CMD為空，則報錯
if [ -z "$GO_CMD" ]; then
    echo "請先配置 .githooks/.go-path.local 文件"
    exit 1
fi

echo "開始執行推送前檢查..."

# 先檢查 go 版本
echo "正在檢查 Go 版本..."
GO_VERSION=$("$GO_CMD" version)
if [ $? -ne 0 ]; then
    echo "Go 版本檢查失敗，請檢查 Go 是否正確安裝或環境變量是否正確。"
    exit 1
fi
echo "Go 版本檢查成功，版本號：$GO_VERSION"

# ------------------------------ 檢查代碼 ------------------------------

# 校驗代碼是否可成功編譯
echo "正在檢查代碼編譯..."
"$GO_CMD" build ./...
if [ $? -ne 0 ]; then
    echo "編譯失敗，請修復錯誤後再推送。"
    exit 1
fi

echo "所有檢查通過，正在推送..."
