package system_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type FacilityProfileController struct {
	v1.CommonController
}

func NewFacilityProfileController() FacilityProfileController {
	return FacilityProfileController{}
}

// @Tags Facility Profile
// @Summary 獲取機構資料列表
// @Description
// @Router /v1/system/facility-profiles [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FacilityProfileListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Param sortingKey query string false "name:機構名稱,approvedTime:審核時間,applicationTime:申請時間"
// @Param sortingType query string false "a=順序,d=倒序"
// @Success 200 {object} []services.FacilityProfileListResp "Success"
func (con FacilityProfileController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityProfileListReq
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	var sortSet xresp.SortingSet
	_ = c.ShouldBindQuery(&sortSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.FacilityProfileService.List(db, req, &pageSet, sortSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility Profile
// @Summary 查询機構資料
// @Description
// @Router /v1/system/facility-profiles/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FacilityProfileInquireReq true "parameter"
// @Success 200 {object} services.FacilityProfileInquireResp "Success"
func (con FacilityProfileController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityProfileInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		resp, err := services.FacilityProfileService.Inquire(db, req)
		if xgorm.IsNotFoundErr(err) {
			nc.BadRequestResponse(err)
			return
		}
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility Profile
// @Summary 審核機構資料
// @Description
// @Router /v1/system/facility-profiles/actions/approve [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FacilityProfileApproveReq true "parameter"
// @Success 200 "Success"
func (con FacilityProfileController) Approve(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityProfileApproveReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var facilityProfile model.FacilityProfile
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityProfileService.CheckIdExist(db, &facilityProfile, req.FacilityProfileId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityProfileService.CheckCanApprove(facilityProfile)
			})
		var alertMsg []string
		alertMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(alertMsg) > 0 {
			nc.AlertResponse(alertMsg)
			return
		}

		tx := db.Begin()
		err = services.FacilityProfileService.Approve(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()

		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility Profile
// @Summary 停用/啟用機構資料
// @Description 停用/啟用機構資料
// @Router /v1/system/facility-profiles/actions/deactivate [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FacilityProfileDeactivateReq true "parameter"
// @Success 200 "Success"
func (con FacilityProfileController) Deactivate(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityProfileDeactivateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var facilityProfile model.FacilityProfile
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityProfileService.CheckIdExist(db, &facilityProfile, req.FacilityProfileId)
			})
		var alertMsg []string
		alertMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(alertMsg) > 0 {
			nc.AlertResponse(alertMsg)
			return
		}

		tx := db.Begin()
		err = services.FacilityProfileService.Deactivate(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()

		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}
