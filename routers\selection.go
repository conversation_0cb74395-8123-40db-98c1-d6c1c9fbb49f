package routers

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/gin-gonic/gin"
)

func selectionPublicRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/public/").Use(handlers...)
	{
		r.GET("/selections", v1.NewSelectionController().ListByPublic)
	}
}

func selectionAppRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/app/").Use(handlers...)
	{
		r.GET("/selections", v1.NewSelectionController().ListByApp)
	}
}

func selectionProfessionalRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/professional/").Use(handlers...)
	{
		r.GET("/selections", v1.NewSelectionController().ListByProfessional)
	}
}

func selectionFacilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility/").Use(handlers...)
	{
		r.GET("/selections", v1.NewSelectionController().ListByFacility)
	}
}

func selectionSystemRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/system/").Use(handlers...)
	{
		r.GET("/selections", v1.NewSelectionController().ListBySystem)
	}
}
