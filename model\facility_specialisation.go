package model

import (
	"github.com/Norray/xrocket/xmodel"
)

// 機構專業
type FacilitySpecialisation struct {
	Id         uint64 `json:"id" gorm:"primary_key"`                                                 // Id
	FacilityId uint64 `json:"facilityId" gorm:"index:facility_position_idx;not null"`                // 機構Id
	Position   string `json:"position" gorm:"index:facility_position_idx;type:varchar(32);not null"` // 職位/崗位
	Code       string `json:"code" gorm:"index:code_idx;not null"`                                   // 專業代碼
	xmodel.Model
}

func (FacilitySpecialisation) TableName() string {
	return "facility_specialisation"
}

func (FacilitySpecialisation) SwaggerDescription() string {
	return "機構專業"
}
