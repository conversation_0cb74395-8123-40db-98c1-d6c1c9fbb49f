#!/bin/bash

echo "--------------------------------------------"
echo "Git Hooks Configuration"
echo "--------------------------------------------"

read -p "Do you want to enable git hooks? (Y/N): " choice

case "${choice:0:1}" in
    y|Y )
        echo "Enabling git hooks..."
        git config core.hooksPath .githooks
        git config --global core.autocrlf input
        if [ $? -eq 0 ]; then
            echo "Git hooks successfully enabled and path set to '.githooks'."
            echo "core.autocrlf has been set to input."
        else
            echo "Failed to enable git hooks."
            exit 1
        fi
        ;;
    n|N )
        echo "Disabling git hooks..."
        git config --unset core.hooksPath
        if [ $? -eq 0 ]; then
            echo "Git hooks successfully disabled."
        else
            echo "Failed to disable git hooks."
            exit 1
        fi
        ;;
    * )
        echo "Invalid choice. Please enter Y or N."
        exit 1
        ;;
esac
