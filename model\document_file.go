package model

import (
	"github.com/Norray/xrocket/xmodel"
)

const (
	DocumentFileCodeWages = "WAGES" // 薪金的文件
	DocumentFileCodeOther = "OTHER" // 報銷的文件
)

var DocumentFileCodes = []string{
	DocumentFileCodeWages,
	DocumentFileCodeOther,
}

// DocumentFile 單據相關文件
type DocumentFile struct {
	Id             uint64 `json:"id" gorm:"primary_key"`
	FacilityId     uint64 `json:"facilityId" gorm:"index:facility_idx;not null"`                 // 機構ID
	UserId         uint64 `json:"userId" gorm:"index:user_idx;not null"`                         // 專業人員ID
	FileCode       string `json:"fileCode" gorm:"type:varchar(255);index:fileCode_idx;not null"` // 文件代碼
	Mode           string `json:"mode" gorm:"type:varchar(255);not null"`                        // 在OSS中的私有還是公開
	Bucket         string `json:"bucket" gorm:"type:varchar(255);not null"`                      // Bucket
	Path           string `json:"path" gorm:"type:varchar(255);not null"`                        // Bucket下的路徑
	Uuid           string `json:"uuid" gorm:"type:varchar(255);index:uuid_idx;not null"`         // 唯一文件名
	OriginFileName string `json:"originFileName" gorm:"type:varchar(255);not null"`              // 原文件名
	FileName       string `json:"fileName" gorm:"type:varchar(255);not null"`                    // 唯一文件名
	FileType       string `json:"fileType" gorm:"type:varchar(255);not null"`                    // 文件類型
	FileSize       uint32 `json:"fileSize" gorm:"not null"`
	xmodel.Model
}

func (DocumentFile) TableName() string {
	return "document_file"
}

func (DocumentFile) SwaggerDescription() string {
	return "單據相關文件"
}
