package system_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type SystemAgreementController struct {
	v1.CommonController
}

func NewSystemAgreementController() SystemAgreementController {
	return SystemAgreementController{}
}

// @Tags Agreement
// @Summary 新增協議
// @Description
// @Router /v1/system/agreements/actions/create [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.AgreementCreateReq true "parameter"
// @Success 200 {object} services.AgreementCreateResp "Success"
func (con SystemAgreementController) Create(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.AgreementCreateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		tx := db.Begin()
		resp, err := services.AgreementService.Create(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Agreement
// @Summary 獲取協議列表
// @Description
// @Router /v1/system/agreements [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.AgreementListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Success 200 {object} []services.AgreementListResp "Success"
func (con SystemAgreementController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.AgreementListReq
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.AgreementService.List(db, req, &pageSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Agreement
// @Summary 搜索協議
// @Description
// @Router /v1/system/agreements/actions/search [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.AgreementSearchReq true "parameter"
// @Success 200 {object} []services.AgreementSearchResp "Success"
func (con SystemAgreementController) Search(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.AgreementSearchReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		resp, err := services.AgreementService.Search(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Agreement
// @Summary 修改協議
// @Description
// @Router /v1/system/agreements/actions/edit [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.AgreementEditReq true "parameter"
// @Success 200 "Success"
func (con SystemAgreementController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.AgreementEditReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		var checkMsg []string
		var agreement model.Agreement
		checker := xapp.NewCK(c, true)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.AgreementService.CheckIdExist(db, &agreement, req.AgreementId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		var alertMsg []string
		alertMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(alertMsg) > 0 {
			nc.AlertResponse(alertMsg)
			return
		}
		tx := db.Begin()
		err = services.AgreementService.Edit(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Agreement
// @Summary 查询協議
// @Description
// @Router /v1/system/agreements/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.AgreementInquireReq true "parameter"
// @Success 200 {object} services.AgreementInquireResp "Success"
func (con SystemAgreementController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.AgreementInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.AgreementService.Inquire(db, req)
		if xgorm.IsNotFoundErr(err) {
			nc.BadRequestResponse(err)
			return
		}
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Agreement
// @Summary 删除協議
// @Description
// @Router /v1/system/agreements/actions/delete [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.AgreementDeleteReq true "parameter"
// @Success 200 "Success"
func (con SystemAgreementController) Delete(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.AgreementDeleteReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		var checkMsg []string
		var agreement model.Agreement
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.AgreementService.CheckIdExist(db, &agreement, req.AgreementId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		tx := db.Begin()
		err = services.AgreementService.Delete(tx, req, agreement)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}
