package model

import (
	"github.com/Norray/xrocket/xmodel"
)

// 工作文件
type JobFile struct {
	Id             uint64 `json:"id" gorm:"primary_key"`                                  // 主鍵
	FacilityId     uint64 `json:"facilityId" gorm:"index:facility_idx;not null"`          // 所屬機構Id
	FacilityFileId uint64 `json:"facilityFileId" gorm:"index:facility_file_idx;not null"` // 機構文件Id
	JobId          uint64 `json:"jobId" gorm:"index:job_idx;not null"`                    // 工作Id
	xmodel.Model
}

func (JobFile) TableName() string {
	return "job_file"
}

func (JobFile) SwaggerDescription() string {
	return "工作文件"
}
