package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/Norray/medic-crew/api/v1/professional_api"
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func professionalConfirmationNoteRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	router := Router.Group("/v1/professional").Use(handlers...)
	{
		router.GET("/confirmation-notes", professional_api.NewProfessionalConfirmationNoteController().ProfessionalList)
		router.GET("/confirmation-notes/actions/inquire", professional_api.NewProfessionalConfirmationNoteController().Inquire)
		router.POST("/confirmation-notes/actions/create", professional_api.NewProfessionalConfirmationNoteController().Create)
		router.POST("/confirmation-notes/actions/update", professional_api.NewProfessionalConfirmationNoteController().Update)
		router.POST("/confirmation-notes/actions/submit", professional_api.NewProfessionalConfirmationNoteController().Submit)
		router.POST("/confirmation-notes/actions/cancel", professional_api.NewProfessionalConfirmationNoteController().Cancel)
		router.GET("/confirmation-notes/actions/job-list", professional_api.NewProfessionalConfirmationNoteController().JobList)
		router.GET("/confirmation-notes/actions/job-shift-time-list", professional_api.NewProfessionalConfirmationNoteController().JobShiftTimeList)
	}
}

func facilityConfirmationNoteRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	router := Router.Group("/v1/facility").Use(handlers...)
	{
		router.GET("/confirmation-notes", facility_api.NewConfirmationNoteController().List)
		router.GET("/confirmation-notes/actions/inquire", facility_api.NewConfirmationNoteController().Inquire)
		router.POST("/confirmation-notes/actions/review", facility_api.NewConfirmationNoteController().Review)
	}
}

func systemConfirmationNoteRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	router := Router.Group("/v1/system").Use(handlers...)
	{
		router.GET("/confirmation-notes/actions/inquire", system_api.NewConfirmationNoteController().Inquire)
	}
}
