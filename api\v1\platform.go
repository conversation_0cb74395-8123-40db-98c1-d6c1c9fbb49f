package v1

import (
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
)

type PlatformController struct{}

func NewPlatformController() PlatformController {
	return PlatformController{}
}

// @Tags Platform
// @Summary Platform Profile
// @Description
// @Router /v1/app/platform/profile [GET]
// @Produce  json
// @Security ApiKeyAuth
// @Success 200 {object} services.PlatformProfile "平台資料"
func (con PlatformController) PlatformProfile(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	db := xgorm.DB.WithContext(c)

	if nc.GetJWTUserType() == model.UserUserTypeProfessional {
		nc.NoPermissionResponse(resource.ForbiddenMsg)
		return
	}

	resp, err := services.CommonSettingService.GetPlatformProfile(db)
	if err != nil {
		nc.ErrorResponse(nil, err)
		return
	}
	nc.OKResponse(resp)
}
