package professional_api

import (
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type ProfessionalTrainingController struct{}

func NewProfessionalTrainingController() ProfessionalTrainingController {
	return ProfessionalTrainingController{}
}

// @Tags Training
// @Summary 獲取培訓模塊列表
// @Description 獲取所有有效的培訓模塊列表，包含訪問權限和完成狀態
// @Router /v1/professional/training-modules [GET]
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} []services.TrainingModuleListResp "Success"
func (con ProfessionalTrainingController) ModuleList(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.TrainingModuleListReq
	if err := c.ShouldBind<PERSON>uery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.UserId = nc.GetJWTUserId()
		resp, err := services.TrainingService.ModuleForProfessional(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Training
// @Summary 獲取培訓模塊詳情
// @Description 獲取指定培訓模塊的詳細信息，包含視頻和問題
// @Router /v1/professional/training-modules/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param moduleId query uint64 true "培訓模塊ID"
// @Success 200 {object} services.TrainingModuleDetailResp "Success"
func (con ProfessionalTrainingController) ModuleInquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.TrainingModuleDetailReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.UserId = nc.GetJWTUserId()

		// 檢查訪問權限
		checker := xapp.NewCK(c)
		checker.Run(func() (bool, i18n.Message, error) {
			return services.TrainingService.CanAccessModule(db, req.UserId, req.ModuleId)
		})
		if nc.BadRequestResponseIfCheckerFailed(checker, req) {
			return
		}

		resp, err := services.TrainingService.ModuleDetail(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Training
// @Summary 獲取培訓模塊問題列表
// @Description 獲取指定培訓模塊的問題列表及用戶已回答的答案
// @Router /v1/professional/training-modules/actions/questions [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param moduleId query uint64 true "培訓模塊ID"
// @Success 200 {object} services.TrainingModuleQuestionsResp "Success"
func (con ProfessionalTrainingController) ModuleQuestions(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.TrainingModuleQuestionsReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.UserId = nc.GetJWTUserId()

		// 檢查訪問權限
		checker := xapp.NewCK(c)
		checker.Run(func() (bool, i18n.Message, error) {
			return services.TrainingService.CanAccessModule(db, req.UserId, req.ModuleId)
		})
		if nc.BadRequestResponseIfCheckerFailed(checker, req) {
			return
		}

		// 檢查視頻觀看狀態，獲取 CanAnswer 信息
		videoWatched, _, err := services.TrainingService.CheckVideoWatched(db, req.UserId, req.ModuleId)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		resp, err := services.TrainingService.ModuleQuestions(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		// 根據視頻觀看狀態過濾問題內容
		if !videoWatched {
			resp.Questions = nil // 未觀看視頻不返回問題內容，前端可根據此判斷是否可答題
		}

		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Training
// @Summary 批量回答培訓問題
// @Description 一次性提交指定培訓模塊的所有答案
// @Router /v1/professional/training-modules/actions/answer [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.TrainingAnswerQuestionReq true "批量答案提交請求"
// @Success 200 {object} services.TrainingAnswerQuestionResp "Success"
func (con ProfessionalTrainingController) Answer(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.TrainingAnswerQuestionReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.UserId = nc.GetJWTUserId()

		// 檢查訪問權限、視頻觀看狀態、答案完整性和模塊完成狀態
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.TrainingService.CanAccessModule(db, req.UserId, req.ModuleId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.TrainingService.CheckVideoWatched(db, req.UserId, req.ModuleId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.TrainingService.CheckAllQuestionsAnswered(db, req.ModuleId, req.Answers)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.TrainingService.CheckModuleNotCompleted(db, req.UserId, req.ModuleId)
			})
		if nc.BadRequestResponseIfCheckerFailed(checker, req) {
			return
		}
		tx := db.Begin()
		resp, err := services.TrainingService.Answer(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Training
// @Summary 標記視頻觀看完成
// @Description 標記用戶已觀看完指定培訓模塊的視頻
// @Router /v1/professional/training-modules/actions/mark-video-watched [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.TrainingMarkVideoWatchedReq true "parameter"
// @Success 200 "Success"
func (con ProfessionalTrainingController) MarkVideoWatched(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.TrainingMarkVideoWatchedReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.UserId = nc.GetJWTUserId()

		// 檢查訪問權限
		checker := xapp.NewCK(c)
		checker.Run(func() (bool, i18n.Message, error) {
			return services.TrainingService.CanAccessModule(db, req.UserId, req.ModuleId)
		})
		if nc.BadRequestResponseIfCheckerFailed(checker, req) {
			return
		}
		tx := db.Begin()
		err = services.TrainingService.VideoWatched(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Training
// @Summary 獲取用戶培訓進度
// @Description 獲取用戶在指定培訓模塊的學習進度
// @Router /v1/professional/training-modules/actions/progress [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param moduleId query uint64 true "培訓模塊ID"
// @Success 200 {object} services.TrainingUserProgressResp "Success"
func (con ProfessionalTrainingController) ModuleProgress(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.TrainingUserProgressReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.UserId = nc.GetJWTUserId()

		// 檢查訪問權限
		checker := xapp.NewCK(c)
		checker.Run(func() (bool, i18n.Message, error) {
			return services.TrainingService.CanAccessModule(db, req.UserId, req.ModuleId)
		})
		if nc.BadRequestResponseIfCheckerFailed(checker, req) {
			return
		}

		resp, err := services.TrainingService.UserProgress(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
