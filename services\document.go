package services

import (
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xmodel"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var DocumentService = new(documentService)

type documentService struct{}

func (s *documentService) CheckIdExistByUserId(db *gorm.DB, m *model.Document, id uint64, userId uint64, userTypes ...string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.document.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	var err error

	if err = db.Where("id = ?", id).First(&m).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	}

	var user xmodel.User
	if err = db.Model(&user).Where("id = ?", userId).First(&user).Error; err != nil {
		return false, msg, err
	}

	if len(userTypes) > 0 {
		found := false
		for _, userType := range userTypes {
			if user.UserType == userType {
				found = true
				break
			}
		}
		if !found {
			return false, msg, nil
		}
	}

	switch user.UserType {
	case model.UserUserTypeFacilityUser:
		if m.Progress != model.DocumentProgressConfirm && m.Progress != model.DocumentProgressSent {
			return false, msg, nil
		}
		// 除了確認單，機構只能看到To它的單據
		if m.Category != model.DocumentCategoryConfirmation && m.DataType == model.DocumentDataTypeProfessionalToSystem {
			return false, msg, nil
		}
		var facilityUser model.FacilityUser
		if err = db.Where("facility_id = ?", m.FacilityId).Where("user_id = ?", userId).First(&facilityUser).Error; xgorm.IsSqlErr(err) {
			return false, msg, err
		}
		if xgorm.IsNotFoundErr(err) {
			return false, msg, nil
		} else {
			return true, i18n.Message{}, nil
		}
	case model.UserUserTypeProfessional:
		if m.DataType != model.DocumentDataTypeProfessionalToFacility && m.DataType != model.DocumentDataTypeProfessionalToSystem {
			return false, msg, nil
		}
		if m.UserId != userId {
			return false, msg, nil
		} else {
			return true, i18n.Message{}, nil
		}
	case model.UserUserTypeSuperAdmin, model.UserUserTypeSystemAdmin:
		return true, i18n.Message{}, nil
	}
	return false, msg, nil
}

func (s *documentService) CheckIdExist(db *gorm.DB, m *model.Document, id uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.document.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	var err error

	if err = db.Where("id = ?", id).First(&m).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	}

	return true, i18n.Message{}, nil
}

func (s *documentService) LockDocuments(db *gorm.DB, documentIds []uint64) error {
	// 鎖定行
	if err := db.Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id IN (?)", documentIds).
		First(&model.Document{}).Error; err != nil {
		return err
	}
	return nil
}
