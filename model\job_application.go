package model

import (
	"time"

	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xmodel/xtype"
	"github.com/shopspring/decimal"
)

// JobApplication 狀態常量
const (
	JobApplicationStatusApply              = "APPLY"               // 已申請 - 應聘者提交申請
	JobApplicationStatusWithdraw           = "WITHDRAW"            // 已撤銷 - 應聘者撤銷申請
	JobApplicationStatusChatting           = "CHATTING"            // 聊天中 - 機構發起聊天
	JobApplicationStatusInvite             = "INVITE"              // 已邀請 - 機構發出邀請
	JobApplicationStatusDecline            = "DECLINE"             // 已婉拒 - 應聘者拒絕邀請
	JobApplicationStatusApplicationCancel  = "APPLICATION_CANCEL"  // 申請已取消 - 在ACCEPT之前機構取消這個工作
	JobApplicationStatusAccept             = "ACCEPT"              // 已接受 - 應聘者接受邀請
	JobApplicationStatusFacilityCancel     = "FACILITY_CANCEL"     // 機構已取消 - 機構取消
	JobApplicationStatusProfessionalCancel = "PROFESSIONAL_CANCEL" // 應聘者已取消 - 應聘者取消

	JobApplicationAcceptY = "Y" // 取錄
	JobApplicationAcceptN = "N" // 未取錄

	JobApplicationDeletedY = "Y" // 已刪除

	JobApplicationInvoiceGeneratedY = "Y" // 已生成發票
	JobApplicationInvoiceGeneratedN = "N" // 未生成發票
)

// JobApplication 應聘者申請記錄
type JobApplication struct {
	Id             uint64               `json:"id" gorm:"primary_key"`                            // 主鍵
	ApplicationNo  xtype.JsonNullString `json:"applicationNo" gorm:"type:varchar(64);unique"`     // 申請編號,確認了才有,剛申請時為null
	FacilityId     uint64               `json:"facilityId" gorm:"index:facility_idx;not null"`    // 機構Id
	JobId          uint64               `json:"jobId" gorm:"index:job_idx;not null"`              // 工作Id
	ProfessionalId uint64               `json:"professionalId" gorm:"index:prof_idx;not null"`    // 應聘專業人員Id
	UserId         uint64               `json:"userId" gorm:"index:user_idx;not null"`            // 應聘者userId
	Status         string               `json:"status" gorm:"type:varchar(32);not null"`          // 申請狀態 APPLY, WITHDRAW, CHATTING, INVITE, REJECT, DECLINE, ACCEPT, UPCOMING, APPLICATION_CANCEL FACILITY_CANCEL, PROFESSIONAL_CANCEL, COMPLETE, BILLING, WAITING_FOR_PAYMENT, BILL_REJECT, FACILITY_PAID, PLATFORM_PAID, CONFIRM
	Deleted        string               `json:"deleted" gorm:"index:deleted_idx;type:varchar(1)"` // 是否刪除, Y 已刪除
	Accept         string               `json:"accept" gorm:"index:accept_idx;type:varchar(1)"`   // 取錄狀態 Y 取錄 N 未取錄
	Score          int32                `json:"score" gorm:"not null"`                            // 匹配分數
	CalendarNote   string               `json:"calendarNote" gorm:"type:text"`                    // 日曆備註
	// 基礎時間記錄
	ApplyTime        time.Time       `json:"applyTime" gorm:"type:datetime(0);not null"`                                   // 申請時間
	ChattingTime     *time.Time      `swaggertype:"string" json:"chattingTime" gorm:"type:datetime(0)"`                    // 開始聊天時間
	InviteTime       *time.Time      `swaggertype:"string" json:"inviteTime" gorm:"type:datetime(0)"`                      // 邀請時間
	InviterUserId    uint64          `json:"inviterUserId" gorm:"index:inviter_user_idx"`                                  // 邀請人Id
	DeclineTime      *time.Time      `swaggertype:"string" json:"declineTime" gorm:"type:datetime(0)"`                     // 婉拒時間
	WithdrawTime     *time.Time      `swaggertype:"string" json:"withdrawTime" gorm:"type:datetime(0)"`                    // 撤銷時間
	AcceptTime       *time.Time      `swaggertype:"string" json:"acceptTime" gorm:"type:datetime(0)"`                      // 接受時間
	CancelTime       *time.Time      `swaggertype:"string" json:"cancelTime" gorm:"type:datetime(0)"`                      // 取消時間
	CancelReason     string          `json:"cancelReason" gorm:"type:text"`                                                // 取消原因
	CancelUserId     uint64          `json:"cancelUserId" gorm:"index:cancel_user_idx"`                                    // 取消操作人Id
	CompleteTime     *time.Time      `swaggertype:"string" json:"completeTime" gorm:"type:datetime(0)"`                    // 完成時間
	CommissionRate   decimal.Decimal `json:"commissionRate" gorm:"type:decimal(10,2)"`                                     // 本次勞務關係的佣金比率
	InvoiceGenerated string          `json:"invoiceGenerated" gorm:"type:varchar(1);index:invoice_generated_idx;not null"` // 系統是否已生成代付發票 Y/N
	InvoiceTime      *time.Time      `swaggertype:"string" json:"invoiceTime" gorm:"type:datetime(0)"`                     // 生成發票時間
	xmodel.Model
}

func (JobApplication) TableName() string {
	return "job_application"
}

func (JobApplication) SwaggerDescription() string {
	return "應聘者申請記錄"
}
