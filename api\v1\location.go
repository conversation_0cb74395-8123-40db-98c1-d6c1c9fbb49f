package v1

import (
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
)

type LocationController struct {
	CommonController
}

func NewLocationController() LocationController {
	return LocationController{}
}

// @Tags Location
// @Summary 搜索行政區域
// @Description
// @Router /v1/app/locations/actions/search [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.LocationSearchReq true "parameter"
// @Success 200 {object} []services.LocationSearchResp "Success"
func (con LocationController) Search(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.LocationSearchReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.LocationService.Search(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Location
// @Summary 搜索國家
// @Description 專門用於搜索國家級別的位置數據
// @Router /v1/app/locations/actions/search-country [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.LocationSearchCountryReq true "parameter"
// @Success 200 {object} []services.LocationSearchCountryResp "Success"
func (con LocationController) SearchCountry(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.LocationSearchCountryReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.LocationService.SearchCountry(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
