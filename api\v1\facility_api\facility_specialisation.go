package facility_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type FacilitySpecialisationController struct {
	v1.CommonController
}

func NewFacilitySpecialisationController() FacilitySpecialisationController {
	return FacilitySpecialisationController{}
}

// @Tags Facility Specialisation
// @Summary 獲取機構專業細分樹狀結構
// @Description 獲取專業細分選項的樹狀結構，如果提供 facilityId 則標記已選擇的專業細分
// @Router /v1/facility/facility-specialisations/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FacilitySpecialisationInquireReq true "parameter"
// @Success 200 {object} map[string][]services.FacilitySpecialisationTreeNode "Success"
func (con FacilitySpecialisationController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilitySpecialisationInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		// 檢查權限
		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		// 獲取專業細分樹狀結構
		resp, err := services.FacilitySpecialisationService.GetSpecialitiesTree(db, req.FacilityId, req.SelectedOnly)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility Specialisation
// @Summary 更新機構專業細分
// @Description 根據前端提交的四個職位的專業代碼更新機構專業細分
// @Router /v1/facility/facility-specialisations/actions/edit [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FacilitySpecialisationEditReq true "parameter"
// @Success 200 "Success"
func (con FacilitySpecialisationController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilitySpecialisationEditReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		// 檢查權限
		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		// 參數驗證
		var checkMsg []string
		checker := xapp.NewCK(c)
		// 驗證醫師代碼
		if len(req.MedicalPractitionerCodes) > 0 {
			checker.Run(func() (bool, i18n.Message, error) {
				return services.FacilitySpecialisationService.CheckCodesValidByPosition(db, model.JobPositionProfessionMedicalPractitioner, req.MedicalPractitionerCodes)
			})
		}
		// 驗證登記護士代碼
		if len(req.EnrolledNurseCodes) > 0 {
			checker.Run(func() (bool, i18n.Message, error) {
				return services.FacilitySpecialisationService.CheckCodesValidByPosition(db, model.JobPositionProfessionEnrolledNurse, req.EnrolledNurseCodes)
			})
		}
		// 驗證註冊護士代碼
		if len(req.RegisteredNurseCodes) > 0 {
			checker.Run(func() (bool, i18n.Message, error) {
				return services.FacilitySpecialisationService.CheckCodesValidByPosition(db, model.JobPositionProfessionRegisteredNurse, req.RegisteredNurseCodes)
			})
		}
		// 驗證個人護理員代碼
		if len(req.PersonalCareWorkerCodes) > 0 {
			checker.Run(func() (bool, i18n.Message, error) {
				return services.FacilitySpecialisationService.CheckCodesValidByPosition(db, model.JobPositionProfessionPersonalCareWorker, req.PersonalCareWorkerCodes)
			})
		}
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		// 開始事務
		tx := db.Begin()
		err = services.FacilitySpecialisationService.UpdateSpecialisations(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}
