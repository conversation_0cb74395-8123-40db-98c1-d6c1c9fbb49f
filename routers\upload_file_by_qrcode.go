package routers

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/gin-gonic/gin"
)

func uploadFileByQrcodeAppRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/app/").Use(handlers...)
	{
		r.POST("/files/actions/gen-upload-qrcode", v1.NewUploadFileByQrcodeController().GenUploadQrcode)
		r.GET("/files/actions/inquire-by-qrcode", v1.NewUploadFileByQrcodeController().InquireByQrcode)
	}
}

func uploadFileByQrcodePublicRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/public/").Use(handlers...)
	{
		r.POST("/files/actions/exchange-by-qrcode", v1.NewUploadFileByQrcodeController().ExchangeQrcode)
	}
}
