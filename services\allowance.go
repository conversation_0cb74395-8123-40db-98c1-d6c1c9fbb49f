package services

import (
	"fmt"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

var AllowanceService = new(allowanceService)

type allowanceService struct{}

func (s *allowanceService) CheckIdExist(db *gorm.DB, m *model.Allowance, id uint64, facilityId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.allowance.id.does_not_exist",
		Other: "No such record, please try after reloading.",
	}
	var err error
	if err = db.Where("id = ? AND facility_id = ?", id, facilityId).First(&m).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

type AllowanceCreateReq struct {
	FacilityId             uint64          `json:"facilityId" binding:"required"`                       // 機構ID
	Name                   string          `json:"name" binding:"required,max=255"`                     // 津貼名稱
	Description            string          `json:"description" binding:"required,max=255"`              // 津貼描述
	Status                 string          `json:"status" binding:"required,oneof=ENABLE DISABLE"`      // 津貼狀態 （ENABLE：啟用，DISABLE：停用）
	AttractsSuperannuation string          `json:"attractsSuperannuation" binding:"required,oneof=Y N"` // 津貼是否吸引公務員公積 （Y：是，N：否）
	PerHourAmount          decimal.Decimal `json:"perHourAmount" binding:"required"`                    // 津貼每小時金額
	PerShiftAmount         decimal.Decimal `json:"perShiftAmount" binding:"required"`                   // 津貼每班金額
	PerJobAmount           decimal.Decimal `json:"perJobAmount" binding:"required"`                     // 津貼每職位金額
}

type AllowanceCreateResp struct {
	AllowanceId uint64 `json:"allowanceId"`
}

func (s *allowanceService) Create(db *gorm.DB, req AllowanceCreateReq) (AllowanceCreateResp, error) {
	var resp AllowanceCreateResp
	var err error
	var m model.Allowance
	_ = copier.Copy(&m, req)
	if err = db.Create(&m).Error; err != nil {
		return resp, err
	}
	resp.AllowanceId = m.Id
	return resp, nil
}

type AllowanceListReq struct {
	FacilityId uint64 `form:"facilityId" binding:"required"`                   // 機構ID
	Name       string `form:"name" binding:"max=255"`                          // 津貼名稱 (模糊查詢)
	Status     string `form:"status" binding:"omitempty,oneof=ENABLE DISABLE"` // 津貼狀態 （ENABLE：啟用，DISABLE：停用）
}

type AllowanceListResp struct {
	AllowanceId            uint64          `json:"allowanceId"`            // 津貼ID
	FacilityId             uint64          `json:"facilityId"`             // 機構ID
	Name                   string          `json:"name"`                   // 津貼名稱
	Description            string          `json:"description"`            // 津貼描述
	Status                 string          `json:"status"`                 // 津貼狀態 （ENABLE：啟用，DISABLE：停用）
	AttractsSuperannuation string          `json:"attractsSuperannuation"` // 津貼是否吸引公務員公積 （Y：是，N：否）
	PerHourAmount          decimal.Decimal `json:"perHourAmount"`          // 津貼每小時金額
	PerShiftAmount         decimal.Decimal `json:"perShiftAmount"`         // 津貼每班金額
	PerJobAmount           decimal.Decimal `json:"perJobAmount"`           // 津貼每職位金額
}

func (s *allowanceService) List(db *gorm.DB, req AllowanceListReq, pageSet *xresp.PageSet) ([]AllowanceListResp, error) {
	var err error
	var resp []AllowanceListResp
	builder := db.Table("allowance AS fa").Select([]string{
		"fa.id AS allowance_id",
		"fa.facility_id",
		"fa.name",
		"fa.description",
		"fa.status",
		"fa.attracts_superannuation",
		"fa.per_hour_amount",
		"fa.per_shift_amount",
		"fa.per_job_amount",
	}).Where("fa.facility_id = ?", req.FacilityId)

	if req.Name != "" {
		builder = builder.Where("fa.name LIKE ?", xgorm.EscapeLikeWithWildcards(req.Name))
	}
	if req.Status != "" {
		builder = builder.Where("fa.status = ?", req.Status)
	}
	if err = builder.Scopes(xresp.Paginate(pageSet)).Order("fa.id").Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

type AllowanceSearchReq struct {
	FacilityId uint64 `form:"facilityId" binding:"required"`                  // 機構ID
	Name       string `form:"name" binding:"max=255"`                         // 津貼名稱 (模糊查詢)
	Status     string `form:"status" binding:"required,oneof=ENABLE DISABLE"` // 津貼狀態 （ENABLE：啟用，DISABLE：停用）
	SelectedId uint64 `form:"selectedId"`                                     // 選擇的津貼ID
	Limit      int    `form:"limit" binding:"omitempty,min=1,max=100"`        // 限制數量
}

type AllowanceSearchResp struct {
	AllowanceId            uint64          `json:"allowanceId"`            // 津貼ID
	FacilityId             uint64          `json:"facilityId"`             // 機構ID
	Name                   string          `json:"name"`                   // 津貼名稱
	Description            string          `json:"description"`            // 津貼描述
	Status                 string          `json:"status"`                 // 津貼狀態 （ENABLE：啟用，DISABLE：停用）
	AttractsSuperannuation string          `json:"attractsSuperannuation"` // 津貼是否吸引公務員公積 （Y：是，N：否）
	PerHourAmount          decimal.Decimal `json:"perHourAmount"`          // 津貼每小時金額
	PerShiftAmount         decimal.Decimal `json:"perShiftAmount"`         // 津貼每班金額
	PerJobAmount           decimal.Decimal `json:"perJobAmount"`           // 津貼每職位金額
}

func (s *allowanceService) Search(db *gorm.DB, req AllowanceSearchReq) ([]AllowanceSearchResp, error) {
	var err error
	var resp []AllowanceSearchResp
	builder := db.Table("allowance AS fa").Select([]string{
		"fa.id AS allowance_id",
		"fa.facility_id",
		"fa.name",
		"fa.description",
		"fa.status",
		"fa.attracts_superannuation",
		"fa.per_hour_amount",
		"fa.per_shift_amount",
		"fa.per_job_amount",
	}).Where("fa.facility_id = ?", req.FacilityId)

	if req.Name != "" {
		builder = builder.Where("fa.name LIKE ?", xgorm.EscapeLikeWithWildcards(req.Name))
	}
	if req.Status != "" {
		builder = builder.Where("fa.status = ?", req.Status)
	}
	if req.SelectedId != 0 {
		builder = builder.Order(fmt.Sprintf("IF(fa.id = %d,0,1)", req.SelectedId))
	}
	if req.Limit > 0 {
		builder = builder.Limit(req.Limit)
	}
	if err = builder.Order("fa.id").Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

type AllowanceEditReq struct {
	AllowanceId            uint64          `json:"allowanceId" binding:"required"`                      // 津貼ID
	FacilityId             uint64          `json:"facilityId" binding:"required"`                       // 機構ID
	Name                   string          `json:"name" binding:"required,max=255"`                     // 津貼名稱
	Description            string          `json:"description" binding:"required,max=255"`              // 津貼描述
	Status                 string          `json:"status" binding:"required,oneof=ENABLE DISABLE"`      // 津貼狀態 （ENABLE：啟用，DISABLE：停用）
	AttractsSuperannuation string          `json:"attractsSuperannuation" binding:"required,oneof=Y N"` // 津貼是否吸引公務員公積 （Y：是，N：否）
	PerHourAmount          decimal.Decimal `json:"perHourAmount" binding:"required"`                    // 津貼每小時金額
	PerShiftAmount         decimal.Decimal `json:"perShiftAmount" binding:"required"`                   // 津貼每班金額
	PerJobAmount           decimal.Decimal `json:"perJobAmount" binding:"required"`                     // 津貼每職位金額
}

func (s *allowanceService) Edit(db *gorm.DB, req AllowanceEditReq) error {
	var err error
	var m model.Allowance
	if err = db.Where("id = ? AND facility_id = ?", req.AllowanceId, req.FacilityId).First(&m).Error; err != nil {
		return err
	}
	_ = copier.Copy(&m, req)
	if err = db.Save(&m).Error; err != nil {
		return err
	}
	return nil
}

type AllowanceInquireReq struct {
	AllowanceId uint64 `form:"allowanceId" binding:"required"` // 津貼ID
	FacilityId  uint64 `form:"facilityId" binding:"required"`  // 機構ID
}

type AllowanceInquireResp struct {
	AllowanceId            uint64          `json:"allowanceId"`            // 津貼ID
	FacilityId             uint64          `json:"facilityId"`             // 機構ID
	Name                   string          `json:"name"`                   // 津貼名稱
	Description            string          `json:"description"`            // 津貼描述
	Status                 string          `json:"status"`                 // 津貼狀態 （ENABLE：啟用，DISABLE：停用）
	AttractsSuperannuation string          `json:"attractsSuperannuation"` // 津貼是否吸引公務員公積 （Y：是，N：否）
	PerHourAmount          decimal.Decimal `json:"perHourAmount"`          // 津貼每小時金額
	PerShiftAmount         decimal.Decimal `json:"perShiftAmount"`         // 津貼每班金額
	PerJobAmount           decimal.Decimal `json:"perJobAmount"`           // 津貼每職位金額
}

func (s *allowanceService) Inquire(db *gorm.DB, req AllowanceInquireReq) (AllowanceInquireResp, error) {
	var err error
	var resp AllowanceInquireResp
	var m model.Allowance
	if err = db.Where("id = ? AND facility_id = ?", req.AllowanceId, req.FacilityId).First(&m).Error; err != nil {
		return resp, err
	}
	_ = copier.Copy(&resp, m)
	resp.AllowanceId = m.Id
	return resp, nil
}

type AllowanceDeleteReq struct {
	AllowanceId uint64 `json:"allowanceId" binding:"required"` // 津貼ID
	FacilityId  uint64 `json:"facilityId" binding:"required"`  // 機構ID
}

func (s *allowanceService) Delete(db *gorm.DB, req AllowanceDeleteReq) error {
	var err error
	if err = db.Where("id = ? AND facility_id = ?", req.AllowanceId, req.FacilityId).Delete(&model.Allowance{}).Error; err != nil {
		return err
	}
	return nil
}

// 初始化機構的默認津貼數據
func (s *allowanceService) Init(db *gorm.DB, facilityId uint64) error {
	// 定義默認津貼數據
	defaultAllowances := []model.Allowance{
		{
			FacilityId:             facilityId,
			Name:                   "Remote Area Incentive",
			Description:            "Incentive for working in a rural or remote area",
			Status:                 model.AllowanceStatusEnable,
			AttractsSuperannuation: model.AllowanceAttractsSuperannuationY,
		},
		{
			FacilityId:             facilityId,
			Name:                   "In-Charge Allowance",
			Description:            "For managing/administering medications",
			Status:                 model.AllowanceStatusEnable,
			AttractsSuperannuation: model.AllowanceAttractsSuperannuationY,
		},
		{
			FacilityId:             facilityId,
			Name:                   "Medication Handling Allowance",
			Description:            "For managing/administering medications",
			Status:                 model.AllowanceStatusEnable,
			AttractsSuperannuation: model.AllowanceAttractsSuperannuationY,
		},
		{
			FacilityId:             facilityId,
			Name:                   "Travel Allowance",
			Description:            "Covers kms, public transport, flights",
			Status:                 model.AllowanceStatusEnable,
			AttractsSuperannuation: model.AllowanceAttractsSuperannuationN,
		},
		{
			FacilityId:             facilityId,
			Name:                   "Accommodation Allowance",
			Description:            "Covers hotel or housing costs",
			Status:                 model.AllowanceStatusEnable,
			AttractsSuperannuation: model.AllowanceAttractsSuperannuationN,
		},
		{
			FacilityId:             facilityId,
			Name:                   "Meal Allowance",
			Description:            "Covers cost of meals while on duty or travelling",
			Status:                 model.AllowanceStatusEnable,
			AttractsSuperannuation: model.AllowanceAttractsSuperannuationN,
		},
		{
			FacilityId:             facilityId,
			Name:                   "Parking Allowance",
			Description:            "Covers paid parking near the facility",
			Status:                 model.AllowanceStatusEnable,
			AttractsSuperannuation: model.AllowanceAttractsSuperannuationN,
		},
		{
			FacilityId:             facilityId,
			Name:                   "Remote Living Support",
			Description:            "Cost-of-living support in remote areas",
			Status:                 model.AllowanceStatusEnable,
			AttractsSuperannuation: model.AllowanceAttractsSuperannuationN,
		},
		{
			FacilityId:             facilityId,
			Name:                   "Uniform / Clothing Allowance",
			Description:            "Covers uniforms or PPE",
			Status:                 model.AllowanceStatusEnable,
			AttractsSuperannuation: model.AllowanceAttractsSuperannuationN,
		},
	}

	// 批量創建默認津貼
	if err := db.CreateInBatches(&defaultAllowances, 20).Error; err != nil {
		return err
	}

	return nil
}
