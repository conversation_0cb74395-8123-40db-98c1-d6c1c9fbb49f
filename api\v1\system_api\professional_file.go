package system_api

import (
	"errors"
	"fmt"
	"net/http"

	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	"github.com/gin-gonic/gin"
)

type ProfessionalFileController struct {
	v1.CommonController
}

func NewProfessionalFileController() ProfessionalFileController {
	return ProfessionalFileController{}
}

// @Tags Professional File
// @Summary 獲取專業人士文件圖片
// @Description 直接獲取專業人士文件的圖片內容，可以在瀏覽器中直接顯示
// @Router /v1/system/professional-files/actions/preview [GET]
// @Security ApiKeyAuth
// @Param professionalFileId query uint64 false "專業人士文件Id"
// @Param professionalFileUuid query string false "專業人士文件uuid"
// @Param thumb query string true "是否獲取縮略圖 Y=縮略圖 N=原圖"
// @Success 200 "Success"
func (con ProfessionalFileController) Preview(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalFileGetPreviewByIdOrUuidReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if req.ProfessionalFileUuid == "" && req.ProfessionalFileId == 0 {
			nc.BadRequestResponse(errors.New("id or uuid required"))
			return
		}

		// 獲取圖片數據
		resp, err := services.ProfessionalFileService.PreviewByIdOrUuid(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		c.Writer.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename*=utf-8''%s", xtool.ReplacePlus(resp.Filename)))
		c.Data(http.StatusOK, services.FacilityFileService.GetFileMimeType(resp.Filename), resp.FileBytes)

	} else {
		nc.BadRequestResponse(err)
	}
}
