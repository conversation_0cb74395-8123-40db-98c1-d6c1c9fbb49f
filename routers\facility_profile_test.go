package routers

import (
	"net/http"
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
	"github.com/shopspring/decimal"
)

func TestFacilityProfileInit(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/facility-profiles/actions/init",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "初始化",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityProfileInitReq{
					FacilityProfileId: 1,
					FacilityType:      "FACILITY_PROFILE_FACILITY_TYPE_HOSPITAL",
					FacilityTypeName:  "",
					Address:           "Location Demo Data",
					LocationState:     "Demo State",
					LocationCity:      "Demo City",
					LocationRoute:     "",
					LocationLat:       decimal.Zero,
					LocationLng:       decimal.Zero,
					Abn:               "***********",
					ContactFirstName:  "ContactFirstName Demo Data",
					ContactLastName:   "ContactLastName Demo Data",
					ContactRole:       "CEO",
					ContactPhone:      "123456789",
					ContactEmail:      "<EMAIL>",
					ExpertiseRequired: "MEDICAL_PRACTITIONER",
					PaymentTerms:      "PAY_IN_ARREARS",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityFacilityProfileInquire(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/facility-profiles/actions/inquire",
		UserId:           11,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityProfileInquireReq{
					FacilityProfileId: 2,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityProfileEdit(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/facility-profiles/actions/edit",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "修改",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityProfileEditReq{
					FacilityProfileId:       1,
					FacilityType:            "FACILITY_PROFILE_FACILITY_TYPE_OTHER",
					FacilityTypeName:        "老人院",
					Name:                    "飛躍老人院",
					Address:                 "科創中心",
					LocationLat:             decimal.Zero,
					LocationLng:             decimal.Zero,
					Abn:                     "***********",
					ContactFirstName:        "大文",
					ContactLastName:         "陳",
					ContactRole:             "CEO",
					ContactPhone:            "88888888",
					ContactEmail:            "<EMAIL>",
					ExpertiseRequired:       "MEDICAL_PRACTITIONER",
					PaymentTerms:            "PAY_IN_ARREARS",
					FacilityAgreementId:     1,
					OrientationConfirmation: model.FacilityProfileOrientationConfirmationY,
					Files:                   []uint64{1},
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityProfileSubmit(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/facility-profiles/actions/submit",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "提交",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrHttpCode: http.StatusOK,
				ExpectErrRespCode: xresp.AlertMsg,
				Params: services.FacilityProfileSubmitReq{
					FacilityProfileId: 1,
					Submit:            "Y",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

//  ---------------------------------------------------  系統端  ---------------------------------------------------

func TestSystemFacilityProfileList(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/facility-profiles",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityProfileListReq{
					DataType:    model.ProfessionalDataTypeDraft,
					Status:      model.ProfessionalStatusPending,
					Deactivated: model.FacilityDeactivatedY,
				},
				PageSet: xresp.PageSet{
					PageSize:  10,
					PageIndex: 1,
				},
				SortingSet: xresp.SortingSet{
					SortingKey:  "name",
					SortingType: "a",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSystemFacilityProfileInquire(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/facility-profiles/actions/inquire",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityProfileInquireReq{
					FacilityProfileId: 1,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSystemFacilityProfileApprove(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/facility-profiles/actions/approve",
		UserId:           22,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "審批",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityProfileApproveReq{
					FacilityProfileId: 1,
					Approve:           "Y",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSystemFacilityProfileDeactivate(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/system/facility-profiles/actions/deactivate",
		UserId:           22,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "審批",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityProfileDeactivateReq{
					FacilityProfileId: 12,
					Deactivate:        model.FacilityDeactivatedY,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
