package model

import (
	"github.com/Norray/xrocket/xmodel"
)

const (
	LocationStatusEnable  = "ENABLE"  // 啟用
	LocationStatusDisable = "DISABLE" // 停用

	LocationLevelCountry = "COUNTRY" // 國家級別
	LocationLevelState   = "STATE"   // 州/領地級別
	LocationLevelCity    = "CITY"    // 城市/地區級別
)

// 行政區域
type Location struct {
	Id       uint64 `json:"id" gorm:"primary_key"`
	Country  string `json:"country" gorm:"index:country_idx;not null"`                // 國家/地區
	ParentId uint64 `json:"parentId" gorm:"index:parent_id_idx;not null"`             // 父級ID，0表示頂級
	Level    string `json:"level" gorm:"type:varchar(32);index:level_idx;not null"`   // 級別 COUNTRY=國家/地區,STATE=州/領地,CITY=城市/地區
	Location string `json:"name" gorm:"type:varchar(255);not null"`                   // 地區名稱
	Status   string `json:"status" gorm:"type:varchar(32);index:status_idx;not null"` // 狀態
	xmodel.Model
}

func (Location) TableName() string {
	return "location"
}

func (Location) SwaggerDescription() string {
	return "行政區域"
}
