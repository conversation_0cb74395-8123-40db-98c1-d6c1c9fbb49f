package services

import (
	"bytes"
	"fmt"
	"io"
	"os"
	"strings"

	"github.com/SebastiaanKlippert/go-wkhtmltopdf"
)

// HTMLToPDFConverter 是HTML到PDF轉換的結構體
type HTMLToPDFConverter struct {
	pdfg *wkhtmltopdf.PDFGenerator
}

// NewHtmlConverter 創建一個新的HTML到PDF轉換器
func NewHtmlConverter() (*HTMLToPDFConverter, error) {
	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		return nil, err
	}

	pdfg.Dpi.Set(300)
	pdfg.Orientation.Set(wkhtmltopdf.OrientationPortrait)
	pdfg.Grayscale.Set(false)

	return &HTMLToPDFConverter{
		pdfg: pdfg,
	}, nil
}

// FromString 從HTML字符串創建PDF
func (c *HTMLToPDFConverter) FromString(html string) error {
	html = c.ensureUTF8Meta(html)
	c.pdfg.AddPage(wkhtmltopdf.NewPageReader(bytes.NewReader([]byte(html))))
	return c.pdfg.Create()
}

// FromFile 從HTML文件創建PDF
func (c *HTMLToPDFConverter) FromFile(htmlFile string) error {
	f, err := os.Open(htmlFile)
	if err != nil {
		return err
	}
	defer f.Close()

	c.pdfg.AddPage(wkhtmltopdf.NewPageReader(f))
	return c.pdfg.Create()
}

// SaveToFile 將生成的PDF保存到文件
func (c *HTMLToPDFConverter) SaveToFile(outputPath string) error {
	return c.pdfg.WriteFile(outputPath)
}

// GetPDFBytes 獲取生成的PDF字節
func (c *HTMLToPDFConverter) GetPDFBytes() ([]byte, error) {
	return c.pdfg.Bytes(), nil
}

// WriteTo 將PDF寫入到io.Writer
func (c *HTMLToPDFConverter) WriteTo(w io.Writer) (int64, error) {
	b := c.pdfg.Bytes()
	n, err := w.Write(b)
	return int64(n), err
}

func (c *HTMLToPDFConverter) ensureUTF8Meta(html string) string {
	// 讀取CSS文件
	coreCss, err := os.ReadFile("resource/pdf/quill.core.css")
	if err != nil {
		// 如果讀取失敗，繼續處理但不添加樣式
		fmt.Println("Warning: Failed to read quill.core.css:", err)
	}

	// 構建樣式標籤
	cssStyles := fmt.Sprintf("<style>\n%s\n</style>", coreCss)

	// 添加UTF-8 meta標籤和CSS樣式
	if strings.Contains(html, "<meta charset=") {
		// 如果已有meta標籤，只添加樣式
		if strings.Contains(html, "</head>") {
			return strings.Replace(html, "</head>", cssStyles+"</head>", 1)
		}
		return html
	}

	if strings.Contains(html, "<head>") {
		// 添加meta標籤和樣式
		return strings.Replace(html, "<head>", "<head><meta charset=\"UTF-8\">"+cssStyles, 1)
	}

	// 若完全沒有head，加上完整head
	return fmt.Sprintf("<html><head><meta charset=\"UTF-8\">%s</head><body>%s</body></html>", cssStyles, html)
}
