package professional_api

import (
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
)

type ProfessionalDashboardController struct{}

func NewProfessionalDashboardController() ProfessionalDashboardController {
	return ProfessionalDashboardController{}
}

// @Tags Professional Dashboard
// @Summary 專業人員收入統計
// @Description 獲取專業人員在指定時間範圍內的收入統計數據
// @Router /v1/professional/dashboard/income [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.ProfessionalIncomeRequest true "parameter"
// @Success 200 {object} []services.PeriodIncomeItem "Success"
func (con ProfessionalDashboardController) Income(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalIncomeRequest
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		// 設置當前用戶ID
		req.UserId = nc.GetJWTUserId()
		resp, err := services.ProfessionalDashboardService.ProfessionalIncome(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Dashboard
// @Summary 專業人員工作時間統計
// @Description 獲取專業人員在指定時間範圍內的工作時間統計數據
// @Router /v1/professional/dashboard/working-hours [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.WorkingHoursRequest true "parameter"
// @Success 200 {object} []services.DailyHoursItem "Success"
func (con ProfessionalDashboardController) WorkingHours(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.WorkingHoursRequest
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		// 設置當前用戶ID
		req.UserId = nc.GetJWTUserId()
		resp, err := services.ProfessionalDashboardService.WorkingHoursAnalytics(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Dashboard
// @Summary 專業人員工作時間統計
// @Description 獲取專業人員在指定時間範圍內的工作時間統計數據
// @Router /v1/professional/dashboard/summary [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.DashboardSummaryRequest true "parameter"
// @Success 200 {object} services.DashboardSummaryResponse "Success"
func (con ProfessionalDashboardController) Summary(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.DashboardSummaryRequest
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		// 設置當前用戶ID
		req.UserId = nc.GetJWTUserId()
		resp, err := services.ProfessionalDashboardService.DashboardSummary(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
