package v1

import (
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
)

type ServiceFacilityController struct{}

func NewServiceFacilityController() ServiceFacilityController {
	return ServiceFacilityController{}
}

// @Tags ServiceFacility
// @Summary 搜索服務機構
// @Description
// @Router /v1/app/service-facilities/actions/search [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.ServiceFacilitySearchReq true "parameter"
// @Success 200 {object} []services.ServiceFacilitySearchResp "Success"
func (con ServiceFacilityController) Search(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ServiceFacilitySearchReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.ServiceFacilityService.Search(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
