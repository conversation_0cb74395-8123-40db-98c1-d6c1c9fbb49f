package routers

import (
	"github.com/Norray/medic-crew/api/v1/professional_api"
	"github.com/gin-gonic/gin"
)

func professionalJobAvailabilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/professional").Use(handlers...)
	{
		controller := professional_api.NewProfessionalJobAvailabilityController()
		r.GET("/professional-job-availabilities/actions/inquire", controller.Inquire) // 查詢專業人士工作可用性
		r.POST("/professional-job-availabilities/actions/update", controller.Update)  // 更新專業人士工作可用性
	}
}
