package v1

import (
	"errors"
	"fmt"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xredis"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gorm.io/gorm"
)

const (
	AuthenticationRegisterRecordExpired      = 20003 // 註冊記錄已過期
	AuthenticationRegisterVerifyCodeExpired  = 20004 // 註冊驗證碼已過期
	AuthenticationRegisterCodeSendCountLimit = 20005 // 註冊驗證碼發送次數限制
	AuthenticationEmailAlreadyRegistered     = 20006 // 電郵已註冊
)

type CommonController struct{}

type ReqCommonCheck struct {
	FacilityId          uint64
	FacilityProfileId   uint64
	FacilityFileId      uint64
	FacilityPrimaryUser string // Y N
}

func (con CommonController) CheckCanAccess(nc xapp.NGinCtx, db *gorm.DB, req ReqCommonCheck) bool {
	var facilityProfile model.FacilityProfile
	var facilityId uint64
	if req.FacilityProfileId > 0 {
		if err := db.First(&facilityProfile, req.FacilityProfileId).Error; xgorm.IsSqlErr(err) {
			return false
		}
		if facilityProfile.Id == 0 {
			return false
		}
		facilityId = facilityProfile.FacilityId
	}
	if req.FacilityId > 0 {
		if facilityProfile.FacilityId > 0 && facilityProfile.FacilityId != req.FacilityId {
			return false
		}
		facilityId = req.FacilityId
	}
	if req.FacilityFileId > 0 {
		var facilityFile model.FacilityFile
		if err := db.First(&facilityFile, req.FacilityFileId).Error; xgorm.IsSqlErr(err) {
			return false
		}
		if facilityFile.Id == 0 {
			return false
		}
		facilityId = facilityFile.FacilityId
	}
	if facilityId > 0 {
		var facilityUser model.FacilityUser
		builder := db.Where("user_id = ?", nc.GetJWTUserId()).Where("facility_id = ?", facilityId)
		if req.FacilityPrimaryUser != "" {
			builder = builder.Where("primary_user = ?", req.FacilityPrimaryUser)
		}
		if err := builder.First(&facilityUser).Error; xgorm.IsSqlErr(err) {
			return false
		}
		if facilityUser.Id == 0 {
			return false
		}
		return true
	}
	return false
}

const (
	cacheKeyUserFacilityId     = "cache:facility_id:user:%d"
	cacheKeyUserProfessionalId = "cache:professional_id:user:%d"
)

func (con CommonController) GetUserFacilityId(nc xapp.NGinCtx, db *gorm.DB) (uint64, error) {
	cacheKey := fmt.Sprintf(cacheKeyUserFacilityId, nc.GetJWTUserId())
	var facilityId uint64
	exist, err := xredis.GetStruct(nc.C, cacheKey, &facilityId)
	if err != nil {
		return 0, err
	}
	if exist {
		return facilityId, nil
	}
	var facilityUser model.FacilityUser
	if err = db.Where("user_id = ?", nc.GetJWTUserId()).First(&facilityUser).Error; err != nil {
		return 0, err
	}
	if err = xredis.SetStruct(nc.C, cacheKey, facilityUser.FacilityId, time.Minute); err != nil {
		return 0, err
	}
	return facilityUser.FacilityId, nil
}

func (con CommonController) GetUserDraftProfessionalId(nc xapp.NGinCtx, db *gorm.DB) (uint64, error) {
	cacheKey := fmt.Sprintf(cacheKeyUserProfessionalId, nc.GetJWTUserId())
	var professionalId uint64
	exist, err := xredis.GetStruct(nc.C, cacheKey, &professionalId)
	if err != nil {
		return 0, err
	}
	if exist {
		return professionalId, nil
	}
	var professional model.Professional
	if err := db.Where("user_id = ?", nc.GetJWTUserId()).
		Where("data_type = ?", model.ProfessionalDataTypeDraft).
		First(&professional).Error; xgorm.IsSqlErr(err) {
		return 0, err
	}
	if professional.Id == 0 {
		return 0, errors.New("user is not a professional")
	}
	if err := xredis.SetStruct(nc.C, cacheKey, professional.Id, 60*60*24); err != nil {
		return 0, err
	}
	return professional.Id, nil
}

// 檢查單一選項是否存在
func (con CommonController) CheckSelectionExist(checker *xapp.XChecker, db *gorm.DB, selectionType string, selectionValue string) {
	checker.
		Run(func() (bool, i18n.Message, error) {
			if selectionValue == "" {
				return true, i18n.Message{}, nil
			}
			return services.SelectionService.CheckSelectionExist(db, &model.Selection{}, selectionType, selectionValue, model.SelectionStatusEnable)
		})
}

// 檢查多個選項是否存在
func (con CommonController) CheckSelectionsExist(checker *xapp.XChecker, db *gorm.DB, selectionType string, selectionValues string) {
	checker.
		Run(func() (bool, i18n.Message, error) {
			if selectionValues == "" {
				return true, i18n.Message{}, nil
			}
			return services.SelectionService.CheckSelectionsExist(db, selectionType, selectionValues, model.SelectionStatusEnable)
		})
}
