package routers

import (
	"encoding/json"
	"fmt"
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

func TestLogin(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:        programPath + "/v1/login",
		Method:     xtest.Post,
		ParamsType: xtest.Body,
		Name:       "用戶登入",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.LoginReq{
					Username: "<EMAIL>",
					Password: frontendPassword123,
					Dk:       "2342438e-c1e9-429a-8b23-7471c32ea630",
					System:   "USER",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestForgetUserPassword(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/auth/actions/forget-user-password",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "忘記密碼",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常發送",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.AuthForgetPasswordReq{
					Email: "<EMAIL>",
				},
			},
			{
				SubName:           "無效的郵箱格式",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.AuthForgetPasswordReq{
					Email: "invalid-email",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestResetPassword(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/auth/actions/reset-password",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "重置密碼",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常重置",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.AuthResetPasswordReq{
					Uuid:     "5b48f4d9-41bf-4391-8d4d-df4211b854b4",
					Password: frontendPassword123,
				},
			},
			{
				SubName:           "無效的 UUID",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.AuthResetPasswordReq{
					Uuid:     "invalid-uuid",
					Password: frontendPassword123,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestResetPasswordUuidCheck(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/auth/actions/reset-password-check",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "檢查重置密碼 UUID",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常檢查",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.AuthCheckResetPasswordUuidReq{
					Uuid: "5b48f4d9-41bf-4391-8d4d-df4211b854b4",
				},
			},
			{
				SubName:           "無效的 UUID",
				ExpectErrRespCode: xresp.ResetPasswordVerifyFailed,
				Params: services.AuthCheckResetPasswordUuidReq{
					Uuid: "invalid-uuid",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestGoogleOAuth(t *testing.T) {
	test := xtest.Test{
		Url:        programPath + "/v1/google-oauth",
		Method:     xtest.Post,
		ParamsType: xtest.Body,
		Name:       "Google 授權登入",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常檢查",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.GoogleOAuthReq{
					Scene:          "REGISTER",
					Purpose:        "POST_JOB",
					RecaptchaToken: "TEST",
				},
				CheckResultHandler: func(resp interface{}) bool {
					response := resp.(xresp.Response)
					var data services.GoogleOAuthResp
					dataByte, _ := json.Marshal(response.Data)
					_ = json.Unmarshal(dataByte, &data)
					fmt.Println(data.URL)
					return true
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
