package routers

import (
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func commissionSystemRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/system").Use(handlers...)
	{
		r.POST("/commissions/actions/create", system_api.NewCommissionController().Create)
		r.GET("/commissions", system_api.NewCommissionController().List)
		r.GET("/commissions/actions/search", system_api.NewCommissionController().Search)
		r.POST("/commissions/actions/edit", system_api.NewCommissionController().Edit)
		r.GET("/commissions/actions/inquire", system_api.NewCommissionController().Inquire)
		r.POST("/commissions/actions/delete", system_api.NewCommissionController().Delete)
	}
}
