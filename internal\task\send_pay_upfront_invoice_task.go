package task

import (
	"context"
	"encoding/json"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xgorm"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
)

const SendPayUpfrontInvoiceTask = "send_pay_upfront_invoice_task" // rabbitmq

type SendPayUpfrontInvoiceReq struct {
	DocumentId uint64 `json:"documentId"` // 發票Id
}

// 執行隊列 - 發送預付發票
func SendPayUpfrontInvoice(taskJson string) (context.Context, error) {
	var err error
	ctx := context.Background()
	traceId := uuid.NewV4().String()
	ctx = context.WithValue(ctx, "traceId", traceId)

	logger := log.WithField("traceId", traceId).WithField("task", SendPayUpfrontInvoiceTask)

	taskData := SendPayUpfrontInvoiceReq{}
	err = json.Unmarshal([]byte(taskJson), &taskData)
	if err != nil {
		logger.Errorf("fail to unmarshal task: %v, taskJson: %s", err, taskJson)
		return ctx, err
	}
	logger = logger.WithField("DocumentId", taskData.DocumentId)
	logger.Info("Start to send pay upfront invoice")

	// 獲取數據庫連接
	db := xgorm.DB.WithContext(ctx)

	// 獲取發票
	var document model.Document
	if err = db.Model(&model.Document{}).
		Where("id = ?", taskData.DocumentId).
		Where("progress = ?", model.DocumentProgressDraft).
		First(&document).Error; err != nil {
		logger.Errorf("fail to get document: %v", err)
		return ctx, err
	}
	if document.Id == 0 {
		logger.Info("document not found")
		return ctx, nil
	}

	// 開啟事務
	tx := db.Begin()

	// 發送預付發票電郵給機構
	err = services.InvoiceService.SendPayUpfrontInvoice(tx, services.SendPayUpfrontInvoiceReq{
		DocumentId: taskData.DocumentId,
	})
	if err != nil {
		tx.Rollback()
		logger.Errorf("fail to send pay upfront invoice: %v", err)
		return ctx, err
	}

	// 提交事務
	if err = tx.Commit().Error; err != nil {
		logger.Errorf("fail to commit transaction: %v", err)
		return ctx, err
	}

	logger.Info("send pay upfront invoice task completed")
	return ctx, nil
}
