package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/Norray/medic-crew/api/v1/professional_api"
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func professionalFileRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/professional").Use(handlers...)
	{
		controller := professional_api.NewProfessionalFileController()
		r.POST("/professional-files/actions/upload-file", controller.UploadFile)
		r.GET("/professional-files/actions/preview", controller.Preview)
	}
}

func professionalFileSystemRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/system").Use(handlers...)
	{
		controller := system_api.NewProfessionalFileController()
		r.GET("/professional-files/actions/preview", controller.Preview)
	}
}

func professionalFileFacilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		controller := facility_api.NewProfessionalFileController()
		r.GET("/professional-files/actions/preview", controller.Preview)
	}
}
