package routers

import (
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

// 測試查詢專業人士工作可用性
func TestProfessionalJobAvailabilityInquire(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/professional/professional-job-availabilities/actions/inquire",
		UserId:           8, // 使用專業人士用戶ID
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢專業人士工作可用性",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
			},
		},
	}
	xtest.RunTests(t, test)
}

// 測試更新專業人士工作可用性
func TestProfessionalJobAvailabilityUpdate(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/professional/professional-job-availabilities/actions/update",
		UserId:           8, // 使用專業人士用戶ID
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "更新專業人士工作可用性",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常更新",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ProfessionalJobAvailabilityUpdateReq{
					Settings: []services.ProfessionalJobAvailabilityUpdateSettingReq{
						{
							FilterType: model.ProfessionalJobAvailabilityFilterTypeAvailableDate,
							BeginDate:  "2025-05-01",
							EndDate:    "2025-05-09",
						},
						{
							FilterType: model.ProfessionalJobAvailabilityFilterTypeUnavailableDate,
							BeginDate:  "2025-05-20",
							EndDate:    "2025-05-30",
						},
						{
							FilterType: model.ProfessionalJobAvailabilityFilterTypeAvailableWeekday,
							Value:      "1,2,3",
						},
					},
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
