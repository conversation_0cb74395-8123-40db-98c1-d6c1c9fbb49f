package professional_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type FaqFileController struct {
	v1.CommonController
}

func NewFaqFileController() FaqFileController {
	return FaqFileController{}
}

// @Tags Faq File
// @Summary 獲取常見問題文件圖片預覽URL
// @Description 獲取常見問題文件的圖片預覽URL
// @Router /v1/professional/faq-files/actions/preview [GET]
// @Security ApiKeyAuth
// @Param json query services.FaqFilePreviewUrlReq true "parameter"
// @Success 200 {object} services.FaqFilePreviewUrlResp "Success"
func (con FaqFileController) Preview(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FaqFilePreviewUrlReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FaqFileService.CheckUuidExist(db, &model.FaqFile{}, req.FaqFileUuid)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		resp, err := services.FaqFileService.GenPreviewUrlByUuid(db, req.FaqFileUuid)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
