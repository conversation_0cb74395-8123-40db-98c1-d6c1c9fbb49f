package system_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
)

type UserDeviceController struct {
	v1.CommonController
}

func NewUserDeviceController() UserDeviceController {
	return UserDeviceController{}
}

// @Tags User Device
// @Summary 獲取用戶設備列表
// @Description
// @Router /v1/system/user-devices [GET]
// @Produce  json
// @Security ApiKeyAuth
// @Param json query services.UserDevicesReq true "parameter"
// @Success 200 {object} []services.UserDevicesResp "Success"
func (con UserDeviceController) UserDevices(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.UserDevicesReq
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.UserDeviceService.List(db, req, &pageSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags User Device
// @Summary 用戶設備更改狀態
// @Description
// @Router /v1/system/user-devices/actions/change-status [POST]
// @Produce  json
// @Security ApiKeyAuth
// @Param json body services.ChangeDeviceStatusReq true "parameter"
// @Success 200 "Success"
func (con UserDeviceController) ChangeStatus(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ChangeDeviceStatusReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		err = services.UserDeviceService.ChangeStatus(nc, db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(nil)

	} else {
		nc.BadRequestResponse(err)
	}
}
