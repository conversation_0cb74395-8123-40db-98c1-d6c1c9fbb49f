package model

import (
	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xmodel/xtype"
	"github.com/shopspring/decimal"
)

const (
	// WAGES
	DocumentItemTypeWages           = "WAGES"            // 薪金
	DocumentItemTypeAdditionalWages = "ADDITIONAL_WAGES" // 額外薪金
	// Allowance
	DocumentItemTypeAllowance = "ALLOWANCE" // 津貼
	// Other
	DocumentItemTypeTravelReimbursement = "TRAVEL_REIMBURSEMENT" // 差旅費
	DocumentItemTypeMeals               = "MEALS"                // 餐費
	DocumentItemTypeAccommodation       = "ACCOMMODATION"        // 住宿費
	// Super Amount
	DocumentItemTypeSuperAmount = "SUPER_AMOUNT" // 退休金供款金额
	// Commission Amount
	DocumentItemTypeCommissionAmount = "COMMISSION_AMOUNT" // 佣金金额
	// Cancellation Fee
	DocumentItemTypeCancellationFee = "CANCELLATION_FEE" // 機構取消賠償金
	// 自定義
	DocumentItemTypeCustom = "CUSTOM" // 自定義

	DocumentItemAllowanceTypeHourly = "HOURLY" // 小時津貼
	DocumentItemAllowanceTypeShift  = "SHIFT"  // 班次津貼
	DocumentItemAllowanceTypeJob    = "JOB"    // 職位津貼
)

var DocumentFileCodeMapToItemType = map[string][]string{
	DocumentFileCodeWages: {DocumentItemTypeWages, DocumentItemTypeAdditionalWages},
	DocumentFileCodeOther: {DocumentItemTypeTravelReimbursement, DocumentItemTypeMeals, DocumentItemTypeAccommodation},
}

// DocumentItem 記錄單據涉及的項目明細
type DocumentItem struct {
	Id               uint64          `json:"id" gorm:"primary_key"`
	DocumentId       uint64          `json:"documentId" gorm:"index:document_idx;not null"`      // 關聯的單據ID
	ItemType         string          `json:"itemType" gorm:"type:varchar(255);not null"`         // 項目類型
	ItemName         string          `json:"itemName" gorm:"type:varchar(255);not null"`         // 項目名稱
	AllowanceType    string          `json:"allowanceType" gorm:"type:varchar(255);not null"`    // 津貼類型
	Seq              int32           `json:"seq" gorm:"not null"`                                // 項目編號
	JobShiftId       uint64          `json:"jobShiftId" gorm:"index:job_shift_idx;not null"`     // 工作班次ID
	ItemDate         xtype.NullDate  `swaggertype:"string" json:"itemDate" gorm:"type:date"`     // 項目相關日期
	StartTime        string          `json:"startTime" gorm:"type:varchar(5);not null"`          // 開始時間
	NextDay          string          `json:"nextDay" gorm:"type:varchar(1);not null"`            // 結束時間是否是次日
	FinishTime       string          `json:"finishTime" gorm:"type:varchar(5);not null"`         // 結束時間
	Particular       string          `json:"particular" gorm:"type:text;not null"`               // 明細
	ExpectedHours    decimal.Decimal `json:"expectedHours" gorm:"type:decimal(4,2);not null"`    // 預期工作小時數
	Hours            decimal.Decimal `json:"hours" gorm:"type:decimal(4,2);not null"`            // 工作小時數
	BreakDuration    decimal.Decimal `json:"breakDuration" gorm:"type:decimal(10,2);not null"`   // 休息時長（小時）
	HourlyRate       decimal.Decimal `json:"hourlyRate" gorm:"type:decimal(9,2);not null"`       // 時薪
	TotalAmount      decimal.Decimal `json:"totalAmount" gorm:"type:decimal(9,2);not null"`      // 項目總金額
	TaxAmount        decimal.Decimal `json:"taxAmount" gorm:"type:decimal(9,2);not null"`        // 稅額
	CommissionRate   decimal.Decimal `json:"commissionRate" gorm:"type:decimal(5,3);not null"`   // 佣金金额 比例 1% 記錄 0.01
	CommissionAmount decimal.Decimal `json:"commissionAmount" gorm:"type:decimal(9,2);not null"` // 佣金金额
	CountTax         string          `json:"countTax" gorm:"type:varchar(1);not null"`           // 是否計算稅額 Y:是 N:否
	SuperAmount      decimal.Decimal `json:"superAmount" gorm:"type:decimal(9,2);not null"`      // 退休金供款金额
	xmodel.Model
}

// TableName 返回資料庫表名
func (m *DocumentItem) TableName() string {
	return "document_item"
}
