package model

import (
	"github.com/Norray/xrocket/xmodel"
)

// 服務機構
type ServiceFacility struct {
	Id                          uint64  `json:"id" gorm:"primary_key"`
	ServiceName                 string  `json:"serviceName" gorm:"type:varchar(255);index:service_name_idx;not null"`    // 服務名稱
	PhysicalAddress             string  `json:"physicalAddress" gorm:"type:varchar(255);not null"`                       // 實體地址
	Suburb                      string  `json:"suburb" gorm:"type:varchar(128);index:suburb_idx;not null"`               // 郊區
	PhysicalState               string  `json:"physicalState" gorm:"type:varchar(16);index:physical_state_idx;not null"` // 實體州
	PostalCode                  string  `json:"postalCode" gorm:"type:varchar(16);index:postal_code_idx;not null"`       // 郵遞區號
	AgedCarePlanningRegionCode  string  `json:"agedCarePlanningRegionCode" gorm:"type:varchar(16);not null"`             // 2018年老年護理規劃區域編號
	AgedCarePlanningRegion      string  `json:"agedCarePlanningRegion" gorm:"type:varchar(128);not null"`                // 2018年老年護理規劃區域
	CareType                    string  `json:"careType" gorm:"type:varchar(64);index:care_type_idx;not null"`           // 護理類型
	ResidentialPlaces           int     `json:"residentialPlaces" gorm:"not null"`                                       // 住宿場所數量
	HomeCarePlaces              int     `json:"homeCarePlaces" gorm:"not null"`                                          // 家庭護理場所數量
	RestorativeCarePlaces       int     `json:"restorativeCarePlaces" gorm:"not null"`                                   // 康復護理場所數量
	ProviderName                string  `json:"providerName" gorm:"type:varchar(255);index:provider_name_idx;not null"`  // 提供者名稱
	OrganisationType            string  `json:"organisationType" gorm:"type:varchar(128);not null"`                      // 組織類型
	AbsRemoteness               string  `json:"absRemoteness" gorm:"type:varchar(128);not null"`                         // ABS偏遠地區
	MmmCode                     string  `json:"mmmCode" gorm:"type:varchar(16);not null"`                                // 2019年MMM編號
	Sa2Code                     string  `json:"sa2Code" gorm:"type:varchar(16);not null"`                                // 2016年SA2編號
	Sa2Name                     string  `json:"sa2Name" gorm:"type:varchar(255);not null"`                               // 2016年SA2名稱
	Sa3Code                     string  `json:"sa3Code" gorm:"type:varchar(16);not null"`                                // 2016年SA3編號
	Sa3Name                     string  `json:"sa3Name" gorm:"type:varchar(255);not null"`                               // 2016年SA3名稱
	LgaName                     string  `json:"lgaName" gorm:"type:varchar(255);not null"`                               // 2016年LGA名稱
	LgaCode                     string  `json:"lgaCode" gorm:"type:varchar(16);not null"`                                // 2016年LGA編號
	PhnCode                     string  `json:"phnCode" gorm:"type:varchar(16);not null"`                                // 2017年PHN編號
	PhnName                     string  `json:"phnName" gorm:"type:varchar(255);not null"`                               // 2017年PHN名稱
	Latitude                    float64 `json:"latitude" gorm:"type:decimal(12,8);not null"`                             // 緯度
	Longitude                   float64 `json:"longitude" gorm:"type:decimal(12,8);not null"`                            // 經度
	AustralianGovernmentFunding string  `json:"australianGovernmentFunding" gorm:"type:varchar(64);not null"`            // 2023/24財年澳大利亞政府資助
	xmodel.Model
}

func (ServiceFacility) TableName() string {
	return "service_facility"
}

func (ServiceFacility) SwaggerDescription() string {
	return "服務機構"
}
