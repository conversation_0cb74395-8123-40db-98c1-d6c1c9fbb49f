package routers

import (
	"testing"

	"github.com/Norray/xrocket/xtool"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

func TestRegisterFacility(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/register/facility",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "機構註冊",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常註冊",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.RegisterFacilityReq{
					Email:          "<EMAIL>",
					Password:       xtool.FrontendEncodeStringWithSalt("123", "medic-crew"),
					RecaptchaToken: "03AFcWeA5jhLteQC5BXVKgECe6EoQDibO61SdzNKfhwxldDP7X2KSXZaR5O0GhLxl_MxkO3i909Hrf6c3K13xVX5ObAAd9cETXH7Nx4qmaPLLqSTC3Mf7X_FPkbF2OSqQDVwAd10fEkAPSLbw6ERajp3LO2-6OaLzMQwBhc2N1bu-N_YnPc1P49-_SkYGlfvnNg4RNAS2F9ofj09s0ZD8fkph0Y4ZGT6c1OpbIMOziHZrt21wUhHk3N7rZYBhNyHXcX_ruHFoshKJfHAhLEIHtd2em0fZ2Ega3_cx5x0CK3lml8qWOnLlW56tqYMtpQxHgnA_Qpsl4gU2gWqUjejEFXX_uTcNuVnLtclBxabagD6bIxdD7Os3O8f_CqJVEFMH0UNxmCo-cd9XU-ydmsdQ48gEUw04aqeaWVOLbBia_5p4gs74YFw2rdqyg4YqHLy_d6zFBBLBBl0tzz6RUDoky70Dih_-sG1lHMd7nMyqGwj4qe7vnn55HOUfYl2LjXTiNRf02JtLnBw_2O7aMqe6kGkRgpDrHtDYRgfOnunDh8wNgu4fCTZe4euEX6EVCDbk46Y64zQV3qQfrlo8MpXGkhlN3Rs6Dk1xFVunml9f2mrctJe8DTSHEz6AVKcYeXZPFJeatIa1iUp96nC4THv2TCg0ualeiZdIaQvXXHRNSnzT3JXLUCYFxG79X5ufKKc1lh7jSn1C2Su_qEiDDwilXCoIMA99uB-vX8Pqvj5eBDTda7OJPQDMUexlnP4zv0tGumSZfCxwHecuvyw12lNCsOWe0wQ31BHsFKXhG9BMzqQhl-1WkGKW0BROZd4kfG2kvhzz5oWZ80yqBx3Dbi292WYob0YWlc5aWlXewI_CvMAeSe4AGRuvewCG47VOyIwRmVC5CTZlntnivlY9Xf3NRHSVfiY2KGNiuMk_5S6vBaHvkijPLS5cb2pkYiV_vXaezoRiq_j4rnSYYDOqokhKH_W4aPLuC7xjIqCUUx5haQCDMFW7MKBoJOxb-9WkXnjbajgBIAeobGyWIEYkt17SEzKc1N5D8cpW8nGvLHHeK1YxD6qJcaEhU6pjmeC9cBEzXfxK0iArqHiD3WEMFYW3RfDOZ_miV9vwacbzBgrwnacWzn9dKc0Hz0ki-WzPgi0xT_TEmzyyyWKyDBnZs9NPmxSL-RitaMxqmY-HrDxgdiJQMjbzpXFm-EUQV1xhTjTH4XhEEXKMmOqqORUaMpSjL85xwCK5XpfDJdd11MnzxHOvylAThMEQflpVuBE6KHNbBbQQBdq_x1GlXE0MTDcnUtYuGLJM4bgcW6gB6Dm7Hk5HXG1LGtE3TcfdYXUiiFwycaswY8wDrn0Dq5PUXBjyd2aasSooDvdVMiLjVh7KRve6ZW5zZ6wzC-mUfcS7M1zVIm7-oLmxqAYxlXcFgM1ZAPCh7Oy7iffUvl1TDMjXtVR9i2mXgPFKvrf3BTLzWzWbEJ17GneKwevuNqeoY0sxZpVmmTpga_e7iH9ICs4uQ-bwQ9PH9jwNgg-ErI5o6LoVUbNPlODbYuCnvFWLKhLnmsHUX2cjXr4PVnLPiUZ2ovoIMB_3szo7ps-KFgQlkSynGGT_nYuQK0Qg2dtzmMzwmWxpQMKuQjCEF1sqaF2WwgLreN3c7knzJrIV-HBLEMJiLFoSUHuLsARRKVUF0ylVFNAdqzQB0gtOmmNgurgmwqCycoKBkb-oyhu1npDLp1S_xNOHJkMVz4sFFllpuq27uiY4zFjVMjbqz5sNF2RhmyY_S48rLyxN2wG5bdeFYX7UMNXSV764NFZ-ZCWA8eknTw16DKbQH_wURZquAWAOZBkxBEogsGUHSFyeE2SWyupPkMIyai4V7ZSeqI-2fDVfcVGy2xtkra4UWaVbJJyAeSG5JbRYufhuozMPQ-AYcrCufOjClIgQ18KVD2lZOUYJuGK2FHR5FDCOjHOTTQGnoLzD6kHSGYMUpM6oKalxeSaxCObX9uQGx9qmoAmHsZAzpIz3EhrgLoOZKr0CW1Cdkk2UkElnA3xeW4XNGCqUB3X3iNuUVO0Scf36Artw7KUPlsfthwoKEzCwiuwgBw8swfC1vm24Pq9TVKEIVv77XZHU1FgfZfw1qRO-bQypIJaTKOjxnwl0XNJcZtoq8540ZqNI1LGEiYKK3WSqV9VzySHF3eiJTeBaHrKO8QA4yJrX3jJpPoNOIs6QnRysX4tpi_hdqrXBYNFdxVJ4JKESq6a33D3BZSqYWDpeBLztNxK59VR7VF_2k1kF8Tg",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestRegisterFacilityEmailVerify(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/register/facility-verify-email",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "機構註冊驗證電郵",
		Cases: []xtest.TestCase{
			{
				SubName:           "驗證電郵",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.RegisterVerifyFacilityEmailReq{
					EmailVerifyUuid: "6ed957f1-2532-5d15-8893-d3017d58fe81",
					EmailVerifyCode: "004359",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestRegisterProfessional(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/register/professional",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "專業人士註冊",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常註冊",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.RegisterProfessionalReq{
					Profession:     "MEDICAL_PRACTITIONER",
					Email:          "<EMAIL>",
					Password:       xtool.FrontendEncodeStringWithSalt("123", "medic-crew"),
					RecaptchaToken: "03AFcWeA6b44l-ulrLbEwOtVeQItOWCKXeD7X4qY5kdBBKGA_biBNw9yJJNBRPbuao5PpOLbIvUnpPH-Z2G0a7lIjSOhP9ErfYp4vQCYvOb14UGH6u4vy1upVv2QylyyhUoJl5zVtS8kXYmDMtNYtCmqj96XoWRx7kgz9H-gMkeg6Vs0od45B8BI-WZyhqF_evlIFyB6K_hpX9YZ9spvkM7skDjl1FVM8xTpe4ffZgojxHZR6cAt9ieaxP23zmUFbIfsAA5MBlLW2XvMxQKOd7xqAIfSdryX3xa_GdqbgALbc9Hp-Fyli7bMVN1L0wCzbRD2MnowaQU4VXaMkhbv1ucsQ9u-G2h7UoC30Pi-1HGTD7_Z491m4tNn_UbEc8pNjya_6DWnxyCPpNIsO9M4QkhdDCs0l0Vjp3lkGsSvO8EvhaIVbAQuuePMyyT_g5RjoTw_vTmnD_aHGOLisFrsvV7-fRKEzEibejZJ7yDt-AnHkmSc2d3ATibSaK__muAxTi-MDdqBDixBS5vCYtce9Gr471whPvd7MOp2Zs8U4FyD9MhBEsCbSBrcj9z2ThEaswXGjqVklTn54gTnIAZVYkpH4fX-LyYlGc_8DhbpGCRhDHy9As70eYNwS-Yu622XlsyaPXGZMBEE5dkdhxgUD-R3hEU0GpjYDDk0fyWkhYIb1Mwf3vozcrQw-7HMxs-4dB5ZuEe3dVgiG37A9wjfbNga_fDbgrhJbVaYIoHxkr5rKGgFaDSHMVT6FOgQ70Ix7Q88eePCRAg8l4v4cJVmzX_21Jj76Vc1avPOnUR1CMpBqecR54WuaSYXCOy_roDsYL4EUeR7cm7nMWOgYJP3pjTlL5va2Oco86uXGnJyihUqA0PNhOyPwnQpLl4bpRWC_Cdn0FjdeJ09ewtcP78N-OEhxsWj1FD9ov_Hz28U5pA7vH0W43F3_WARUPaNCKnqleIDa-xff1e77aZRExeHP9VPXY9nIGeN0sHlQuQUGIzO6Cb5QMLLImY95BDAioCPF_3ZkrSaE_dC_WyRqsYBpimdBETcWGJ1rLJ7ZHj34yFFHTi95AvHoBh_elHEkNybnMJDnTPO1_vTcAs_Wxt52vj-XggCY8begco1HvKD2Luabv2WrlmIIH_df-v9ViFUigsr1-C-1olAGaFLAErXrzOdlSW1jk5UxfyhwSBNtfOrRj-6GFl_95KP_s1KZqb_hsxnaAOfZDXFzoLCfNGW1YCtZyb0g0USLHHbpU6EA22ZWvr6oxMCZqbfza0IY-L3aAgBLOl5w5BaZysg-KuuvCMZGts_tXKLkINNTikeiw44xtEhTiUNm_whcRHgKvgb69a8qFx2ImeDXMHSJPq9Ay2vzj1S0MlcAZhQ_qds5yQq2caxAB4HmszYEj55HgOzlYA47SUNgVyY9dLSjS9pBNgiCyKckx1ymzqdkBSpZfVhY_gNNbRpdVz0wLpm_PiPA1hlReb8NmdEiexrmx7Jw55yZlh2ZASJmrr8RCMBfV_-ELgf8gYgl_Twv0ZjLfKXlMpIkYZ85mojmEKiM6LHi3t8ok6oK1YlnRLOS6DaTMxEcy3l-mvEP9eOhNQOqEREC8WAtSkJrUY1ogwTBlueyk6O7977yt3ieqwIjTWtt9kGUrCyTfwOxEiuSepqUfm9lkzYTY61_E67whbE2AponBJ6xDjzzqQauQm7dwducgFBO274JM1hkSWE328rmjHRW6YhamA_TvMA_woDOWXOmm9Ya163upMOphrg1oIFn0HAC1xgo6YhiccUUYSfvwS4_dYlI3Z7MLnDCv8J7zNnUYCUujyo0QCEfk4AGcazY5q97mID0ZgeRUGPzVH5ZWOO9VskYZaN7Krf0LTMlG18c3kjC9aG7hkeQEAD4pBNaiv3mLWQwPbhN6uh06rXz8DIHeHh7ucNKov5sT-W92JMu7u7rpPNSseRFPZPgVrkIhBWhMMKIdfmi1heJhYqse4l-TA1vHANQ2sdM7d8Uvf_nlVdufCCQubFdNkmB-qxMvG3kzcszR03MP-5SfzIWQWhcpaAv4hXegJsj5oMYcouRDedxUWuNlMGI4eP-xSGA9b1QdS_hbgvgCAnPqjdVGxdxQjrJb0RV4bUZh5swc1Z7MucKjotTY9irxcBY_tSbvODnPHsiyLLrUDm3fgIjPgU794d7pHT9HwkP7m64sjqrmwdyYcmFELDc2jPoEtpNDmldd08TP9_qype18tn010dGT4v7SagBlw1hNIGORHYaUBEURVQ0VY6USzg",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestRegisterProfessionalEmailVerify(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/register/professional-verify-email",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "專業人士註冊驗證電郵",
		Cases: []xtest.TestCase{
			{
				SubName:           "驗證電郵",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.RegisterVerifyProfessionalEmailReq{
					EmailVerifyUuid: "6a41f8d8-6c1c-54c4-a5dc-578917bc1996",
					EmailVerifyCode: "999713",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestResendVerificationEmail(t *testing.T) {
	test := xtest.Test{
		Url:        programPath + "/v1/register/resend-verification-email",
		Method:     xtest.Post,
		ParamsType: xtest.Body,
		Name:       "重發驗證電郵",
		Cases: []xtest.TestCase{
			{
				SubName:           "重發驗證電郵",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ResendVerificationEmailReq{
					UserType:        services.RegisterUserTypeProfessional,
					EmailVerifyUuid: "18df3dc2-0508-4815-a79d-7476f1f57b08995f83b9-cf85-4e49-98a9-d3fa9061f89a",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
